<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>

        <template id="portal_my_home_lease_contracts" name="Show Lease Contracts" inherit_id="portal.portal_my_home"
                  customize_show="True">
            <div class="o_portal_my_home" position="before">
                <t t-call="portal.portal_docs_entry">
                    <t t-set="icon" t-value="'/isky_realestate_development/static/img/rent-a-house-svgrepo-com.svg'"/>
                    <t t-set="title">Your Lease Contracts</t>
                    <t t-set="url" t-value="'/my/lease_contracts'"/>
                    <t t-set="text">View and manage your lease contracts</t>
                    <t t-set="config_card" t-value="True"/>
                    <t t-set="placeholder_count" t-value="'lease_contracts_counts'"/>
                </t>
            </div>
        </template>

        <template id="portal_lease_contract_breadcrumbs" inherit_id="portal.portal_breadcrumbs">
            <xpath expr="//ol[hasclass('o_portal_submenu')]" position="inside">
                <li t-if="page_name == 'lease_contracts_list_view'" class="breadcrumb-item">Lease Contracts</li>
                <li t-if="page_name == 'portal_lease_contract_form_view'" class="breadcrumb-item">
                    <a href="/my/lease_contracts">Contract</a>
                </li>
                <li t-if="page_name == 'portal_lease_contract_form_view'" class="breadcrumb-item">Contract Details</li>
            </xpath>
        </template>

        <template id="lease_contracts_list_view">
            <t t-call="portal.portal_layout">
                <t t-call="portal.portal_table">
                    <thead>
                        <tr>
                            <th class="text-center">Contract Reference</th>
                            <th class="text-center">Customer</th>
                            <th class="text-center">Unit</th>
                            <th class="text-center">Lease Type</th>
                            <th class="text-center">Issue Date</th>
                            <th class="text-center">Start Date</th>
                            <th class="text-center">End Date</th>
                            <th class="text-center">Status</th>
                        </tr>
                    </thead>
                    <tbody>
                        <t t-foreach="lease_contracts" t-as="c">
                            <tr>
                                <td class="text-center">
                                    <a t-attf-href="/my/lease_contracts/#{c.id}">
                                        <t t-out="c.name"/>
                                    </a>
                                </td>
                                <td class="text-center">
                                    <t t-out="c.sudo().customer_id.name"/>
                                </td>
                                <td class="text-center">
                                    <t t-out="c.unit_name"/>
                                </td>
                                <td class="text-center">
                                    <t t-out="dict(c._fields['lease_contract_type'].selection).get(c.lease_contract_type)"/>
                                </td>
                                <td class="text-center">
                                    <t t-out="c.contract_issue_date"/>
                                </td>
                                <td class="text-center">
                                    <t t-out="c.contract_start_date"/>
                                </td>
                                <td class="text-center">
                                    <t t-out="c.contract_end_date"/>
                                </td>
                                <td class="text-center">
                                    <t t-if="c.contract_status == 'draft'">Draft</t>
                                    <t t-if="c.contract_status == 'reserved'">Reserved</t>
                                    <t t-if="c.contract_status == 'approved'">Approved</t>
                                    <t t-if="c.contract_status == 'running'">Running</t>
                                    <t t-if="c.contract_status == 'cancelled'">Cancelled</t>
                                    <t t-if="c.contract_status == 'expired'">Expired</t>
                                    <t t-if="c.contract_status == 'terminated'">Terminated</t>
                                </td>
                            </tr>
                        </t>
                    </tbody>
                </t>
            </t>
        </template>

        <template id="portal_lease_contract_form_view" name="Lease Contract Form View">
            <t t-call="portal.portal_layout">
                <div class="container mt16 mb16">
                    <t t-if="lease_contract">
                        <h2 class="text-center">Lease Contract Details</h2>
                        <div class="row mt16">
                            <div class="col-12 col-md-6 mb16">
                                <div class="card"
                                     style="box-shadow: 0 4px 8px 0 rgba(0, 0, 0, 0.2), 0 6px 20px 0 rgba(0, 0, 0, 0.19);">
                                    <div class="card-header bg-primary text-white">
                                        <h3>General Information</h3>
                                    </div>
                                    <div class="card-body">
                                        <p>
                                            <strong>Reference:</strong>
                                            <t t-out="lease_contract.name"/>
                                        </p>
                                        <p>
                                            <strong>contract Type:</strong>
                                            <t t-out="lease_contract.lease_contract_type"/>
                                        </p>
                                        <p>
                                            <strong>Customer:</strong>
                                            <t t-out="lease_contract.sudo().customer_id.name"/>
                                        </p>
                                        <p>
                                            <strong>Lease Type:</strong>
                                            <t t-out="dict(lease_contract._fields['lease_contract_type'].selection).get(lease_contract.lease_contract_type)"/>
                                        </p>
                                        <p>
                                            <strong>Notice Period:</strong>
                                            <t t-out="lease_contract.notice_period"/>
                                        </p>
                                        <p>
                                            <strong>Contract Status:</strong>
                                            <t t-if="lease_contract.contract_status == 'draft'">Draft</t>
                                            <t t-if="lease_contract.contract_status == 'reserved'">Reserved</t>
                                            <t t-if="lease_contract.contract_status == 'approved'">Approved</t>
                                            <t t-if="lease_contract.contract_status == 'running'">Running</t>
                                            <t t-if="lease_contract.contract_status == 'cancelled'">Cancelled</t>
                                            <t t-if="lease_contract.contract_status == 'expired'">Expired</t>
                                            <t t-if="lease_contract.contract_status == 'terminated'">Terminated</t>
                                        </p>
                                    </div>
                                </div>
                                <br/>
                                <div class="card"
                                     style="box-shadow: 0 4px 8px 0 rgba(0, 0, 0, 0.2), 0 6px 20px 0 rgba(0, 0, 0, 0.19);">
                                    <div class="card-header bg-primary text-white">
                                        <h3>Unit Information</h3>
                                    </div>
                                    <div class="card-body">
                                        <p>
                                            <strong>Unit:</strong>
                                            <t t-out="lease_contract.unit_name"/>
                                        </p>
                                        <p>
                                            <strong>Unit Number:</strong>
                                            <t t-out="lease_contract.unit_number"/>
                                        </p>
                                        <p>
                                            <strong>Unit Code:</strong>
                                            <t t-out="lease_contract.unit_code"/>
                                        </p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-12 col-md-6 mb16">
                                <div class="card"
                                     style="box-shadow: 0 4px 8px 0 rgba(0, 0, 0, 0.2), 0 6px 20px 0 rgba(0, 0, 0, 0.19);">
                                    <div class="card-header bg-primary text-white">
                                        <h3>Date Information</h3>
                                    </div>
                                    <div class="card-body">
                                        <p>
                                            <strong>Issue Date:</strong>
                                            <t t-out="lease_contract.contract_issue_date"/>
                                        </p>
                                        <p>
                                            <strong>Start Date:</strong>
                                            <t t-out="lease_contract.contract_start_date"/>
                                        </p>
                                        <p>
                                            <strong>Grace Period Start:</strong>
                                            <t t-out="lease_contract.grace_period_start"/>
                                        </p>
                                        <p>
                                            <strong>Grace Period End:</strong>
                                            <t t-out="lease_contract.grace_period_end"/>
                                        </p>
                                    </div>
                                </div>
                                <br/>
                                <div class="card"
                                     style="box-shadow: 0 4px 8px 0 rgba(0, 0, 0, 0.2), 0 6px 20px 0 rgba(0, 0, 0, 0.19);">
                                    <div class="card-header bg-primary text-white">
                                        <h3>Financial Information</h3>
                                    </div>
                                    <div class="card-body">
                                        <p>
                                            <strong>Payment Plan</strong>
                                            <t t-if="lease_contract.payment_plan == 'monthly'">Monthly</t>
                                            <t t-if="lease_contract.payment_plan == 'quarterly'">Quarterly</t>
                                            <t t-if="lease_contract.payment_plan == 'semi_annually'">Semi-Annually</t>
                                            <t t-if="lease_contract.payment_plan == 'annually'">Annually</t>
                                        </p>
                                        <p>
                                            <strong>Rent Price (per m² per month):</strong>
                                            <t t-out="lease_contract.rent_price_per_m2_per_month"/>
                                        </p>
                                        <p>
                                            <strong>Maintenance (per m² per month):</strong>
                                            <t t-out="lease_contract.maintenance_per_m2_per_month"/>
                                        </p>
                                        <p>
                                            <strong>Advance Payment:</strong>
                                            <t t-out="lease_contract.advance_payment"/>
                                        </p>
                                        <p>
                                            <strong>Security Deposit:</strong>
                                            <t t-out="lease_contract.security_deposit"/>
                                        </p>
                                        <p>
                                            <strong>Annual Increase:</strong>
                                            <t t-out="lease_contract.increment_percentage"/>
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <t t-if="lease_contract.lease_contract_type == 'commercial'">
                            <div class="row mt16">
                                <div class="col-12">
                                    <div class="card"
                                         style="box-shadow: 0 4px 8px 0 rgba(0, 0, 0, 0.2), 0 6px 20px 0 rgba(0, 0, 0, 0.19);">
                                        <div class="card-header bg-primary text-white">
                                            <h3>Additional Information</h3>
                                        </div>
                                        <div class="card-body">
                                            <p>
                                                <strong>Minimum Opening Hours:</strong>
                                                <t t-out="lease_contract.minimum_opening_hours"/>
                                            </p>
                                            <p>
                                                <strong>Revenue Share:</strong>
                                                <t t-out="lease_contract.revenue_share"/>
                                            </p>
                                            <p>
                                                <strong>Revenue Share 2:</strong>
                                                <t t-out="lease_contract.revenue_share_2"/>
                                            </p>
                                            <p>
                                                <strong>Expected Sales per Month:</strong>
                                                <t t-out="lease_contract.expected_sales_per_month"/>
                                            </p>
                                            <p>
                                                <strong>Marketing Fees:</strong>
                                                <t t-out="lease_contract.marketing_fees"/>
                                            </p>
                                            <p>
                                                <strong>CAPEX Cost:</strong>
                                                <t t-out="lease_contract.capex_cost"/>
                                            </p>
                                            <p>
                                                <strong>Service Charge (per m² per month):</strong>
                                                <t t-out="lease_contract.service_charge_per_m2_per_month"/>
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Penalties Section -->
                            <div class="row mt16">
                                <div class="col-12">
                                    <div class="card"
                                         style="box-shadow: 0 4px 8px 0 rgba(0, 0, 0, 0.2), 0 6px 20px 0 rgba(0, 0, 0, 0.19);">
                                        <div class="card-header bg-primary text-white">
                                            <h3>Penalties</h3>
                                        </div>
                                        <div class="card-body">
                                            <p>
                                                <strong>Late Payment Penalty:</strong>
                                                <t t-out="lease_contract.late_payment_penalty"/>
                                            </p>
                                            <p>
                                                <strong>Late Payment Limit:</strong>
                                                <t t-out="lease_contract.late_payment_limit"/>
                                            </p>
                                            <p>
                                                <strong>Late Opening Penalty:</strong>
                                                <t t-out="lease_contract.late_opening_penalty"/>
                                            </p>
                                            <p>
                                                <strong>Non-Evacuation Penalty:</strong>
                                                <t t-out="lease_contract.non_evacuation_penalty"/>
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="row mt16">
                                <div class="col-12">
                                    <div class="card"
                                         style="box-shadow: 0 4px 8px 0 rgba(0, 0, 0, 0.2), 0 6px 20px 0 rgba(0, 0, 0, 0.19);">
                                        <div class="card-header bg-primary text-white">
                                            <h3>Utilities</h3>
                                        </div>
                                        <div class="card-body">
                                            <p>
                                                <strong>Electricity:</strong>
                                                <t t-if="lease_contract.electricity">Yes</t>
                                                <t t-if="not lease_contract.electricity">No</t>
                                            </p>
                                            <p>
                                                <strong>Water:</strong>
                                                <t t-if="lease_contract.water">Yes</t>
                                                <t t-if="not lease_contract.water">No</t>
                                            </p>
                                            <p>
                                                <strong>Gas:</strong>
                                                <t t-if="lease_contract.gas">Yes</t>
                                                <t t-if="not lease_contract.gas">No</t>
                                            </p>
                                            <p>
                                                <strong>Cooling Water:</strong>
                                                <t t-if="lease_contract.cooling_water">Yes</t>
                                                <t t-if="not lease_contract.cooling_water">No</t>
                                            </p>
                                            <t t-if="lease_contract.cooling_water">
                                                <p>
                                                    <strong>Cooling Water Price:</strong>
                                                    <t t-out="lease_contract.cooling_water_price"/>
                                                </p>
                                            </t>
                                            <p>
                                                <strong>Landline:</strong>
                                                <t t-if="lease_contract.landline">Yes</t>
                                                <t t-if="not lease_contract.landline">No</t>
                                            </p>
                                            <t t-if="lease_contract.landline">
                                                <p>
                                                    <strong>Landline Fees:</strong>
                                                    <t t-out="lease_contract.landline_fees"/>
                                                </p>
                                            </t>
                                            <p>
                                                <strong>Internet:</strong>
                                                <t t-if="lease_contract.internet">Yes</t>
                                                <t t-if="not lease_contract.internet">No</t>
                                            </p>
                                            <p>
                                                <strong>Solar System:</strong>
                                                <t t-if="lease_contract.solar_system">Yes</t>
                                                <t t-if="not lease_contract.solar_system">No</t>
                                            </p>
                                            <p>
                                                <strong>Heating System:</strong>
                                                <t t-if="lease_contract.heating_system">Yes</t>
                                                <t t-if="not lease_contract.heating_system">No</t>
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            </div>

                        </t>
                    </t>
                    <t t-if="not lease_contract">
                        <h2 class="text-center">Lease Contract not found</h2>
                    </t>
                </div>
            </t>
        </template>


    </data>
</odoo>