<odoo>
    <data>

         <record id="purchase_delivery_form" model="ir.ui.view">
            <field name="name">delivery</field>
            <field name="model">purchase.delivery</field>
            <field name="arch" type="xml">
                <form string="">
                    <sheet>
                        <group>
                            <field name="name"/>
                        </group>
                    </sheet>
                </form>
            </field>
        </record>
        <record id="purchase_delivery_tree" model="ir.ui.view">
            <field name="name">delivery</field>
            <field name="model">purchase.delivery</field>
            <field name="arch" type="xml">
                <tree string="">
                    <field name="name"/>
                </tree>
            </field>
        </record>
        <record id="purchase_delivery_action" model="ir.actions.act_window">
            <field name="name">delivery</field>
            <field name="type">ir.actions.act_window</field>
            <field name="res_model">purchase.delivery</field>
            <field name="view_mode">tree,form</field>
            <field name="help" type="html">
              <p class="oe_view_nocontent_create">
                <!-- Add Text Here -->
              </p><p>
                <!-- More details about what a user can do with this object will be OK -->
              </p>
            </field>
        </record>


         <record id="purchase_warranty_form" model="ir.ui.view">
            <field name="name">Warranty</field>
            <field name="model">purchase.warranty</field>
            <field name="arch" type="xml">
                <form string="">
                    <sheet>
                        <group>
                            <field name="name"/>
                        </group>
                    </sheet>
                </form>
            </field>
        </record>
        <record id="purchase_warranty_tree" model="ir.ui.view">
            <field name="name">Warranty</field>
            <field name="model">purchase.warranty</field>
            <field name="arch" type="xml">
                <tree string="">
                    <field name="name"/>
                </tree>
            </field>
        </record>
        <record id="purchase_warranty_action" model="ir.actions.act_window">
            <field name="name">Warranty</field>
            <field name="type">ir.actions.act_window</field>
            <field name="res_model">purchase.warranty</field>
            <field name="view_mode">tree,form</field>
            <field name="help" type="html">
              <p class="oe_view_nocontent_create">
                <!-- Add Text Here -->
              </p><p>
                <!-- More details about what a user can do with this object will be OK -->
              </p>
            </field>
        </record>


         <record id="purchase_certificate_from" model="ir.ui.view">
            <field name="name">purchase certificate</field>
            <field name="model">purchase.certificate</field>
            <field name="arch" type="xml">
                <form string="">
                    <sheet>
                        <group>
                            <field name="name"/>
                        </group>
                    </sheet>
                </form>
            </field>
        </record>
        <record id="tree_purchase_certificate" model="ir.ui.view">
            <field name="name">purchase.certificate</field>
            <field name="model">purchase.certificate</field>
            <field name="arch" type="xml">
                <tree string="">
                    <field name="name"/>
                </tree>
            </field>
        </record>
        <record id="action_purchase_certificate" model="ir.actions.act_window">
            <field name="name">Purchase Certificate</field>
            <field name="type">ir.actions.act_window</field>
            <field name="res_model">purchase.certificate</field>
            <field name="view_mode">tree,form</field>
            <field name="help" type="html">
              <p class="oe_view_nocontent_create">
                <!-- Add Text Here -->
              </p><p>
                <!-- More details about what a user can do with this object will be OK -->
              </p>
            </field>
        </record>



         <record id="purchase_payment_method_form" model="ir.ui.view">
            <field name="name">purchase payment method</field>
            <field name="model">purchase.payment.method</field>
            <field name="arch" type="xml">
                <form string="">
                    <sheet>
                        <group>
                            <field name="name"/>
                        </group>
                    </sheet>
                </form>
            </field>
        </record>
        <record id="purchase_payment_method_tree" model="ir.ui.view">
            <field name="name">purchase.payment.method</field>
            <field name="model">purchase.payment.method</field>
            <field name="arch" type="xml">
                <tree string="">
                    <field name="name"/>
                </tree>
            </field>
        </record>
        <record id="purchase_payment_method_action" model="ir.actions.act_window">
            <field name="name">purchase payment method</field>
            <field name="type">ir.actions.act_window</field>
            <field name="res_model">purchase.payment.method</field>
            <field name="view_mode">tree,form</field>
            <field name="help" type="html">
              <p class="oe_view_nocontent_create">
                <!-- Add Text Here -->
              </p><p>
                <!-- More details about what a user can do with this object will be OK -->
              </p>
            </field>
        </record>





        <!-- This Menu Item must have a parent and an action -->
        <menuitem id="purchase_payment_method_menu" name="Payment Method" parent="purchase.menu_purchase_config" action="purchase_payment_method_action" sequence="20"/>
        <menuitem id="menu_purchase_certificate" name="Purchase Certificate" parent="purchase.menu_purchase_config" action="action_purchase_certificate" sequence="20"/>
        <menuitem id="purchase_delivery_menu" name="Delivery Period" parent="purchase.menu_purchase_config" action="purchase_delivery_action" sequence="20"/>
        <menuitem id="purchase_warranty_menu" name="Warranty" parent="purchase.menu_purchase_config" action="purchase_warranty_action" sequence="20"/>
    </data>

</odoo>