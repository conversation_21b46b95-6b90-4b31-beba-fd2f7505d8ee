# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* website_sale_stock_wishlist
# 
# Translators:
# <PERSON> <<EMAIL>>, 2023
# <PERSON>, 2023
# <PERSON><PERSON> <linas<PERSON><PERSON><PERSON>@gmail.com>, 2023
# <PERSON><PERSON><PERSON><PERSON> M, 2024
# <PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-10-26 21:56+0000\n"
"PO-Revision-Date: 2023-10-26 23:09+0000\n"
"Last-Translator: <PERSON>, 2024\n"
"Language-Team: Lithuanian (https://app.transifex.com/odoo/teams/41243/lt/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: lt\n"
"Plural-Forms: nplurals=4; plural=(n % 10 == 1 && (n % 100 > 19 || n % 100 < 11) ? 0 : (n % 10 >= 2 && n % 10 <=9) && (n % 100 > 19 || n % 100 < 11) ? 1 : n % 1 != 0 ? 2: 3);\n"

#. module: website_sale_stock_wishlist
#: model_terms:ir.ui.view,arch_db:website_sale_stock_wishlist.product_wishlist
msgid ""
"<i class=\"fa fa-bell\"/>\n"
"                        We'll notify you once the product is back in stock."
msgstr ""

#. module: website_sale_stock_wishlist
#: model_terms:ir.ui.view,arch_db:website_sale_stock_wishlist.product_wishlist
msgid ""
"<i class=\"fa fa-times text-danger\"/>\n"
"                                Invalid email"
msgstr ""

#. module: website_sale_stock_wishlist
#: model_terms:ir.ui.view,arch_db:website_sale_stock_wishlist.product_wishlist
msgid ""
"<small>\n"
"                            <i class=\"fa fa-envelope-o\"/>\n"
"                            Get notified when back in stock\n"
"                        </small>"
msgstr ""
"<small>\n"
"                            <i class=\"fa fa-envelope-o\"/>\n"
"                            Gauti pranešimą gavus prekių papildymą\n"
"                        </small>"

#. module: website_sale_stock_wishlist
#: model_terms:ir.ui.view,arch_db:website_sale_stock_wishlist.product_wishlist
msgid ""
"<span class=\"fa fa-fw fa-shopping-cart\"/>\n"
"                <span class=\"d-none d-md-inline\">Add</span>"
msgstr ""

#. module: website_sale_stock_wishlist
#. odoo-javascript
#: code:addons/website_sale_stock_wishlist/static/src/xml/product_availability.xml:0
#, python-format
msgid "Add to wishlist"
msgstr "Pasižymėti"

#. module: website_sale_stock_wishlist
#. odoo-javascript
#: code:addons/website_sale_stock_wishlist/static/src/xml/product_availability.xml:0
#, python-format
msgid "Added to your wishlist"
msgstr "Pridėta į norų sąrašą"

#. module: website_sale_stock_wishlist
#: model:ir.model,name:website_sale_stock_wishlist.model_product_template
msgid "Product"
msgstr "Produktas"

#. module: website_sale_stock_wishlist
#: model:ir.model,name:website_sale_stock_wishlist.model_product_wishlist
msgid "Product Wishlist"
msgstr "Produkto norų sąrašas"

#. module: website_sale_stock_wishlist
#. odoo-javascript
#: code:addons/website_sale_stock_wishlist/static/src/xml/product_availability.xml:0
#, python-format
msgid "Save for later"
msgstr "Išsaugoti ateičiai"

#. module: website_sale_stock_wishlist
#: model:ir.model.fields,field_description:website_sale_stock_wishlist.field_product_wishlist__stock_notification
msgid "Stock Notification"
msgstr ""

#. module: website_sale_stock_wishlist
#: model_terms:ir.ui.view,arch_db:website_sale_stock_wishlist.product_wishlist
msgid "Temporarily out of stock"
msgstr "Laikinai nėra sandėlyje"

#. module: website_sale_stock_wishlist
#: model_terms:ir.ui.view,arch_db:website_sale_stock_wishlist.product_wishlist
msgid "<EMAIL>"
msgstr "<EMAIL>"
