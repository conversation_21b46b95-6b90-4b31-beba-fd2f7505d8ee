# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* hr_skills_survey
# 
# Translators:
# Wil <PERSON>, 2023
# <PERSON><PERSON>, 2023
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-10-26 21:55+0000\n"
"PO-Revision-Date: 2023-10-26 23:09+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON> <<EMAIL>>, 2024\n"
"Language-Team: Japanese (https://app.transifex.com/odoo/teams/41243/ja/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ja\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: hr_skills_survey
#: model:hr.resume.line,name:hr_skills_survey.resume_line_valid
msgid "AWS Cloud"
msgstr "AWSクラウド"

#. module: hr_skills_survey
#: model:ir.model.fields,field_description:hr_skills_survey.field_hr_resume_line__survey_id
#: model:ir.model.fields.selection,name:hr_skills_survey.selection__hr_resume_line__display_type__certification
#: model_terms:ir.ui.view,arch_db:hr_skills_survey.hr_resume_line_view_search
msgid "Certification"
msgstr "検定"

#. module: hr_skills_survey
#: model:ir.ui.menu,name:hr_skills_survey.hr_employee_certication_report_menu
msgid "Certifications"
msgstr "検定"

#. module: hr_skills_survey
#: model:ir.model.fields,field_description:hr_skills_survey.field_hr_resume_line__department_id
#: model_terms:ir.ui.view,arch_db:hr_skills_survey.hr_resume_line_view_search
msgid "Department"
msgstr "部門"

#. module: hr_skills_survey
#: model:ir.model.fields,field_description:hr_skills_survey.field_hr_resume_line__display_type
msgid "Display Type"
msgstr "表示タイプ"

#. module: hr_skills_survey
#: model_terms:ir.ui.view,arch_db:hr_skills_survey.hr_resume_line_view_search
msgid "Employee"
msgstr "従業員"

#. module: hr_skills_survey
#: model:ir.actions.act_window,name:hr_skills_survey.hr_employee_certification_report_action
msgid "Employee Certifications"
msgstr "従業員検定"

#. module: hr_skills_survey
#: model:ir.model.fields,field_description:hr_skills_survey.field_hr_resume_line__expiration_status
msgid "Expiration Status"
msgstr "有効期限ステータス"

#. module: hr_skills_survey
#: model_terms:ir.ui.view,arch_db:hr_skills_survey.hr_resume_line_view_search
msgid "Expiration date"
msgstr "有効期限日"

#. module: hr_skills_survey
#: model:ir.model.fields.selection,name:hr_skills_survey.selection__hr_resume_line__expiration_status__expired
#: model_terms:ir.ui.view,arch_db:hr_skills_survey.hr_resume_line_view_search
msgid "Expired"
msgstr "契約期間終了済"

#. module: hr_skills_survey
#: model:ir.model.fields.selection,name:hr_skills_survey.selection__hr_resume_line__expiration_status__expiring
msgid "Expiring"
msgstr "失効"

#. module: hr_skills_survey
#: model_terms:ir.ui.view,arch_db:hr_skills_survey.hr_resume_line_view_search
msgid "Expiring Soon"
msgstr "すぐに失効する"

#. module: hr_skills_survey
#: model:hr.resume.line,name:hr_skills_survey.resume_line_expiring
msgid "MongoDB Developer"
msgstr "MongoDB開発者"

#. module: hr_skills_survey
#: model:hr.resume.line,name:hr_skills_survey.resume_line_aws
msgid "Oracle DB"
msgstr "Oracle DB"

#. module: hr_skills_survey
#: model:ir.model,name:hr_skills_survey.model_hr_resume_line
msgid "Resume line of an employee"
msgstr "従業員の履歴書明細"

#. module: hr_skills_survey
#: model:ir.model,name:hr_skills_survey.model_survey_user_input
msgid "Survey User Input"
msgstr "ユーザーの入力を調査する"

#. module: hr_skills_survey
#: model:ir.model.fields.selection,name:hr_skills_survey.selection__hr_resume_line__expiration_status__valid
msgid "Valid"
msgstr "有効"

#. module: hr_skills_survey
#: model_terms:ir.ui.view,arch_db:hr_skills_survey.hr_resume_line_view_search
msgid "Valid Until"
msgstr "有効期限日"

#. module: hr_skills_survey
#: model_terms:ir.ui.view,arch_db:hr_skills_survey.hr_employee_certification_report_view_list
msgid "Validity End"
msgstr "有効期間終了"

#. module: hr_skills_survey
#: model_terms:ir.ui.view,arch_db:hr_skills_survey.hr_employee_certification_report_view_list
msgid "Validity Start"
msgstr "有効期間開始"
