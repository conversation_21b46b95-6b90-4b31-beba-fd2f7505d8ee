# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* website_sale_picking
# 
# Translators:
# Wil <PERSON>, 2024
# <PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-08-13 10:28+0000\n"
"PO-Revision-Date: 2023-10-26 23:09+0000\n"
"Last-Translator: <PERSON>, 2025\n"
"Language-Team: Chinese (Taiwan) (https://app.transifex.com/odoo/teams/41243/zh_TW/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: zh_TW\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: website_sale_picking
#: model_terms:payment.provider,pending_msg:website_sale_picking.payment_provider_onsite
msgid ""
"<i>Your order has been saved.</i> Please come to the store to pay for your "
"products"
msgstr "<b>已儲存你的訂單。</b>請前往商店，為選購產品付款"

#. module: website_sale_picking
#: model_terms:ir.ui.view,arch_db:website_sale_picking.payment_confirmation_status
msgid "<span class=\"text-muted\"> (On site picking)</span>"
msgstr "<span class=\"text-muted\">（到場自取）</span>"

#. module: website_sale_picking
#: model:ir.model,name:website_sale_picking.model_res_config_settings
msgid "Config Settings"
msgstr "配置設定"

#. module: website_sale_picking
#: model:ir.model.fields,field_description:website_sale_picking.field_payment_provider__custom_mode
msgid "Custom Mode"
msgstr "自訂模式"

#. module: website_sale_picking
#: model_terms:ir.ui.view,arch_db:website_sale_picking.res_config_settings_view_form
msgid "Customize Pickup Sites"
msgstr "自訂取貨地點"

#. module: website_sale_picking
#. odoo-javascript
#: code:addons/website_sale_picking/static/src/js/payment_form.js:0
#, python-format
msgid ""
"If you believe that it is an error, please contact the website "
"administrator."
msgstr "如果您認為這是一個錯誤，請聯繫網站管理員。"

#. module: website_sale_picking
#. odoo-javascript
#: code:addons/website_sale_picking/static/src/js/payment_form.js:0
#, python-format
msgid "No suitable payment method could be found."
msgstr "找不到合適的付款方式。"

#. module: website_sale_picking
#: model:ir.model.fields.selection,name:website_sale_picking.selection__payment_provider__custom_mode__onsite
msgid "On Site"
msgstr "到場"

#. module: website_sale_picking
#: model:product.template,name:website_sale_picking.onsite_delivery_product_product_template
msgid "On site picking"
msgstr "到場自取"

#. module: website_sale_picking
#: model:payment.provider,name:website_sale_picking.payment_provider_onsite
#: model_terms:product.template,description:website_sale_picking.onsite_delivery_product_product_template
msgid "Pay in Store"
msgstr "到店付款"

#. module: website_sale_picking
#: model:ir.model,name:website_sale_picking.model_payment_provider
msgid "Payment Provider"
msgstr "付款服務商"

#. module: website_sale_picking
#: model:ir.model.fields,field_description:website_sale_picking.field_res_config_settings__picking_site_ids
#: model:ir.model.fields,field_description:website_sale_picking.field_website__picking_site_ids
#: model_terms:ir.ui.view,arch_db:website_sale_picking.res_config_settings_view_form
msgid "Picking sites"
msgstr "取貨地點"

#. module: website_sale_picking
#: model:ir.model.fields.selection,name:website_sale_picking.selection__delivery_carrier__delivery_type__onsite
msgid "Pickup in store"
msgstr "到店自取"

#. module: website_sale_picking
#: model:ir.model.fields,field_description:website_sale_picking.field_delivery_carrier__delivery_type
msgid "Provider"
msgstr "服務商"

#. module: website_sale_picking
#: model:ir.model,name:website_sale_picking.model_sale_order
msgid "Sales Order"
msgstr "銷售訂單"

#. module: website_sale_picking
#: model:ir.model,name:website_sale_picking.model_delivery_carrier
msgid "Shipping Methods"
msgstr "運送方式"

#. module: website_sale_picking
#. odoo-python
#: code:addons/website_sale_picking/models/delivery_carrier.py:0
#, python-format
msgid "The picking site and warehouse must share the same company"
msgstr "取貨地點與倉庫必須是同一家公司"

#. module: website_sale_picking
#: model:ir.model.fields,field_description:website_sale_picking.field_delivery_carrier__warehouse_id
msgid "Warehouse"
msgstr "倉庫"

#. module: website_sale_picking
#: model:ir.model,name:website_sale_picking.model_website
msgid "Website"
msgstr "網站"

#. module: website_sale_picking
#. odoo-python
#: code:addons/website_sale_picking/controllers/main.py:0
#, python-format
msgid "You cannot pay onsite if the delivery is not onsite"
msgstr "若不是到場取貨，便不可選擇到店付款"

#. module: website_sale_picking
#: model_terms:payment.provider,auth_msg:website_sale_picking.payment_provider_onsite
msgid "Your payment has been authorized."
msgstr "您的付款已獲授權。"

#. module: website_sale_picking
#: model_terms:payment.provider,cancel_msg:website_sale_picking.payment_provider_onsite
msgid "Your payment has been cancelled."
msgstr "您的付款已被取消。"

#. module: website_sale_picking
#: model_terms:payment.provider,done_msg:website_sale_picking.payment_provider_onsite
msgid "Your payment has been successfully processed."
msgstr "你的付款已成功處理。"

#. module: website_sale_picking
#: model:delivery.carrier,name:website_sale_picking.default_onsite_carrier
msgid "[On Site Pick] My Shop 1"
msgstr "[自取] 我的商店 1"
