<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- add picking to wave  -->
    <record id="stock_add_to_wave_form" model="ir.ui.view">
        <field name="name">stock.add.to.wave.form</field>
        <field name="model">stock.add.to.wave</field>
        <field name="arch" type="xml">
            <form string="Add to Wave">
                <group>
                    <group>
                        <label for="mode" string="Add to"/>
                        <field name="mode" widget="radio" nolabel="1"/>
                        <field name="wave_id" options="{'no_create': True, 'no_open': True}" invisible="mode == 'new'" required="mode == 'existing'"/>
                        <field name="user_id" options="{'no_create': True}" invisible="mode == 'existing'"/>
                    </group>
                </group>

                <footer>
                    <button name="attach_pickings" type="object" string="Confirm" class="btn-primary"/>
                    <button string="Cancel" class="btn-secondary" special="cancel"/>
                </footer>
            </form>
        </field>
    </record>
    <record id="stock_add_to_wave_action_stock_picking" model="ir.actions.act_window">
        <field name="name">Add to wave</field>
        <field name="res_model">stock.add.to.wave</field>
        <field name="view_mode">form</field>
        <field name="target">new</field>
        <field name="groups_id" eval="[(4, ref('stock.group_stock_picking_wave'))]"/>
        <field name="binding_model_id" ref="stock.model_stock_picking"/>
        <field name="binding_view_types">list</field>
    </record>
</odoo>
