<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="diot_report" model="account.report">
        <field name="name">DIOT</field>
        <field name="root_report_id" ref="account.generic_tax_report"/>
        <field name="filter_show_draft" eval="False"/>
        <field name="filter_period_comparison" eval="False"/>
        <field name="load_more_limit" eval="80"/>
        <field name="filter_unfold_all" eval="False"/>
        <field name="default_opening_date_filter">this_month</field>
        <field name="filter_date_range" eval="True"/>
        <field name="filter_partner" eval="True"/>
        <field name="filter_multi_company">selector</field>
        <field name="country_id" ref="base.mx"/>
        <field name="availability_condition">country</field>
        <field name="column_ids">
            <record id="diot_report_paid_16" model="account.report.column">
                <field name="name">Paid 16%</field>
                <field name="expression_label">paid_16</field>
                <field name="sequence">10</field>
            </record>
            <record id="diot_report_paid_16_non_cred" model="account.report.column">
                <field name="name">Paid 16% - Non-Creditable</field>
                <field name="expression_label">paid_16_non_cred</field>
                <field name="sequence">11</field>
            </record>
            <record id="diot_report_paid_8" model="account.report.column">
                <field name="name">Paid 8 %</field>
                <field name="expression_label">paid_8</field>
                <field name="sequence">12</field>
            </record>
            <record id="diot_report_paid_8_non_cred" model="account.report.column">
                <field name="name">Paid 8 % - Non-Creditable</field>
                <field name="expression_label">paid_8_non_cred</field>
                <field name="sequence">13</field>
            </record>
            <record id="diot_report_importation_16" model="account.report.column">
                <field name="name">Importation 16%</field>
                <field name="expression_label">importation_16</field>
                <field name="sequence">14</field>
            </record>
            <record id="diot_report_paid_0" model="account.report.column">
                <field name="name">Paid 0%</field>
                <field name="expression_label">paid_0</field>
                <field name="sequence">15</field>
            </record>
            <record id="diot_report_exempt" model="account.report.column">
                <field name="name">Exempt</field>
                <field name="expression_label">exempt</field>
                <field name="sequence">16</field>
            </record>
            <record id="diot_report_withheld" model="account.report.column">
                <field name="name">Withheld</field>
                <field name="expression_label">withheld</field>
                <field name="sequence">17</field>
            </record>
            <record id="diot_report_refunds" model="account.report.column">
                <field name="name">Refunds</field>
                <field name="expression_label">refunds</field>
                <field name="sequence">18</field>
            </record>
        </field>
        <field name="line_ids">
            <record id="diot_report_line" model="account.report.line">
                <field name="name">DIOT</field>
                <field name="groupby">partner_id, id</field>
                <field name="hierarchy_level">0</field>
                <field name="expression_ids">
                    <record id="tax_report_mx_diot_paid_16_tag" model="account.report.expression">
                        <field name="label">paid_16</field>
                        <field name="engine">tax_tags</field>
                        <field name="formula">DIOT: 16%</field>
                    </record>
                    <record id="tax_report_mx_diot_paid_16_non_cred_tag" model="account.report.expression">
                        <field name="label">paid_16_non_cred</field>
                        <field name="engine">tax_tags</field>
                        <field name="formula">DIOT: 16% NO ACREDITABLE</field>
                    </record>
                    <record id="tax_report_mx_diot_paid_8_tag" model="account.report.expression">
                        <field name="label">paid_8</field>
                        <field name="engine">tax_tags</field>
                        <field name="formula">DIOT: 8%</field>
                    </record>
                    <record id="tax_report_mx_diot_paid_8_non_cred_tag" model="account.report.expression">
                        <field name="label">paid_8_non_cred</field>
                        <field name="engine">tax_tags</field>
                        <field name="formula">DIOT: 8% NO ACREDITABLE</field>
                    </record>
                    <record id="tax_report_mx_diot_importation_16_tag" model="account.report.expression">
                        <field name="label">importation_16</field>
                        <field name="engine">tax_tags</field>
                        <field name="formula">DIOT: 16% IMP</field>
                    </record>
                    <record id="tax_report_mx_diot_paid_0_tag" model="account.report.expression">
                        <field name="label">paid_0</field>
                        <field name="engine">tax_tags</field>
                        <field name="formula">DIOT: 0%</field>
                    </record>
                    <record id="tax_report_mx_diot_exempt_tag" model="account.report.expression">
                        <field name="label">exempt</field>
                        <field name="engine">tax_tags</field>
                        <field name="formula">DIOT: Exento</field>
                    </record>
                    <record id="tax_report_mx_diot_withheld_tag" model="account.report.expression">
                        <field name="label">withheld</field>
                        <field name="engine">tax_tags</field>
                        <field name="formula">DIOT: Retención</field>
                    </record>
                    <record id="tax_report_mx_diot_refunds_tag" model="account.report.expression">
                        <field name="label">refunds</field>
                        <field name="engine">tax_tags</field>
                        <field name="formula">DIOT: Refunds</field>
                    </record>
                </field>
            </record>
        </field>
    </record>
</odoo>
