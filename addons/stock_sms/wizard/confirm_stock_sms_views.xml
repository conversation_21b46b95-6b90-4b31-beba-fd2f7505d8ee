<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="view_confirm_stock_sms" model="ir.ui.view">
        <field name="name">stock_confirm_sms</field>
        <field name="model">confirm.stock.sms</field>
        <field name="arch" type="xml">
            <form string="SMS">
                You are about to confirm this Delivery Order by SMS Text Message.<br/>
                This feature can easily be disabled from the Settings of Inventory or by clicking on "Disable SMS".<br/>
                <footer>
                    <button name="send_sms" type="object" data-hotkey="q"
                            string="Confirm" class="oe_highlight"/>
                    <button name="dont_send_sms" type="object" data-hotkey="w"
                            string="Disable SMS" class="btn btn-secondary"/>
                    <button special="cancel" data-hotkey="x" string="Cancel"/>
                </footer>
            </form>
        </field>
    </record>
</odoo>
