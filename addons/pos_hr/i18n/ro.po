# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* pos_hr
# 
# Translators:
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON><PERSON> <fold<PERSON><EMAIL>>, 2024
# <PERSON>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-08-13 10:27+0000\n"
"PO-Revision-Date: 2023-10-26 23:09+0000\n"
"Last-Translator: <PERSON>, 2024\n"
"Language-Team: Romanian (https://app.transifex.com/odoo/teams/41243/ro/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ro\n"
"Plural-Forms: nplurals=3; plural=(n==1?0:(((n%100>19)||((n%100==0)&&(n!=0)))?2:1));\n"

#. module: pos_hr
#: model_terms:ir.ui.view,arch_db:pos_hr.pos_config_form_view_inherit
#: model_terms:ir.ui.view,arch_db:pos_hr.res_config_settings_view_form
msgid "Advanced rights"
msgstr "Drepturi avansate"

#. module: pos_hr
#: model_terms:ir.ui.view,arch_db:pos_hr.res_config_settings_view_form
msgid "All employees"
msgstr "Toți angajații"

#. module: pos_hr
#: model_terms:ir.ui.view,arch_db:pos_hr.pos_config_form_view_inherit
#: model_terms:ir.ui.view,arch_db:pos_hr.res_config_settings_view_form
msgid "Basic rights"
msgstr "Drepturi de bază"

#. module: pos_hr
#: model:ir.model.fields,field_description:pos_hr.field_pos_order__cashier
#: model_terms:ir.ui.view,arch_db:pos_hr.pos_order_form_inherit
#: model_terms:ir.ui.view,arch_db:pos_hr.pos_order_list_select_inherit
msgid "Cashier"
msgstr "Casier"

#. module: pos_hr
#. odoo-javascript
#: code:addons/pos_hr/static/src/app/select_cashier_mixin.js:0
#, python-format
msgid "Change Cashier"
msgstr "Schimbare casier"

#. module: pos_hr
#: model:ir.model,name:pos_hr.model_res_config_settings
msgid "Config Settings"
msgstr "Setări de configurare"

#. module: pos_hr
#: model:ir.model,name:pos_hr.model_hr_employee
#: model:ir.model.fields,field_description:pos_hr.field_pos_order__employee_id
#: model:ir.model.fields,field_description:pos_hr.field_report_pos_order__employee_id
#: model_terms:ir.ui.view,arch_db:pos_hr.view_report_pos_order_search_inherit
msgid "Employee"
msgstr "Angajat"

#. module: pos_hr
#. odoo-python
#: code:addons/pos_hr/models/hr_employee.py:0
#, python-format
msgid "Employee: %s - PoS Config(s): %s \n"
msgstr "Angajat: %s - PoS Config(s): %s \n"

#. module: pos_hr
#: model:ir.model.fields,field_description:pos_hr.field_pos_config__basic_employee_ids
#: model:ir.model.fields,field_description:pos_hr.field_res_config_settings__pos_basic_employee_ids
msgid "Employees with basic access"
msgstr "Angajați cu acces de bază"

#. module: pos_hr
#: model:ir.model.fields,field_description:pos_hr.field_pos_config__advanced_employee_ids
#: model:ir.model.fields,field_description:pos_hr.field_res_config_settings__pos_advanced_employee_ids
msgid "Employees with manager access"
msgstr "Angajați cu acces de manager"

#. module: pos_hr
#: model:ir.model.fields,help:pos_hr.field_pos_config__basic_employee_ids
#: model:ir.model.fields,help:pos_hr.field_res_config_settings__pos_basic_employee_ids
msgid "If left empty, all employees can log in to PoS"
msgstr "Dacă este lăsat gol, toți angajații se pot conecta la PoS"

#. module: pos_hr
#: model:ir.model.fields,help:pos_hr.field_pos_config__advanced_employee_ids
#: model:ir.model.fields,help:pos_hr.field_res_config_settings__pos_advanced_employee_ids
msgid "If left empty, only Odoo users have extended rights in PoS"
msgstr ""
"Dacă este lăsat gol, numai utilizatorii Odoo au drepturi extinse în PoS"

#. module: pos_hr
#. odoo-javascript
#: code:addons/pos_hr/static/src/app/select_cashier_mixin.js:0
#, python-format
msgid "Incorrect Password"
msgstr "Parolă incorectă"

#. module: pos_hr
#. odoo-javascript
#: code:addons/pos_hr/static/src/overrides/components/navbar/navbar.xml:0
#, python-format
msgid "Lock"
msgstr "Închide"

#. module: pos_hr
#. odoo-javascript
#: code:addons/pos_hr/static/src/app/login_screen/login_screen.xml:0
#, python-format
msgid "Log in to"
msgstr "Autentificare"

#. module: pos_hr
#. odoo-javascript
#: code:addons/pos_hr/static/src/app/select_cashier_mixin.js:0
#, python-format
msgid "No Cashiers"
msgstr ""

#. module: pos_hr
#: model_terms:ir.ui.view,arch_db:pos_hr.res_config_settings_view_form
msgid "No employee"
msgstr "Fără angajat"

#. module: pos_hr
#. odoo-javascript
#: code:addons/pos_hr/static/src/app/select_cashier_mixin.js:0
#, python-format
msgid "Password?"
msgstr "Parolă?"

#. module: pos_hr
#: model:ir.model.fields,help:pos_hr.field_pos_order__employee_id
msgid ""
"Person who uses the cash register. It can be a reliever, a student or an "
"interim employee."
msgstr ""
"Persoană care folosește casa de marcat. Poate fi un înlocuitor, un student "
"sau un angajat temporar."

#. module: pos_hr
#. odoo-javascript
#: code:addons/pos_hr/static/src/app/select_cashier_mixin.js:0
#, python-format
msgid "Please try again."
msgstr "Vă rugăm să încercați din nou."

#. module: pos_hr
#: model:ir.model,name:pos_hr.model_pos_config
msgid "Point of Sale Configuration"
msgstr "Configurarea Punctului de Vânzare"

#. module: pos_hr
#: model:ir.model,name:pos_hr.model_pos_order
msgid "Point of Sale Orders"
msgstr "Comenzile Punctului de vânzare"

#. module: pos_hr
#: model:ir.model,name:pos_hr.model_report_pos_order
msgid "Point of Sale Orders Report"
msgstr "Raport Comenzi Punct de Vânzare"

#. module: pos_hr
#: model:ir.model,name:pos_hr.model_pos_session
msgid "Point of Sale Session"
msgstr "Sesiune Punct de vânzare"

#. module: pos_hr
#. odoo-javascript
#: code:addons/pos_hr/static/src/app/login_screen/login_screen.xml:0
#, python-format
msgid "Scan your badge"
msgstr "Scanați ecusonul dvs"

#. module: pos_hr
#. odoo-javascript
#: code:addons/pos_hr/static/src/app/login_screen/login_screen.xml:0
#, python-format
msgid "Select Cashier"
msgstr "Selectați Casier"

#. module: pos_hr
#. odoo-javascript
#: code:addons/pos_hr/static/src/app/select_cashier_mixin.js:0
#, python-format
msgid "There are no employees to select as cashier. Please create one."
msgstr ""

#. module: pos_hr
#. odoo-python
#: code:addons/pos_hr/models/hr_employee.py:0
#, python-format
msgid ""
"You cannot delete an employee that may be used in an active PoS session, "
"close the session(s) first: \n"
msgstr ""
"Nu puteți șterge un angajat care poate fi utilizat într-o sesiune PoS "
"activă, închideți mai întâi sesiunea (le):\n"

#. module: pos_hr
#. odoo-javascript
#: code:addons/pos_hr/static/src/app/login_screen/login_screen.xml:0
#, python-format
msgid "or"
msgstr "sau"
