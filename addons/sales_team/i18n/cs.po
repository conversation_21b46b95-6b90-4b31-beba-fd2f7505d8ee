# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* sales_team
# 
# Translators:
# <PERSON><PERSON>, 2023
# <PERSON><PERSON>, 2023
# <PERSON><PERSON><PERSON>, 2023
# <PERSON><PERSON>, 2024
# <PERSON><PERSON><PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-10-26 21:55+0000\n"
"PO-Revision-Date: 2023-10-26 23:09+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON>, 2024\n"
"Language-Team: Czech (https://app.transifex.com/odoo/teams/41243/cs/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: cs\n"
"Plural-Forms: nplurals=4; plural=(n == 1 && n % 1 == 0) ? 0 : (n >= 2 && n <= 4 && n % 1 == 0) ? 1: (n % 1 != 0 ) ? 2 : 3;\n"

#. module: sales_team
#: model_terms:ir.ui.view,arch_db:sales_team.crm_team_member_view_kanban
#: model_terms:ir.ui.view,arch_db:sales_team.crm_team_view_form
msgid "<i class=\"fa fa-envelope me-1\" role=\"img\" aria-label=\"Email\" title=\"Email\"/>"
msgstr "<i class=\"fa fa-envelope me-1\" role=\"img\" aria-label=\"Email\" title=\"Email\"/>"

#. module: sales_team
#: model_terms:ir.ui.view,arch_db:sales_team.crm_team_member_view_kanban
msgid "<span class=\"text-bg-danger\">Archived</span>"
msgstr "<span class=\"text-bg-danger\">Archivováné</span>"

#. module: sales_team
#: model_terms:ir.ui.view,arch_db:sales_team.crm_team_view_kanban_dashboard
msgid "<span>New</span>"
msgstr "<span>Nový</span>"

#. module: sales_team
#: model_terms:ir.ui.view,arch_db:sales_team.crm_team_view_kanban_dashboard
msgid "<span>Reporting</span>"
msgstr "<span>Výkazy</span>"

#. module: sales_team
#: model_terms:ir.ui.view,arch_db:sales_team.crm_team_view_kanban_dashboard
msgid "<span>View</span>"
msgstr "<span>Zobrazení</span>"

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_team__message_needaction
#: model:ir.model.fields,field_description:sales_team.field_crm_team_member__message_needaction
msgid "Action Needed"
msgstr "Vyžadována akce"

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_team__active
#: model:ir.model.fields,field_description:sales_team.field_crm_team_member__active
msgid "Active"
msgstr "Aktivní"

#. module: sales_team
#: model:ir.actions.act_window,name:sales_team.mail_activity_type_action_config_sales
msgid "Activity Types"
msgstr "Typy aktivit"

#. module: sales_team
#: model_terms:ir.actions.act_window,help:sales_team.crm_team_member_action
msgid "Add a Team Member"
msgstr "Přidat člena týmu"

#. module: sales_team
#: model:ir.model.fields,help:sales_team.field_crm_team__crm_team_member_ids
msgid ""
"Add members to automatically assign their documents to this sales team."
msgstr ""
"Přidejte členy, aby se jejich dokumenty automaticky přiřadily k tomuto "
"prodejnímu týmu."

#. module: sales_team
#. odoo-python
#: code:addons/sales_team/models/crm_team.py:0
#, python-format
msgid ""
"Adding %(user_name)s in this team would remove him/her from its current team"
" %(team_name)s."
msgstr ""
"Přidání %(user_name)sdo tohoto týmu by ho vyřadilo z jeho současného "
"týmu%(team_name)s."

#. module: sales_team
#. odoo-python
#: code:addons/sales_team/models/crm_team_member.py:0
#, python-format
msgid ""
"Adding %(user_name)s in this team would remove him/her from its current "
"teams %(team_names)s."
msgstr ""
"Přidání %(user_name)sdo tohoto týmu by ho vyřadilo z jeho současných "
"týmů%(team_names)s."

#. module: sales_team
#. odoo-python
#: code:addons/sales_team/models/crm_team.py:0
#, python-format
msgid ""
"Adding %(user_names)s in this team would remove them from their current "
"teams (%(team_names)s)."
msgstr ""
"Přidání %(user_names)stohoto týmu by je vyřadilo z jejich současných týmů "
"(%(team_names)s)."

#. module: sales_team
#: model:res.groups,name:sales_team.group_sale_manager
msgid "Administrator"
msgstr "Administrátor"

#. module: sales_team
#: model_terms:ir.ui.view,arch_db:sales_team.crm_team_member_view_form
#: model_terms:ir.ui.view,arch_db:sales_team.crm_team_member_view_search
#: model_terms:ir.ui.view,arch_db:sales_team.crm_team_view_form
#: model_terms:ir.ui.view,arch_db:sales_team.crm_team_view_search
msgid "Archived"
msgstr "Archivováno"

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_team__message_attachment_count
#: model:ir.model.fields,field_description:sales_team.field_crm_team_member__message_attachment_count
msgid "Attachment Count"
msgstr "Počet příloh"

#. module: sales_team
#: model_terms:ir.ui.view,arch_db:sales_team.crm_team_member_view_kanban
#: model_terms:ir.ui.view,arch_db:sales_team.crm_team_view_form
msgid "Avatar"
msgstr "Avatar"

#. module: sales_team
#. odoo-python
#: code:addons/sales_team/models/crm_team.py:0
#, python-format
msgid "Big Pretty Button :)"
msgstr "Velké hezké tlačítko :)"

#. module: sales_team
#: model:ir.model,name:sales_team.model_crm_tag
msgid "CRM Tag"
msgstr "CRM štítek"

#. module: sales_team
#. odoo-python
#: code:addons/sales_team/models/crm_team.py:0
#, python-format
msgid "Cannot delete default team \"%s\""
msgstr "Nelze odstranit výchozí tým \"%s\""

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_tag__color
msgid "Color"
msgstr "Barva"

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_team__color
msgid "Color Index"
msgstr "Barevný index"

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_team__company_id
#: model:ir.model.fields,field_description:sales_team.field_crm_team_member__company_id
#: model_terms:ir.ui.view,arch_db:sales_team.crm_team_view_search
msgid "Company"
msgstr "Společnost"

#. module: sales_team
#: model_terms:ir.ui.view,arch_db:sales_team.crm_team_view_kanban_dashboard
msgid "Configuration"
msgstr "Konfigurace"

#. module: sales_team
#: model:crm.tag,name:sales_team.categ_oppor7
msgid "Consulting"
msgstr "Konzultace"

#. module: sales_team
#: model:ir.model,name:sales_team.model_res_partner
msgid "Contact"
msgstr "Kontakt"

#. module: sales_team
#: model_terms:ir.actions.act_window,help:sales_team.sales_team_crm_tag_action
msgid "Create CRM Tags"
msgstr "Vytvoření štítků CRM"

#. module: sales_team
#: model_terms:ir.actions.act_window,help:sales_team.crm_team_action_config
msgid "Create a Sales Team"
msgstr "Vytvořit prodejní tým"

#. module: sales_team
#: model_terms:ir.actions.act_window,help:sales_team.mail_activity_type_action_config_sales
msgid "Create an Activity Type"
msgstr "Vytvořit typ aktivity"

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_tag__create_uid
#: model:ir.model.fields,field_description:sales_team.field_crm_team__create_uid
#: model:ir.model.fields,field_description:sales_team.field_crm_team_member__create_uid
msgid "Created by"
msgstr "Vytvořeno uživatelem"

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_tag__create_date
#: model:ir.model.fields,field_description:sales_team.field_crm_team__create_date
#: model:ir.model.fields,field_description:sales_team.field_crm_team_member__create_date
msgid "Created on"
msgstr "Vytvořeno dne"

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_team__currency_id
msgid "Currency"
msgstr "Měna"

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_team__dashboard_button_name
msgid "Dashboard Button"
msgstr "Tlačítko dashboardu"

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_team__dashboard_graph_data
msgid "Dashboard Graph Data"
msgstr "Data grafu dashboardu"

#. module: sales_team
#: model_terms:ir.actions.act_window,help:sales_team.crm_team_action_pipeline
#: model_terms:ir.actions.act_window,help:sales_team.crm_team_action_sales
msgid "Define a new sales team"
msgstr "Definujte nový prodejní tým"

#. module: sales_team
#: model:crm.tag,name:sales_team.categ_oppor5
msgid "Design"
msgstr "Návrh"

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_tag__display_name
#: model:ir.model.fields,field_description:sales_team.field_crm_team__display_name
#: model:ir.model.fields,field_description:sales_team.field_crm_team_member__display_name
msgid "Display Name"
msgstr "Zobrazovací název"

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_team_member__email
msgid "Email"
msgstr "Email "

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_team__favorite_user_ids
msgid "Favorite Members"
msgstr "Oblíbení členové"

#. module: sales_team
#: model:ir.model.fields,help:sales_team.field_crm_team__is_favorite
msgid ""
"Favorite teams to display them in the dashboard and access them easily."
msgstr ""
"Oblíbené týmy, které se zobrazují na hlavním panelu a je k nim snadný "
"přístup."

#. module: sales_team
#: model_terms:ir.ui.view,arch_db:sales_team.crm_team_view_form
msgid ""
"Follow this salesteam to automatically track the events associated to users "
"of this team."
msgstr ""
"Odebírejte zprávy obchodního týmu. Automaticky budete mít přehled o "
"událostech spojených s členy tohoto týmu."

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_team__message_follower_ids
#: model:ir.model.fields,field_description:sales_team.field_crm_team_member__message_follower_ids
msgid "Followers"
msgstr "Odběratelé"

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_team__message_partner_ids
#: model:ir.model.fields,field_description:sales_team.field_crm_team_member__message_partner_ids
msgid "Followers (Partners)"
msgstr "Odběratelé (partneři)"

#. module: sales_team
#: model_terms:ir.ui.view,arch_db:sales_team.crm_team_member_view_search
msgid "Group By"
msgstr "Seskupit podle"

#. module: sales_team
#: model_terms:ir.ui.view,arch_db:sales_team.crm_team_view_search
msgid "Group By..."
msgstr "Seskupit podle…"

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_team__has_message
#: model:ir.model.fields,field_description:sales_team.field_crm_team_member__has_message
msgid "Has Message"
msgstr "Má zprávu"

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_tag__id
#: model:ir.model.fields,field_description:sales_team.field_crm_team__id
#: model:ir.model.fields,field_description:sales_team.field_crm_team_member__id
msgid "ID"
msgstr "ID"

#. module: sales_team
#: model:ir.model.fields,help:sales_team.field_crm_team__is_membership_multi
#: model:ir.model.fields,help:sales_team.field_crm_team_member__is_membership_multi
msgid ""
"If True, users may belong to several sales teams. Otherwise membership is "
"limited to a single sales team."
msgstr ""
"Pokud je to pravda, mohou uživatelé patřit do několika prodejních týmů. V "
"opačném případě je členství omezeno na jeden prodejní tým."

#. module: sales_team
#: model:ir.model.fields,help:sales_team.field_crm_team__message_needaction
#: model:ir.model.fields,help:sales_team.field_crm_team_member__message_needaction
msgid "If checked, new messages require your attention."
msgstr "Pokud zaškrtnuto, nové zprávy vyžadují vaši pozornost."

#. module: sales_team
#: model:ir.model.fields,help:sales_team.field_crm_team__message_has_error
#: model:ir.model.fields,help:sales_team.field_crm_team_member__message_has_error
msgid "If checked, some messages have a delivery error."
msgstr "Pokud zaškrtnuto, některé zprávy mají chybu při doručení."

#. module: sales_team
#: model:ir.model.fields,help:sales_team.field_res_partner__team_id
#: model:ir.model.fields,help:sales_team.field_res_users__team_id
msgid ""
"If set, this Sales Team will be used for sales and assignments related to "
"this partner"
msgstr ""
"Pokud je nastaveno, bude tento prodejní tým použit pro prodej a úkoly "
"související s tímto partnerem"

#. module: sales_team
#: model:ir.model.fields,help:sales_team.field_crm_team__active
msgid ""
"If the active field is set to false, it will allow you to hide the Sales "
"Team without removing it."
msgstr ""
"Pokud je aktivní pole nastaveno na hodnotu false, umožní vám to skrýt "
"prodejní tým, aniž byste jej odstranili."

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_team_member__image_1920
msgid "Image"
msgstr "Obrázek"

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_team_member__image_128
msgid "Image (128)"
msgstr "Obrázek (128)"

#. module: sales_team
#: model:crm.tag,name:sales_team.categ_oppor4
msgid "Information"
msgstr "Informace"

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_team__message_is_follower
#: model:ir.model.fields,field_description:sales_team.field_crm_team_member__message_is_follower
msgid "Is Follower"
msgstr "Je odběratel"

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_tag__write_uid
#: model:ir.model.fields,field_description:sales_team.field_crm_team__write_uid
#: model:ir.model.fields,field_description:sales_team.field_crm_team_member__write_uid
msgid "Last Updated by"
msgstr "Naposledy upraveno uživatelem"

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_tag__write_date
#: model:ir.model.fields,field_description:sales_team.field_crm_team__write_date
#: model:ir.model.fields,field_description:sales_team.field_crm_team_member__write_date
msgid "Last Updated on"
msgstr "Naposledy upraveno dne"

#. module: sales_team
#: model:ir.model.fields,help:sales_team.field_res_users__sale_team_id
msgid ""
"Main user sales team. Used notably for pipeline, or to set sales team in "
"invoicing or subscription."
msgstr ""
"Hlavní prodejní tým uživatelů. Používá se zejména pro pipeline nebo pro "
"nastavení prodejního týmu při fakturaci nebo předplatném."

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_team__member_company_ids
msgid "Member Company"
msgstr "Členská firma"

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_team_member__member_warning
msgid "Member Warning"
msgstr "Upozornění pro členy"

#. module: sales_team
#: model_terms:ir.ui.view,arch_db:sales_team.crm_team_view_form
msgid "Members"
msgstr "Členové"

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_team__member_warning
msgid "Membership Issue Warning"
msgstr "Upozornění na problém s členstvím"

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_team__message_has_error
#: model:ir.model.fields,field_description:sales_team.field_crm_team_member__message_has_error
msgid "Message Delivery error"
msgstr "Chyba při doručování zprávy"

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_team__message_ids
#: model:ir.model.fields,field_description:sales_team.field_crm_team_member__message_ids
msgid "Messages"
msgstr "Zprávy"

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_team_member__mobile
msgid "Mobile"
msgstr "Mobil"

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_team__is_membership_multi
#: model:ir.model.fields,field_description:sales_team.field_crm_team_member__is_membership_multi
msgid "Multiple Memberships Allowed"
msgstr "Povolení více členství"

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_team_member__name
msgid "Name"
msgstr "Název"

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_team__message_needaction_counter
#: model:ir.model.fields,field_description:sales_team.field_crm_team_member__message_needaction_counter
msgid "Number of Actions"
msgstr "Počet akcí"

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_team__message_has_error_counter
#: model:ir.model.fields,field_description:sales_team.field_crm_team_member__message_has_error_counter
msgid "Number of errors"
msgstr "Počet chyb"

#. module: sales_team
#: model:ir.model.fields,help:sales_team.field_crm_team__message_needaction_counter
#: model:ir.model.fields,help:sales_team.field_crm_team_member__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "Počet zpráv vyžadujících akci"

#. module: sales_team
#: model:ir.model.fields,help:sales_team.field_crm_team__message_has_error_counter
#: model:ir.model.fields,help:sales_team.field_crm_team_member__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Počet zpráv s chybou při doručení"

#. module: sales_team
#: model:crm.tag,name:sales_team.categ_oppor8
msgid "Other"
msgstr "Ostatní"

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_team_member__phone
msgid "Phone"
msgstr "Telefon"

#. module: sales_team
#: model:crm.team,name:sales_team.pos_sales_team
msgid "Point of Sale"
msgstr "Prodejní místa"

#. module: sales_team
#: model:crm.team,name:sales_team.crm_team_1
msgid "Pre-Sales"
msgstr "Předprodejní služby"

#. module: sales_team
#: model:crm.tag,name:sales_team.categ_oppor1
msgid "Product"
msgstr "Produkt"

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_team__rating_ids
#: model:ir.model.fields,field_description:sales_team.field_crm_team_member__rating_ids
msgid "Ratings"
msgstr "Hodnocení"

#. module: sales_team
#: model:crm.team,name:sales_team.team_sales_department
msgid "Sales"
msgstr "Prodej"

#. module: sales_team
#: model_terms:ir.ui.view,arch_db:sales_team.crm_team_member_view_form
#: model_terms:ir.ui.view,arch_db:sales_team.crm_team_member_view_tree
msgid "Sales Men"
msgstr "Prodejci"

#. module: sales_team
#: model_terms:ir.ui.view,arch_db:sales_team.crm_team_member_view_search
msgid "Sales Person"
msgstr "Obchodník"

#. module: sales_team
#: model:ir.model,name:sales_team.model_crm_team
#: model:ir.model.fields,field_description:sales_team.field_crm_team__name
#: model:ir.model.fields,field_description:sales_team.field_crm_team_member__crm_team_id
#: model:ir.model.fields,field_description:sales_team.field_res_partner__team_id
#: model:ir.model.fields,field_description:sales_team.field_res_users__team_id
#: model_terms:ir.ui.view,arch_db:sales_team.crm_team_member_view_search
#: model_terms:ir.ui.view,arch_db:sales_team.crm_team_view_form
#: model_terms:ir.ui.view,arch_db:sales_team.crm_team_view_tree
msgid "Sales Team"
msgstr "Obchodní tým"

#. module: sales_team
#: model:ir.model,name:sales_team.model_crm_team_member
msgid "Sales Team Member"
msgstr "Člen obchodního týmu"

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_team__crm_team_member_ids
#: model:ir.model.fields,field_description:sales_team.field_res_users__crm_team_member_ids
msgid "Sales Team Members"
msgstr "Členové prodejního týmu"

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_team__crm_team_member_all_ids
msgid "Sales Team Members (incl. inactive)"
msgstr "Členové prodejního týmu (včetně neaktivních)"

#. module: sales_team
#: model:ir.actions.act_window,name:sales_team.crm_team_action_config
#: model:ir.actions.act_window,name:sales_team.crm_team_action_sales
#: model:ir.model.fields,field_description:sales_team.field_res_users__crm_team_ids
msgid "Sales Teams"
msgstr "Obchodní týmy"

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_team_member__user_id
msgid "Salesperson"
msgstr "Obchodník"

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_team__member_ids
msgid "Salespersons"
msgstr "Obchodníci"

#. module: sales_team
#: model_terms:ir.ui.view,arch_db:sales_team.crm_team_view_search
msgid "Salesteams Search"
msgstr "Vyhledávání prodejních týmů"

#. module: sales_team
#. odoo-python
#: code:addons/sales_team/models/crm_team.py:0
#, python-format
msgid "Sample data"
msgstr "Vzorek dat"

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_team__sequence
msgid "Sequence"
msgstr "Sekvence"

#. module: sales_team
#: model:crm.tag,name:sales_team.categ_oppor3
msgid "Services"
msgstr "Servisní služby"

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_team__is_favorite
msgid "Show on dashboard"
msgstr "Zobrazit na dashboardu"

#. module: sales_team
#: model:crm.tag,name:sales_team.categ_oppor2
msgid "Software"
msgstr "Software"

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_tag__name
msgid "Tag Name"
msgstr "Název štítku"

#. module: sales_team
#: model:ir.model.constraint,message:sales_team.constraint_crm_tag_name_uniq
msgid "Tag name already exists!"
msgstr "Název značky již existuje!"

#. module: sales_team
#: model:ir.actions.act_window,name:sales_team.sales_team_crm_tag_action
#: model_terms:ir.ui.view,arch_db:sales_team.sales_team_crm_tag_view_form
#: model_terms:ir.ui.view,arch_db:sales_team.sales_team_crm_tag_view_tree
msgid "Tags"
msgstr "Štítky"

#. module: sales_team
#: model_terms:ir.ui.view,arch_db:sales_team.crm_team_view_form
msgid "Team Details"
msgstr "Informace o týmu"

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_team__user_id
#: model_terms:ir.ui.view,arch_db:sales_team.crm_team_view_search
msgid "Team Leader"
msgstr "Vedoucí týmu"

#. module: sales_team
#: model:ir.actions.act_window,name:sales_team.crm_team_member_action
msgid "Team Members"
msgstr "Členové týmu"

#. module: sales_team
#: model_terms:ir.actions.act_window,help:sales_team.crm_team_member_action
msgid "Team Members are salespersons assigned to specific teams."
msgstr "Členové týmu jsou prodejci přiřazení k určitým týmům."

#. module: sales_team
#: model:ir.actions.act_window,name:sales_team.crm_team_action_pipeline
msgid "Teams"
msgstr "Týmy"

#. module: sales_team
#: model:ir.model.fields,help:sales_team.field_crm_team__color
msgid "The color of the channel"
msgstr "Barva kanálu"

#. module: sales_team
#: model:ir.model.fields,help:sales_team.field_crm_team_member__company_id
msgid "The default company for this user."
msgstr "Výchozí společnost pro tohoto uživatele."

#. module: sales_team
#: model_terms:ir.actions.act_window,help:sales_team.mail_activity_type_action_config_sales
msgid ""
"Those represent the different categories of things you have to do (e.g. "
"\"Call\" or \"Prepare meeting\")."
msgstr ""
"Ty představují různé kategorie věcí, které musíte udělat (např. \"Volat\" "
"nebo \"Připravit schůzku\")."

#. module: sales_team
#. odoo-python
#: code:addons/sales_team/models/crm_team.py:0
#, python-format
msgid ""
"To add a Salesperson into multiple Teams, activate the Multi-Team option in "
"settings."
msgstr ""
"Chcete-li přidat prodejce do více týmů, aktivujte v nastavení možnost Více "
"týmů."

#. module: sales_team
#: model:crm.tag,name:sales_team.categ_oppor6
msgid "Training"
msgstr "Tréning"

#. module: sales_team
#: model:ir.model.fields,help:sales_team.field_crm_team_member__user_in_teams_ids
msgid ""
"UX: Give users not to add in the currently chosen team to avoid duplicates"
msgstr ""
"UX: Dejte uživatelům možnost nepřidávat do aktuálně zvoleného týmu, aby se "
"zabránilo duplicitám."

#. module: sales_team
#: model:ir.model.fields,help:sales_team.field_crm_team__member_company_ids
#: model:ir.model.fields,help:sales_team.field_crm_team_member__user_company_ids
msgid "UX: Limit to team company or all if no company"
msgstr ""
"UX: Omezit na týmovou společnost nebo na všechny, pokud žádná společnost "
"neexistuje"

#. module: sales_team
#. odoo-python
#: code:addons/sales_team/models/crm_team.py:0
#: code:addons/sales_team/models/crm_team.py:0
#, python-format
msgid "Undefined graph model for Sales Team: %s"
msgstr "Nedefinovaný grafický model pro prodejní tým:%s"

#. module: sales_team
#: model_terms:ir.actions.act_window,help:sales_team.crm_team_action_config
msgid ""
"Use Sales Teams to organize your sales departments and draw up reports."
msgstr ""
"Pomocí prodejních týmů můžete organizovat svá prodejní oddělení a sestavovat"
" přehledy."

#. module: sales_team
#: model_terms:ir.actions.act_window,help:sales_team.crm_team_action_pipeline
#: model_terms:ir.actions.act_window,help:sales_team.crm_team_action_sales
msgid ""
"Use Sales Teams to organize your sales departments.\n"
"                Each team will work with a separate pipeline."
msgstr ""
"Pomocí prodejních týmů můžete organizovat svá prodejní oddělení.\n"
"Každý tým bude pracovat se samostatným prodejním plánem."

#. module: sales_team
#: model_terms:ir.actions.act_window,help:sales_team.sales_team_crm_tag_action
msgid ""
"Use Tags to manage and track your Opportunities (product structure, sales "
"type, ...)"
msgstr ""
"Pomocí štítků můžete spravovat a sledovat příležitosti (struktura produktu, "
"typ prodeje, ...)."

#. module: sales_team
#: model:ir.model,name:sales_team.model_res_users
msgid "User"
msgstr "Uživatel"

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_team_member__user_company_ids
msgid "User Company"
msgstr "Společnost uživatele"

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_team_member__user_in_teams_ids
msgid "User In Teams"
msgstr "Uživatel v týmech"

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_res_users__sale_team_id
msgid "User Sales Team"
msgstr "Obchodní tým uživatelů"

#. module: sales_team
#: model:res.groups,name:sales_team.group_sale_salesman_all_leads
msgid "User: All Documents"
msgstr "Uživatel: Všechny dokumenty"

#. module: sales_team
#: model:res.groups,name:sales_team.group_sale_salesman
msgid "User: Own Documents Only"
msgstr "Uživatel: Pouze vlastní dokumenty"

#. module: sales_team
#: model:ir.model.fields,help:sales_team.field_crm_team__member_ids
msgid "Users assigned to this team."
msgstr "Uživatelé přiřazení k tomuto týmu."

#. module: sales_team
#: model:crm.team,name:sales_team.salesteam_website_sales
msgid "Website"
msgstr "Webová stránka"

#. module: sales_team
#. odoo-python
#: code:addons/sales_team/models/crm_team_member.py:0
#, python-format
msgid ""
"You are trying to create duplicate membership(s). We found that "
"%(duplicates)s already exist(s)."
msgstr ""
"Snažíte se vytvořit duplicitní členství. Zjistili jsme, že%(duplicates)s již"
" členství existuje/existují."

#. module: sales_team
#: model_terms:ir.ui.view,arch_db:sales_team.crm_team_view_form
msgid "e.g. North America"
msgstr "např. Severní Amerika"

#. module: sales_team
#: model_terms:ir.ui.view,arch_db:sales_team.sales_team_crm_tag_view_form
msgid "e.g. Services"
msgstr "např. služby"

#. module: sales_team
#: model:crm.team,name:sales_team.ebay_sales_team
msgid "eBay"
msgstr "eBay"

#. module: sales_team
#: model:res.groups,comment:sales_team.group_sale_salesman_all_leads
msgid ""
"the user will have access to all records of everyone in the sales "
"application."
msgstr ""
"uživatel bude mít přístup ke všem záznamům kohokoli v aplikaci prodejů."

#. module: sales_team
#: model:res.groups,comment:sales_team.group_sale_salesman
msgid "the user will have access to his own data in the sales application."
msgstr "uživatel bude mít přístup ke svým datum v aplikaci prodejů."

#. module: sales_team
#: model:res.groups,comment:sales_team.group_sale_manager
msgid ""
"the user will have an access to the sales configuration as well as statistic"
" reports."
msgstr ""
"uživatel bude mít přístup k nastavení prodeje stejně jako ke statistickým "
"zprávám"
