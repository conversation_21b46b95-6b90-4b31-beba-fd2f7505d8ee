<?xml version="1.0" encoding="utf-8"?>
<odoo auto_sequence="1">
    <record id="tax_annual_report_vat_ve" model="account.report">
        <field name="name">VE VAT Report</field>
        <field name="sequence">1</field>
        <field name="country_id" ref="base.it"/>
        <field name="availability_condition">country</field>
        <field name="column_ids">
            <record id="tax_annual_report_vat_balance_ve" model="account.report.column">
                <field name="name">Balance</field>
                <field name="expression_label">balance</field>
            </record>
            <record id="tax_annual_report_vat_tax_ve" model="account.report.column">
                <field name="name">Tax</field>
                <field name="expression_label">tax</field>
            </record>
        </field>
        <field name="line_ids">
            <record id="tax_annual_report_line_turnover" model="account.report.line">
                <field name="name">Turnover</field>
                <field name="code">VE</field>
                <field name="children_ids">
                    <record id="tax_annual_report_line_turnover_section_1" model="account.report.line">
                        <field name="name">Contributions of agricultural products and transfers from exempt farmers (in case of exceeding 1/3</field>
                        <field name="code">VE_section1</field>
                        <field name="foldable" eval="True"/>
                        <field name="children_ids">
                            <record id="tax_annual_report_line_ve1" model="account.report.line">
                                <field name="name">VE1 - Transfers to cooperatives art.34 paragraph 2 with compensation percentage 2%</field>
                                <field name="code">VE1</field>
                                <field name="expression_ids">
                                    <record id="tax_annual_report_line_ve1_tag" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">ve1</field>
                                    </record>
                                    <record id="tax_annual_report_line_ve1_tax" model="account.report.expression">
                                        <field name="label">tax</field>
                                        <field name="engine">aggregation</field>
                                        <field name="formula">VE1.balance * 0.02</field>
                                    </record>
                                </field>
                            </record>
                            <record id="tax_annual_report_line_ve2" model="account.report.line">
                                <field name="name">VE2 - Transfers to cooperatives art.34 paragraph 2 with compensation percentage 4%</field>
                                <field name="code">VE2</field>
                                <field name="expression_ids">
                                    <record id="tax_annual_report_line_ve2_tag" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">ve2</field>
                                    </record>
                                    <record id="tax_annual_report_line_ve2_tax" model="account.report.expression">
                                        <field name="label">tax</field>
                                        <field name="engine">aggregation</field>
                                        <field name="formula">VE2.balance * 0.04</field>
                                    </record>
                                </field>
                            </record>
                            <record id="tax_annual_report_line_ve3" model="account.report.line">
                                <field name="name">VE3 - Transfers to cooperatives art.34 paragraph 2 with compensation percentage 6,4%</field>
                                <field name="code">VE3</field>
                                <field name="expression_ids">
                                    <record id="tax_annual_report_line_ve3_tag" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">ve3</field>
                                    </record>
                                    <record id="tax_annual_report_line_ve3_tax" model="account.report.expression">
                                        <field name="label">tax</field>
                                        <field name="engine">aggregation</field>
                                        <field name="formula">VE3.balance * 0.064</field>
                                    </record>
                                </field>
                            </record>
                            <record id="tax_annual_report_line_ve4" model="account.report.line">
                                <field name="name">VE4 - Transfers to cooperatives art.34 paragraph 2 with compensation percentage 7,3%</field>
                                <field name="code">VE4</field>
                                <field name="expression_ids">
                                    <record id="tax_annual_report_line_ve4_tag" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">ve4</field>
                                    </record>
                                    <record id="tax_annual_report_line_ve4_tax" model="account.report.expression">
                                        <field name="label">tax</field>
                                        <field name="engine">aggregation</field>
                                        <field name="formula">VE4.balance * 0.07</field>
                                    </record>
                                </field>
                            </record>
                            <record id="tax_annual_report_line_ve5" model="account.report.line">
                                <field name="name">VE5 - Transfers to cooperatives art.34 paragraph 2 with compensation percentage 7,5%</field>
                                <field name="code">VE5</field>
                                <field name="expression_ids">
                                    <record id="tax_annual_report_line_ve5_tag" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">ve5</field>
                                    </record>
                                    <record id="tax_annual_report_line_ve5_tax" model="account.report.expression">
                                        <field name="label">tax</field>
                                        <field name="engine">aggregation</field>
                                        <field name="formula">VE5.balance * 0.073</field>
                                    </record>
                                </field>
                            </record>
                            <record id="tax_annual_report_line_ve6" model="account.report.line">
                                <field name="name">VE6 - Transfers to cooperatives art.34 paragraph 2 with compensation percentage 8,3%</field>
                                <field name="code">VE6</field>
                                <field name="expression_ids">
                                    <record id="tax_annual_report_line_ve6_tag" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">ve6</field>
                                    </record>
                                    <record id="tax_annual_report_line_ve6_tax" model="account.report.expression">
                                        <field name="label">tax</field>
                                        <field name="engine">aggregation</field>
                                        <field name="formula">VE6.balance * 0.075</field>
                                    </record>
                                </field>
                            </record>
                            <record id="tax_annual_report_line_ve7" model="account.report.line">
                                <field name="name">VE7 - Transfers to cooperatives art.34 paragraph 2 with compensation percentage 8,5%</field>
                                <field name="code">VE7</field>
                                <field name="expression_ids">
                                    <record id="tax_annual_report_line_ve7_tag" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">ve7</field>
                                    </record>
                                    <record id="tax_annual_report_line_ve7_tax" model="account.report.expression">
                                        <field name="label">tax</field>
                                        <field name="engine">aggregation</field>
                                        <field name="formula">VE7.balance * 0.083</field>
                                    </record>
                                </field>
                            </record>
                            <record id="tax_annual_report_line_ve8" model="account.report.line">
                                <field name="name">VE8 - Transfers to cooperatives art.34 paragraph 2 with compensation percentage 8,8%</field>
                                <field name="code">VE8</field>
                                <field name="expression_ids">
                                    <record id="tax_annual_report_line_ve8_tag" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">ve8</field>
                                    </record>
                                    <record id="tax_annual_report_line_ve8_tax" model="account.report.expression">
                                        <field name="label">tax</field>
                                        <field name="engine">aggregation</field>
                                        <field name="formula">VE8.balance * 0.085</field>
                                    </record>
                                </field>
                            </record>
                            <record id="tax_annual_report_line_ve9" model="account.report.line">
                                <field name="name">VE9 - Transfers to cooperatives art.34 paragraph 2 with compensation percentage 9,5%</field>
                                <field name="code">VE9</field>
                                <field name="expression_ids">
                                    <record id="tax_annual_report_line_ve9_tag" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">ve9</field>
                                    </record>
                                    <record id="tax_annual_report_line_ve9_tax" model="account.report.expression">
                                        <field name="label">tax</field>
                                        <field name="engine">aggregation</field>
                                        <field name="formula">VE9.balance * 0.088</field>
                                    </record>
                                </field>
                            </record>
                            <record id="tax_annual_report_line_ve10" model="account.report.line">
                                <field name="name">VE10 - Transfers to cooperatives art.34 paragraph 2 with compensation percentage 10%</field>
                                <field name="code">VE10</field>
                                <field name="expression_ids">
                                    <record id="tax_annual_report_line_ve10_tag" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">ve10</field>
                                    </record>
                                    <record id="tax_annual_report_line_ve10_tax" model="account.report.expression">
                                        <field name="label">tax</field>
                                        <field name="engine">aggregation</field>
                                        <field name="formula">VE10.balance * 0.10</field>
                                    </record>
                                </field>
                            </record>
                            <record id="tax_annual_report_line_ve11" model="account.report.line">
                                <field name="name">VE11 - Transfers to cooperatives art.34 paragraph 2 with compensation percentage 12,3%</field>
                                <field name="code">VE11</field>
                                <field name="expression_ids">
                                    <record id="tax_annual_report_line_ve11_tag" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">ve11</field>
                                    </record>
                                    <record id="tax_annual_report_line_ve11_tax" model="account.report.expression">
                                        <field name="label">tax</field>
                                        <field name="engine">aggregation</field>
                                        <field name="formula">VE11.balance * 0.123</field>
                                    </record>
                                </field>
                            </record>
                        </field>
                    </record>
                    <record id="tax_annual_report_line_turnover_section_2" model="account.report.line">
                        <field name="name">Agricultural taxable transactions (Article 34 paragraph 1) and commercial and professional taxable transactions</field>
                        <field name="code">VE_section2</field>
                        <field name="foldable" eval="True"/>
                        <field name="children_ids">
                            <record id="tax_annual_report_line_ve20" model="account.report.line">
                                <field name="name">VE20 - Taxable transactions rate 4%</field>
                                <field name="code">VE20</field>
                                <field name="expression_ids">
                                    <record id="tax_annual_report_line_ve20_tag" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">ve20</field>
                                    </record>
                                    <record id="tax_annual_report_line_ve20_tax" model="account.report.expression">
                                        <field name="label">tax</field>
                                        <field name="engine">aggregation</field>
                                        <field name="formula">VE20.balance * 0.4</field>
                                    </record>
                                </field>
                            </record>
                            <record id="tax_annual_report_line_ve21" model="account.report.line">
                                <field name="name">VE21 - Taxable transactions rate 5%</field>
                                <field name="code">VE21</field>
                                <field name="expression_ids">
                                    <record id="tax_annual_report_line_ve21_tag" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">ve21</field>
                                    </record>
                                    <record id="tax_annual_report_line_ve21_tax" model="account.report.expression">
                                        <field name="label">tax</field>
                                        <field name="engine">aggregation</field>
                                        <field name="formula">VE21.balance * 0.5</field>
                                    </record>
                                </field>
                            </record>
                            <record id="tax_annual_report_line_ve22" model="account.report.line">
                                <field name="name">VE22 - Taxable transactions rate 10%</field>
                                <field name="code">VE22</field>
                                <field name="expression_ids">
                                    <record id="tax_annual_report_line_ve22_tag" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">ve22</field>
                                    </record>
                                    <record id="tax_annual_report_line_ve22_tax" model="account.report.expression">
                                        <field name="label">tax</field>
                                        <field name="engine">aggregation</field>
                                        <field name="formula">VE22.balance * 0.10</field>
                                    </record>
                                </field>
                            </record>
                            <record id="tax_annual_report_line_ve23" model="account.report.line">
                                <field name="name">VE23 - Taxable transactions rate 22%</field>
                                <field name="code">VE23</field>
                                <field name="expression_ids">
                                    <record id="tax_annual_report_line_ve23_tag" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">ve23</field>
                                    </record>
                                    <record id="tax_annual_report_line_ve23_tax" model="account.report.expression">
                                        <field name="label">tax</field>
                                        <field name="engine">aggregation</field>
                                        <field name="formula">VE23.balance * 0.22</field>
                                    </record>
                                </field>
                            </record>
                        </field>
                    </record>
                    <record id="tax_annual_report_line_turnover_section_3" model="account.report.line">
                        <field name="name">Total taxable income and tax</field>
                        <field name="code">VE_section3</field>
                        <field name="foldable" eval="True"/>
                        <field name="children_ids">
                            <record id="tax_annual_report_line_ve24" model="account.report.line">
                                <field name="name">VE24 - Total lines VE1 to VE11 and lines VE20 to VE23</field>
                                <field name="code">VE24</field>
                                <field name="aggregation_formula">
                                            VE1.balance + VE2.balance + VE3.balance + VE4.balance + VE5.balance
                                            + VE6.balance + VE7.balance + VE8.balance + VE9.balance + VE10.balance
                                            + VE11.balance + VE20.balance + VE21.balance + VE22.balance + VE23.balance
                                </field>
                                <field name="expression_ids">
                                    <record id="tax_annual_report_line_ve24_tax" model="account.report.expression">
                                        <field name="label">tax</field>
                                        <field name="engine">aggregation</field>
                                        <field name="formula">
                                            VE1.tax + VE2.tax + VE3.tax + VE4.tax + VE5.tax
                                            + VE6.tax + VE7.tax + VE8.tax + VE9.tax + VE10.tax
                                            + VE11.tax + VE20.tax + VE21.tax + VE22.tax + VE23.tax
                                        </field>
                                    </record>
                                </field>
                            </record>
                            <record id="tax_annual_report_line_ve25" model="account.report.line">
                                <field name="name">VE25 - Variations and rounding (use &#43;/&#8722; sign)</field>
                                <field name="code">VE25</field>
                                <field name="tax_tags_formula">ve25</field>
                            </record>
                            <record id="tax_annual_report_line_ve26" model="account.report.line">
                                <field name="name">VE26 - Total VE24 and VE25</field>
                                <field name="code">VE26</field>
                                <field name="aggregation_formula">VE24.balance + VE25.balance</field>
                            </record>
                        </field>
                    </record>
                    <record id="tax_annual_report_line_turnover_section_4" model="account.report.line">
                        <field name="name">Other operations</field>
                        <field name="code">VE_section4</field>
                        <field name="foldable" eval="True"/>
                        <field name="children_ids">
                            <record id="tax_annual_report_line_ve30" model="account.report.line">
                                <field name="name">VE30 - Transactions that contribute to the formation of the ceiling</field>
                                <field name="code">VE30</field>
                                <field name="children_ids">
                                    <record id="tax_annual_report_line_ve30_I" model="account.report.line">
                                        <field name="name">VE30_I - Total</field>
                                        <field name="code">VE30_I</field>
                                        <field name="aggregation_formula">VE30_II.balance + VE30_III.balance + VE30_IV.balance + VE30_V.balance</field>
                                    </record>
                                    <record id="tax_annual_report_line_ve30_ii" model="account.report.line">
                                        <field name="name">VE30_II - Exports</field>
                                        <field name="code">VE30_II</field>
                                        <field name="tax_tags_formula">ve30_ii</field>
                                    </record>
                                    <record id="tax_annual_report_line_ve30_iii" model="account.report.line">
                                        <field name="name">VE30_III - Intra-Community supplies</field>
                                        <field name="code">VE30_III</field>
                                        <field name="tax_tags_formula">ve30_iii</field>
                                    </record>
                                    <record id="tax_annual_report_line_ve30_iv" model="account.report.line">
                                        <field name="name">VE30_IV - Transfers to San Marino</field>
                                        <field name="code">VE30_IV</field>
                                        <field name="tax_tags_formula">ve30_iv</field>
                                    </record>
                                    <record id="tax_annual_report_line_ve30_v" model="account.report.line">
                                        <field name="name">VE30_V - Assimilated operations</field>
                                        <field name="code">VE30_V</field>
                                        <field name="tax_tags_formula">ve30_v</field>
                                    </record>
                                </field>
                            </record>
                            <record id="tax_annual_report_line_ve31" model="account.report.line">
                                <field name="name">VE31 - Non-taxable transactions as a result of declarations of intent</field>
                                <field name="code">VE31</field>
                                <field name="tax_tags_formula">ve31</field>
                            </record>
                            <record id="tax_annual_report_line_ve32" model="account.report.line">
                                <field name="name">VE32 - Other non-taxable transactions</field>
                                <field name="code">VE32</field>
                                <field name="tax_tags_formula">ve32</field>
                            </record>
                            <record id="tax_annual_report_line_ve33" model="account.report.line">
                                <field name="name">VE33 - Exempt transactions (art.10</field>
                                <field name="code">VE33</field>
                                <field name="tax_tags_formula">ve33</field>
                            </record>
                            <record id="tax_annual_report_line_ve34" model="account.report.line">
                                <field name="name">VE34 - Transactions not subject to the tax under Articles 7 to 7-septies</field>
                                <field name="code">VE34</field>
                                <field name="tax_tags_formula">ve34</field>
                            </record>
                            <record id="tax_annual_report_line_ve35" model="account.report.line">
                                <field name="name">VE35 - Transactions with application of internal reverse charge</field>
                                <field name="code">VE35</field>
                                <field name="children_ids">
                                    <record id="tax_annual_report_line_ve35_I" model="account.report.line">
                                        <field name="name">VE35_I - Total</field>
                                        <field name="code">VE35_I</field>
                                        <field name="aggregation_formula">
                                            VE35_II.balance + VE35_III.balance + VE35_IV.balance
                                            + VE35_V.balance + VE35_VI.balance + VE35_VII.balance
                                            + VE35_VIII.balance + VE35_IX.balance
                                        </field>
                                    </record>
                                    <record id="tax_annual_report_line_ve35_ii" model="account.report.line">
                                        <field name="name">VE35_II - Disposal of scrap and other recovered materials</field>
                                        <field name="code">VE35_II</field>
                                        <field name="tax_tags_formula">ve35_ii</field>
                                    </record>
                                    <record id="tax_annual_report_line_ve35_iii" model="account.report.line">
                                        <field name="name">VE35_III - Disposals of pure gold and silver</field>
                                        <field name="code">VE35_III</field>
                                        <field name="tax_tags_formula">ve35_iii</field>
                                    </record>
                                    <record id="tax_annual_report_line_ve35_iv" model="account.report.line">
                                        <field name="name">VE35_IV - Subcontracting in the construction industry</field>
                                        <field name="code">VE35_IV</field>
                                        <field name="tax_tags_formula">ve35_iv</field>
                                    </record>
                                    <record id="tax_annual_report_line_ve35_v" model="account.report.line">
                                        <field name="name">VE35_V - Disposal of capital buildings</field>
                                        <field name="code">VE35_V</field>
                                        <field name="tax_tags_formula">ve35_v</field>
                                    </record>
                                    <record id="tax_annual_report_line_ve35_vi" model="account.report.line">
                                        <field name="name">VE35_VI - Disposal of cell phones</field>
                                        <field name="code">VE35_VI</field>
                                        <field name="tax_tags_formula">ve35_vi</field>
                                    </record>
                                    <record id="tax_annual_report_line_ve35_vii" model="account.report.line">
                                        <field name="name">VE35_VII - Disposal of electronic products</field>
                                        <field name="code">VE35_VII</field>
                                        <field name="tax_tags_formula">ve35_vii</field>
                                    </record>
                                    <record id="tax_annual_report_line_ve35_viii" model="account.report.line">
                                        <field name="name">VE35_VIII - Benefits construction and related industries</field>
                                        <field name="code">VE35_VIII</field>
                                        <field name="tax_tags_formula">ve35_viii</field>
                                    </record>
                                    <record id="tax_annual_report_line_ve35_ix" model="account.report.line">
                                        <field name="name">VE35_IX - Energy sector operations</field>
                                        <field name="code">VE35_IX</field>
                                        <field name="tax_tags_formula">ve35_ix</field>
                                    </record>
                                </field>
                            </record>
                            <record id="tax_annual_report_line_ve36" model="account.report.line">
                                <field name="name">VE36 - Non-taxable transactions made to earthquake victims</field>
                                <field name="code">VE36</field>
                                <field name="tax_tags_formula">ve36</field>
                            </record>
                            <record id="tax_annual_report_line_ve37" model="account.report.line">
                                <field name="name">VE37 - Transactions made during the year but with tax due in subsequent years</field>
                                <field name="code">VE37</field>
                                <field name="children_ids">
                                    <record id="tax_annual_report_line_ve37_I" model="account.report.line">
                                        <field name="name">VE37_I - Total</field>
                                        <field name="code">VE37_I</field>
                                        <field name="tax_tags_formula">ve37_i</field>
                                    </record>
                                    <record id="tax_annual_report_line_ve37_ii" model="account.report.line">
                                        <field name="name">VE37_II - ex art. 32-bis, DL no. 83/2012</field>
                                        <field name="code">VE37_II</field>
                                        <field name="tax_tags_formula">ve37_ii</field>
                                    </record>
                                </field>
                            </record>
                            <record id="tax_annual_report_line_ve38" model="account.report.line">
                                <field name="name">VE38 - Transactions with parties referred to in Article 17-ter.</field>
                                <field name="code">VE38</field>
                                <field name="tax_tags_formula">ve38</field>
                            </record>
                            <record id="tax_annual_report_line_ve39" model="account.report.line">
                                <field name="name">VE39 - (minus) Transactions made in previous years but with tax due in 2022</field>
                                <field name="code">VE39</field>
                                <field name="tax_tags_formula">ve39</field>
                            </record>
                            <record id="tax_annual_report_line_ve40" model="account.report.line">
                                <field name="name">VE40 - (minus) Disposals of depreciable assets and internal transfers.</field>
                                <field name="code">VE40</field>
                                <field name="tax_tags_formula">ve40</field>
                            </record>
                            <record id="tax_annual_report_line_ve45" model="account.report.line">
                                <field name="name">VE50 - TURNOVER</field>
                                <field name="code">VE50</field>
                                <field name="aggregation_formula">
                                    VE24.balance + VE30_I.balance + VE31.balance
                                    + VE32.balance + VE33.balance + VE34.balance
                                    + VE35_I.balance + VE36.balance + VE37_I.balance
                                    + VE37_II.balance + VE38.balance
                                </field>
                            </record>
                        </field>
                    </record>
                </field>
            </record>
        </field>
    </record>
</odoo>
