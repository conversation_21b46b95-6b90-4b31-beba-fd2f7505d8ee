<?xml version="1.0" encoding="utf-8"?>
<odoo auto_sequence="1">
    <record id="tax_report" model="account.report">
        <field name="name">Tax Report</field>
        <field name="root_report_id" ref="account.generic_tax_report"/>
        <field name="country_id" ref="base.do"/>
        <field name="filter_fiscal_position" eval="True"/>
        <field name="availability_condition">country</field>
        <field name="column_ids">
            <record id="tax_report_balance" model="account.report.column">
                <field name="name">Balance</field>
                <field name="expression_label">balance</field>
            </record>
        </field>
        <field name="line_ids">
            <record id="account_tax_report_total_operaciones" model="account.report.line">
                <field name="name">II-1 TOTAL OPERATIONS FOR THE PERIOD</field>
                <field name="aggregation_formula">IIA_UNTAXED.balance + IIB_TAXED.balance</field>
                <field name="hierarchy_level">0</field>
                <field name="children_ids">
                    <record id="account_tax_report_2A" model="account.report.line">
                        <field name="name">II.A NON-TAXABLE</field>
                        <field name="code">IIA_UNTAXED</field>
                        <field name="aggregation_formula">II_A_2.balance + II_A_3.balance + II_A_4.balance + II_A_5.balance + II_A_6.balance + II_A_7.balance + II_A_8.balance</field>
                        <field name="children_ids">
                            <record id="account_tax_report_2A2_base" model="account.report.line">
                                <field name="name">II.A.2 INCOME FROM EXPORTS OF GOODS</field>
                                <field name="code">II_A_2</field>
                                <field name="expression_ids">
                                    <record id="account_tax_report_2A2_base_tag" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">base.good.export</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_tax_report_2A3_base" model="account.report.line">
                                <field name="name">II.A.3 INCOME FROM SERVICES EXPORTS</field>
                                <field name="code">II_A_3</field>
                                <field name="expression_ids">
                                    <record id="account_tax_report_2A3_base_tag" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">base.service.export</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_tax_report_2A4_base" model="account.report.line">
                                <field name="name">II.A.4 INCOME FROM LOCAL SALES OF EXEMPT GOODS OR SERVICES</field>
                                <field name="code">II_A_4</field>
                                <field name="expression_ids">
                                    <record id="account_tax_report_2A4_base_tag" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">base.untaxed.sales.services</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_tax_report_2A5_base" model="account.report.line">
                                <field name="name">II.A.5 INCOME FROM SALES OF EXEMPT GOODS OR SERVICES BY DESTINATION</field>
                                <field name="code">II_A_5</field>
                                <field name="expression_ids">
                                    <record id="account_tax_report_2A5_base_tag" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">base.untaxed.sales.destination</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_tax_report_2A6_base" model="account.report.line">
                                <field name="name">II.A.6 NOT SUBJECT TO ITBIS FOR CONSTRUCTION SERVICES</field>
                                <field name="code">II_A_6</field>
                                <field name="expression_ids">
                                    <record id="account_tax_report_2A6_base_tag" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">base.untaxed.construction.service</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_tax_report_2A7_base" model="account.report.line">
                                <field name="name">II.A.7 NOT SUBJECT TO ITBIS FOR COMMISSIONS</field>
                                <field name="code">II_A_7</field>
                                <field name="expression_ids">
                                    <record id="account_tax_report_2A7_base_tag" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">base.untaxed.commissions</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_tax_report_2A8_base" model="account.report.line">
                                <field name="name">II.A.8 INCOME FROM LOCAL SALES OF EXEMPT GOODS</field>
                                <field name="code">II_A_8</field>
                                <field name="expression_ids">
                                    <record id="account_tax_report_2A8_base_tag" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">base.untaxed.sales</field>
                                    </record>
                                </field>
                            </record>
                        </field>
                    </record>
                    <record id="account_tax_report_2B" model="account.report.line">
                        <field name="name">II.B. TAXED</field>
                        <field name="code">IIB_TAXED</field>
                        <field name="aggregation_formula">II_B_11.balance + II_B_12.balance + II_B_13.balance + II_B_14.balance + II_B_15.balance</field>
                        <field name="children_ids">
                            <record id="account_tax_report_2B11_base" model="account.report.line">
                                <field name="name">II.B.11 TRANSACTIONS TAXED AT 18%</field>
                                <field name="code">II_B_11</field>
                                <field name="expression_ids">
                                    <record id="account_tax_report_2B11_base_tag" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">base.18%</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_tax_report_2B12_base" model="account.report.line">
                                <field name="name">II.B.12 TRANSACTIONS TAXED AT 16%</field>
                                <field name="code">II_B_12</field>
                                <field name="expression_ids">
                                    <record id="account_tax_report_2B12_base_tag" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">base.16%</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_tax_report_2B13_base" model="account.report.line">
                                <field name="name">II.B.13 TRANSACTIONS TAXED AT 9%</field>
                                <field name="code">II_B_13</field>
                                <field name="expression_ids">
                                    <record id="account_tax_report_2B13_base_tag" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">base.9%</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_tax_report_2B14_base" model="account.report.line">
                                <field name="name">II.B.13 TRANSACTIONS TAXED AT 8%</field>
                                <field name="code">II_B_14</field>
                                <field name="expression_ids">
                                    <record id="account_tax_report_2B14_base_tag" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">base.8%</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_tax_report_2B15_base" model="account.report.line">
                                <field name="name">II.B.15 TRANSACTIONS TAXED ON SALES OF DEPRECIABLE ASSETS</field>
                                <field name="code">II_B_15</field>
                                <field name="expression_ids">
                                    <record id="account_tax_report_2B15_base_tag" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">base.depreciable</field>
                                    </record>
                                </field>
                            </record>
                        </field>
                    </record>
                </field>
            </record>
            <record id="account_tax_report_3" model="account.report.line">
                <field name="name">III SETTLEMENT</field>
                <field name="aggregation_formula">III_21.balance + III_25.balance</field>
                <field name="hierarchy_level">0</field>
                <field name="children_ids">
                    <record id="account_tax_report_3_21_total" model="account.report.line">
                        <field name="name">III.21 TOTAL ITBIS COLLECTED (Add Boxes 16+17+18+19+20)</field>
                        <field name="code">III_21</field>
                        <field name="aggregation_formula">III_16.balance + III_17.balance + III_18.balance + III_19.balance + III_20.balance</field>
                        <field name="children_ids">
                            <record id="account_tax_report_3_16_tax" model="account.report.line">
                                <field name="name">III.16 ITBIS COLLECTED (18% of box 11)</field>
                                <field name="code">III_16</field>
                                <field name="expression_ids">
                                    <record id="account_tax_report_3_16_tax_tag" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">tax.18%</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_tax_report_3_17_tax" model="account.report.line">
                                <field name="name">III.17 ITBIS COLLECTED (16% of box 12)</field>
                                <field name="code">III_17</field>
                                <field name="expression_ids">
                                    <record id="account_tax_report_3_17_tax_tag" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">tax.16%</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_tax_report_3_18_tax" model="account.report.line">
                                <field name="name">III.18 ITBIS COLLECTED (9% of box 13)</field>
                                <field name="code">III_18</field>
                                <field name="expression_ids">
                                    <record id="account_tax_report_3_18_tax_tag" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">tax.9%</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_tax_report_3_19_tax" model="account.report.line">
                                <field name="name">III.19 ITBIS COLLECTED (8% of box 14)</field>
                                <field name="code">III_19</field>
                                <field name="expression_ids">
                                    <record id="account_tax_report_3_19_tax_tag" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">tax.8%</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_tax_report_3_20_tax" model="account.report.line">
                                <field name="name">III.20 ITBIS CHARGED ON SALES OF DEPRECIABLE ASSETS</field>
                                <field name="code">III_20</field>
                                <field name="expression_ids">
                                    <record id="account_tax_report_3_20_tax_tag" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">tax.decreciable</field>
                                    </record>
                                </field>
                            </record>
                        </field>
                    </record>
                    <record id="account_tax_report_3_25_total" model="account.report.line">
                        <field name="name">III.25 TOTAL ITBIS DEDUCTIBLE (Add boxes 22+23+24)</field>
                        <field name="code">III_25</field>
                        <field name="aggregation_formula">III_22.balance + III_23.balance + III_24.balance</field>
                        <field name="children_ids">
                            <record id="account_tax_report_3_22_tax" model="account.report.line">
                                <field name="name">III.22 ITBIS PAID ON LOCAL PURCHASES</field>
                                <field name="code">III_22</field>
                                <field name="expression_ids">
                                    <record id="account_tax_report_3_22_tax_tag" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">tax.paid.purchase</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_tax_report_3_23_tax" model="account.report.line">
                                <field name="name">III.23 ITBIS PAID FOR DEDUCTIBLE SERVICES</field>
                                <field name="code">III_23</field>
                                <field name="expression_ids">
                                    <record id="account_tax_report_3_23_tax_tag" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">tax.paid.service</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_tax_report_3_24_tax" model="account.report.line">
                                <field name="name">III.24 ITBIS PAID ON IMPORTS</field>
                                <field name="code">III_24</field>
                                <field name="expression_ids">
                                    <record id="account_tax_report_3_24_tax_tag" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">tax.paid.imports</field>
                                    </record>
                                </field>
                            </record>
                        </field>
                    </record>
                </field>
            </record>
            <record id="account_tax_report_A" model="account.report.line">
                <field name="name">A ITBIS WITHHELD</field>
                <field name="aggregation_formula">A39.balance + A50.balance</field>
                <field name="hierarchy_level">0</field>
                <field name="children_ids">
                    <record id="account_tax_report_A39_base" model="account.report.line">
                        <field name="name">A.39 Services Subject to Withholding Individuals = Individuals and Entity</field>
                        <field name="code">A39</field>
                        <field name="expression_ids">
                            <record id="account_tax_report_A39_base_tag" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">tax_tags</field>
                                <field name="formula">A.39</field>
                            </record>
                        </field>
                    </record>
                    <record id="account_tax_report_A50_tax" model="account.report.line">
                        <field name="name">A.50 ITBIS For Services Subject To Withholding Taxes Individuals</field>
                        <field name="code">A50</field>
                        <field name="expression_ids">
                            <record id="account_tax_report_A50_tax_tag" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">tax_tags</field>
                                <field name="formula">A.50</field>
                            </record>
                        </field>
                    </record>
                </field>
            </record>
        </field>
    </record>
</odoo>
