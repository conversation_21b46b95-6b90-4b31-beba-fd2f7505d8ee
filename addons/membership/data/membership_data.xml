<?xml version="1.0" encoding="UTF-8"?>
<odoo>
    <data noupdate="1">
        <record id="ir_cron_update_membership" model="ir.cron">
            <field name="name">Membership: update memberships</field>
            <field name="model_id" ref="base.model_res_partner"/>
            <field name="state">code</field>
            <field name="code">model._cron_update_membership()</field>
            <field name="interval_type">days</field>
            <field name="numbercall">-1</field>
        </record>
    </data>
</odoo>
