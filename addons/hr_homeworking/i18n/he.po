# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* hr_homeworking
# 
# Translators:
# <AUTHOR> <EMAIL>, 2023
# NoaFarkash, 2023
# <PERSON><PERSON> <<EMAIL>>, 2023
# <PERSON>, 2023
# <PERSON><PERSON> BLONDER <<EMAIL>>, 2023
# <PERSON><PERSON> <<EMAIL>>, 2023
# <PERSON>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-05-09 20:37+0000\n"
"PO-Revision-Date: 2023-10-26 23:09+0000\n"
"Last-Translator: He<PERSON> <<EMAIL>>, 2024\n"
"Language-Team: Hebrew (https://app.transifex.com/odoo/teams/41243/he/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: he\n"
"Plural-Forms: nplurals=3; plural=(n == 1 && n % 1 == 0) ? 0 : (n == 2 && n % 1 == 0) ? 1: 2;\n"

#. module: hr_homeworking
#: model_terms:ir.ui.view,arch_db:hr_homeworking.res_useurs_view_form_profile
#: model_terms:ir.ui.view,arch_db:hr_homeworking.view_employee_form
msgid ""
"<span class=\"text-muted fst-italic oe_inline\" colspan=\"2\">Specify your "
"default work location for each day of the week. This schedule will repeat "
"itself each week.</span>"
msgstr ""

#. module: hr_homeworking
#. odoo-javascript
#: code:addons/hr_homeworking/static/src/calendar/hr_homeworking_calendar_controller.js:0
#, python-format
msgid "Are you sure you want to delete this exception?"
msgstr ""

#. module: hr_homeworking
#. odoo-javascript
#: code:addons/hr_homeworking/static/src/calendar/hr_homeworking_calendar_controller.js:0
#, python-format
msgid "Are you sure you want to delete this location?"
msgstr ""

#. module: hr_homeworking
#: model:ir.model.fields.selection,name:hr_homeworking.selection__hr_employee__hr_icon_display__presence_home
#: model:ir.model.fields.selection,name:hr_homeworking.selection__hr_employee_base__hr_icon_display__presence_home
#: model:ir.model.fields.selection,name:hr_homeworking.selection__hr_employee_public__hr_icon_display__presence_home
msgid "At Home"
msgstr ""

#. module: hr_homeworking
#: model:ir.model.fields.selection,name:hr_homeworking.selection__hr_employee__hr_icon_display__presence_office
#: model:ir.model.fields.selection,name:hr_homeworking.selection__hr_employee_base__hr_icon_display__presence_office
#: model:ir.model.fields.selection,name:hr_homeworking.selection__hr_employee_public__hr_icon_display__presence_office
msgid "At Office"
msgstr ""

#. module: hr_homeworking
#: model:ir.model.fields.selection,name:hr_homeworking.selection__hr_employee__hr_icon_display__presence_other
#: model:ir.model.fields.selection,name:hr_homeworking.selection__hr_employee_base__hr_icon_display__presence_other
#: model:ir.model.fields.selection,name:hr_homeworking.selection__hr_employee_public__hr_icon_display__presence_other
msgid "At Other"
msgstr ""

#. module: hr_homeworking
#: model:ir.model,name:hr_homeworking.model_hr_employee_base
msgid "Basic Employee"
msgstr "עובד רגיל"

#. module: hr_homeworking
#. odoo-javascript
#: code:addons/hr_homeworking/static/src/calendar/hr_homeworking_calendar_controller.js:0
#: code:addons/hr_homeworking/static/src/calendar/hr_homeworking_calendar_controller.js:0
#, python-format
msgid "Confirmation"
msgstr "אישור"

#. module: hr_homeworking
#: model:ir.model,name:hr_homeworking.model_res_partner
msgid "Contact"
msgstr "איש קשר"

#. module: hr_homeworking
#: model:ir.model.fields,field_description:hr_homeworking.field_homework_location_wizard__work_location_type
#: model:ir.model.fields,field_description:hr_homeworking.field_hr_employee_location__work_location_type
msgid "Cover Image"
msgstr "תמונת הכריכה"

#. module: hr_homeworking
#. odoo-javascript
#: code:addons/hr_homeworking/static/src/calendar/hr_homeworking_calendar_controller.js:0
#, python-format
msgid "Create Record"
msgstr "צור רשומה"

#. module: hr_homeworking
#: model:ir.model.fields,field_description:hr_homeworking.field_homework_location_wizard__create_uid
#: model:ir.model.fields,field_description:hr_homeworking.field_hr_employee_location__create_uid
msgid "Created by"
msgstr "נוצר על-ידי"

#. module: hr_homeworking
#: model:ir.model.fields,field_description:hr_homeworking.field_homework_location_wizard__create_date
#: model:ir.model.fields,field_description:hr_homeworking.field_hr_employee_location__create_date
msgid "Created on"
msgstr "נוצר ב-"

#. module: hr_homeworking
#: model:ir.model.fields,field_description:hr_homeworking.field_hr_employee__exceptional_location_id
#: model:ir.model.fields,field_description:hr_homeworking.field_hr_employee_base__exceptional_location_id
#: model:ir.model.fields,field_description:hr_homeworking.field_hr_employee_public__exceptional_location_id
msgid "Current"
msgstr "נוכחי"

#. module: hr_homeworking
#: model:ir.model.fields,field_description:hr_homeworking.field_homework_location_wizard__date
#: model:ir.model.fields,field_description:hr_homeworking.field_hr_employee_location__date
msgid "Date"
msgstr "תאריך"

#. module: hr_homeworking
#: model:ir.model.fields,field_description:hr_homeworking.field_homework_location_wizard__day_week_string
#: model:ir.model.fields,field_description:hr_homeworking.field_hr_employee_location__day_week_string
msgid "Day Week String"
msgstr ""

#. module: hr_homeworking
#: model:ir.model.fields,field_description:hr_homeworking.field_homework_location_wizard__display_name
#: model:ir.model.fields,field_description:hr_homeworking.field_hr_employee_location__display_name
msgid "Display Name"
msgstr "שם לתצוגה"

#. module: hr_homeworking
#. odoo-javascript
#: code:addons/hr_homeworking/static/src/calendar/hr_homeworking_calendar_controller.js:0
#, python-format
msgid "Edit Record"
msgstr ""

#. module: hr_homeworking
#: model:ir.model.fields,field_description:hr_homeworking.field_homework_location_wizard__employee_id
#: model:ir.model.fields,field_description:hr_homeworking.field_hr_employee_location__employee_id
msgid "Employee"
msgstr "עובד"

#. module: hr_homeworking
#: model:ir.model,name:hr_homeworking.model_hr_employee_location
msgid "Employee Location"
msgstr ""

#. module: hr_homeworking
#: model:ir.model.fields,field_description:hr_homeworking.field_homework_location_wizard__employee_name
#: model:ir.model.fields,field_description:hr_homeworking.field_hr_employee_location__employee_name
msgid "Employee Name"
msgstr "שם העובד"

#. module: hr_homeworking
#: model:ir.model.fields,field_description:hr_homeworking.field_hr_employee__friday_location_id
#: model:ir.model.fields,field_description:hr_homeworking.field_hr_employee_base__friday_location_id
#: model:ir.model.fields,field_description:hr_homeworking.field_hr_employee_public__friday_location_id
#: model:ir.model.fields,field_description:hr_homeworking.field_res_users__friday_location_id
msgid "Friday"
msgstr "יום שישי"

#. module: hr_homeworking
#: model:ir.model.fields,field_description:hr_homeworking.field_hr_employee__hr_icon_display
#: model:ir.model.fields,field_description:hr_homeworking.field_hr_employee_base__hr_icon_display
#: model:ir.model.fields,field_description:hr_homeworking.field_hr_employee_public__hr_icon_display
msgid "Hr Icon Display"
msgstr "תצוגת סמל של משאבי אנוש (HR)"

#. module: hr_homeworking
#: model:ir.model.fields,field_description:hr_homeworking.field_homework_location_wizard__id
#: model:ir.model.fields,field_description:hr_homeworking.field_hr_employee_location__id
msgid "ID"
msgstr "מזהה"

#. module: hr_homeworking
#: model:ir.model.fields,field_description:hr_homeworking.field_homework_location_wizard__write_uid
#: model:ir.model.fields,field_description:hr_homeworking.field_hr_employee_location__write_uid
msgid "Last Updated by"
msgstr "עודכן לאחרונה על-ידי"

#. module: hr_homeworking
#: model:ir.model.fields,field_description:hr_homeworking.field_homework_location_wizard__write_date
#: model:ir.model.fields,field_description:hr_homeworking.field_hr_employee_location__write_date
msgid "Last Updated on"
msgstr "עדכון אחרון ב"

#. module: hr_homeworking
#: model:ir.model.fields,field_description:hr_homeworking.field_homework_location_wizard__work_location_id
#: model:ir.model.fields,field_description:hr_homeworking.field_hr_employee_location__work_location_id
#: model_terms:ir.ui.view,arch_db:hr_homeworking.homework_location_wizard_view_form
msgid "Location"
msgstr "מיקום"

#. module: hr_homeworking
#: model:ir.model.fields,field_description:hr_homeworking.field_homework_location_wizard__work_location_name
#: model:ir.model.fields,field_description:hr_homeworking.field_hr_employee_location__work_location_name
msgid "Location name"
msgstr ""

#. module: hr_homeworking
#: model:ir.model.fields,field_description:hr_homeworking.field_hr_employee__monday_location_id
#: model:ir.model.fields,field_description:hr_homeworking.field_hr_employee_base__monday_location_id
#: model:ir.model.fields,field_description:hr_homeworking.field_hr_employee_public__monday_location_id
#: model:ir.model.fields,field_description:hr_homeworking.field_res_users__monday_location_id
msgid "Monday"
msgstr "יום שני"

#. module: hr_homeworking
#: model:ir.model.fields,field_description:hr_homeworking.field_hr_employee__name_work_location_display
#: model:ir.model.fields,field_description:hr_homeworking.field_hr_employee_base__name_work_location_display
#: model:ir.model.fields,field_description:hr_homeworking.field_hr_employee_public__name_work_location_display
msgid "Name Work Location Display"
msgstr ""

#. module: hr_homeworking
#. odoo-python
#: code:addons/hr_homeworking/models/hr_homeworking.py:0
#: model:ir.model.constraint,message:hr_homeworking.constraint_hr_employee_location_uniq_exceptional_per_day
#, python-format
msgid ""
"Only one default work location and one exceptional work location per day per"
" employee."
msgstr ""

#. module: hr_homeworking
#: model_terms:ir.ui.view,arch_db:hr_homeworking.res_useurs_view_form_profile
#: model_terms:ir.ui.view,arch_db:hr_homeworking.view_employee_form
msgid "Remote Work"
msgstr "עבודה מרחוק"

#. module: hr_homeworking
#: model_terms:ir.ui.view,arch_db:hr_homeworking.homework_location_wizard_view_form
msgid "Repeat every"
msgstr "חזור כל"

#. module: hr_homeworking
#: model:ir.model.fields,field_description:hr_homeworking.field_hr_employee__saturday_location_id
#: model:ir.model.fields,field_description:hr_homeworking.field_hr_employee_base__saturday_location_id
#: model:ir.model.fields,field_description:hr_homeworking.field_hr_employee_public__saturday_location_id
#: model:ir.model.fields,field_description:hr_homeworking.field_res_users__saturday_location_id
msgid "Saturday"
msgstr "יום שבת"

#. module: hr_homeworking
#: model:ir.model,name:hr_homeworking.model_homework_location_wizard
msgid "Set Homework Location Wizard"
msgstr ""

#. module: hr_homeworking
#. odoo-javascript
#: code:addons/hr_homeworking/static/src/calendar/common/calender_common_renderer.xml:0
#: model:ir.actions.act_window,name:hr_homeworking.set_location_wizard_action
#: model_terms:ir.ui.view,arch_db:hr_homeworking.homework_location_wizard_view_form
#, python-format
msgid "Set Location"
msgstr ""

#. module: hr_homeworking
#: model:ir.model.fields,field_description:hr_homeworking.field_hr_employee__sunday_location_id
#: model:ir.model.fields,field_description:hr_homeworking.field_hr_employee_base__sunday_location_id
#: model:ir.model.fields,field_description:hr_homeworking.field_hr_employee_public__sunday_location_id
#: model:ir.model.fields,field_description:hr_homeworking.field_res_users__sunday_location_id
msgid "Sunday"
msgstr "יום ראשון"

#. module: hr_homeworking
#: model:ir.model.fields,help:hr_homeworking.field_hr_employee__exceptional_location_id
#: model:ir.model.fields,help:hr_homeworking.field_hr_employee_base__exceptional_location_id
#: model:ir.model.fields,help:hr_homeworking.field_hr_employee_public__exceptional_location_id
msgid "This is the exceptional, non-weekly, location set for today."
msgstr ""

#. module: hr_homeworking
#: model:ir.model.fields,field_description:hr_homeworking.field_hr_employee__thursday_location_id
#: model:ir.model.fields,field_description:hr_homeworking.field_hr_employee_base__thursday_location_id
#: model:ir.model.fields,field_description:hr_homeworking.field_hr_employee_public__thursday_location_id
#: model:ir.model.fields,field_description:hr_homeworking.field_res_users__thursday_location_id
msgid "Thursday"
msgstr "יום חמישי"

#. module: hr_homeworking
#: model:ir.model.fields,field_description:hr_homeworking.field_hr_employee__today_location_name
#: model:ir.model.fields,field_description:hr_homeworking.field_hr_employee_base__today_location_name
#: model:ir.model.fields,field_description:hr_homeworking.field_hr_employee_public__today_location_name
msgid "Today Location Name"
msgstr ""

#. module: hr_homeworking
#: model:ir.model.fields,field_description:hr_homeworking.field_hr_employee__tuesday_location_id
#: model:ir.model.fields,field_description:hr_homeworking.field_hr_employee_base__tuesday_location_id
#: model:ir.model.fields,field_description:hr_homeworking.field_hr_employee_public__tuesday_location_id
#: model:ir.model.fields,field_description:hr_homeworking.field_res_users__tuesday_location_id
msgid "Tuesday"
msgstr "יום שלישי"

#. module: hr_homeworking
#. odoo-python
#: code:addons/hr_homeworking/models/hr_employee.py:0
#: model_terms:ir.ui.view,arch_db:hr_homeworking.res_useurs_view_form_profile
#: model_terms:ir.ui.view,arch_db:hr_homeworking.view_employee_form
#, python-format
msgid "Unspecified"
msgstr ""

#. module: hr_homeworking
#: model:ir.model,name:hr_homeworking.model_res_users
msgid "User"
msgstr "משתמש"

#. module: hr_homeworking
#: model:ir.model.fields,field_description:hr_homeworking.field_hr_employee__wednesday_location_id
#: model:ir.model.fields,field_description:hr_homeworking.field_hr_employee_base__wednesday_location_id
#: model:ir.model.fields,field_description:hr_homeworking.field_hr_employee_public__wednesday_location_id
#: model:ir.model.fields,field_description:hr_homeworking.field_res_users__wednesday_location_id
msgid "Wednesday"
msgstr "יום רביעי"

#. module: hr_homeworking
#: model:ir.model.fields,field_description:hr_homeworking.field_homework_location_wizard__weekly
msgid "Weekly"
msgstr "שבועי"

#. module: hr_homeworking
#: model:ir.model,name:hr_homeworking.model_hr_work_location
#: model_terms:ir.ui.view,arch_db:hr_homeworking.view_employee_tree
msgid "Work Location"
msgstr "מיקום עבודה"

#. module: hr_homeworking
#: model_terms:ir.ui.view,arch_db:hr_homeworking.view_employee_filter
msgid "Work location"
msgstr ""

#. module: hr_homeworking
#. odoo-python
#: code:addons/hr_homeworking/models/hr_work_location.py:0
#, python-format
msgid "You cannot delete locations that are being used by your employees"
msgstr ""
