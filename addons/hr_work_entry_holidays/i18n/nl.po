# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* hr_work_entry_holidays
# 
# Translators:
# Wil Odoo, 2023
# <PERSON><PERSON><PERSON>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-10-26 21:55+0000\n"
"PO-Revision-Date: 2023-10-26 23:09+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON>, 2023\n"
"Language-Team: Dutch (https://app.transifex.com/odoo/teams/41243/nl/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: nl\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: hr_work_entry_holidays
#. odoo-python
#: code:addons/hr_work_entry_holidays/models/hr_leave.py:0
#, python-format
msgid ""
"A leave cannot be set across multiple contracts with different working schedules.\n"
"\n"
"Please create one time off for each contract.\n"
"\n"
"Time off:\n"
"%s\n"
"\n"
"Contracts:\n"
"%s"
msgstr ""
"Een verlof kan niet worden verdeeld over meerdere contracten met verschillende werkroosters.\n"
"\n"
"Maak één verlof aan voor elk contract.\n"
"\n"
"Verlof:\n"
"%s\n"
"\n"
"Contracten:\n"
"%s"

#. module: hr_work_entry_holidays
#: model:ir.model,name:hr_work_entry_holidays.model_hr_employee_base
msgid "Basic Employee"
msgstr "Basis werknemer"

#. module: hr_work_entry_holidays
#. odoo-python
#: code:addons/hr_work_entry_holidays/models/hr_contract.py:0
#, python-format
msgid ""
"Changing the contract on this employee changes their working schedule in a "
"period they already took leaves. Changing this working schedule changes the "
"duration of these leaves in such a way the employee no longer has the "
"required allocation for them. Please review these leaves and/or allocations "
"before changing the contract."
msgstr ""
"Als het contrat van deze werknemer wordt gewijzigd, veranderd zijn "
"werkschema in een periode waarin hij al verlof heeft genomen. Door dit "
"werkschema te wijzigen, verandert de duur van dit verlof zodanig dat de "
"werknemer niet langer de vereiste toewijzing hiervoor heeft. Controleer dit "
"verlof en/of deze toewijzingen voordat je het contract wijzigt."

#. module: hr_work_entry_holidays
#: model:ir.model,name:hr_work_entry_holidays.model_hr_contract
msgid "Employee Contract"
msgstr "Arbeidsovereenkomst"

#. module: hr_work_entry_holidays
#: model:ir.model,name:hr_work_entry_holidays.model_hr_work_entry
msgid "HR Work Entry"
msgstr "HR-werkboeking"

#. module: hr_work_entry_holidays
#: model:ir.model,name:hr_work_entry_holidays.model_hr_work_entry_type
msgid "HR Work Entry Type"
msgstr "HR werkboekingstype"

#. module: hr_work_entry_holidays
#: model_terms:ir.ui.view,arch_db:hr_work_entry_holidays.work_entry_type_leave_form_inherit
msgid "Payroll"
msgstr "Loonadministratie"

#. module: hr_work_entry_holidays
#: model:ir.model.fields,field_description:hr_work_entry_holidays.field_hr_work_entry__leave_state
msgid "Status"
msgstr "Status"

#. module: hr_work_entry_holidays
#: model:ir.model.fields,help:hr_work_entry_holidays.field_hr_work_entry__leave_state
msgid ""
"The status is set to 'To Submit', when a time off request is created.\n"
"The status is 'To Approve', when time off request is confirmed by user.\n"
"The status is 'Refused', when time off request is refused by manager.\n"
"The status is 'Approved', when time off request is approved by manager."
msgstr ""
"De status is ingesteld op 'Indienen', wanneer een verlofaanvraag wordt aangemaakt.\n"
"De status is 'Goed te keuren', wanneer een verlofaanvraag wordt bevestigd door de gebruiker. \n"
"De status wordt 'Afgewezen', wanneer een verlofaanvraag wordt afgewezen door de manager. \n"
"De status wordt 'Goedgekeurd', wanneer een verlofaanvraag wordt goedgekeurd door de manager."

#. module: hr_work_entry_holidays
#. odoo-python
#: code:addons/hr_work_entry_holidays/models/hr_leave.py:0
#, python-format
msgid ""
"There is no employee set on the time off. Please make sure you're logged in "
"the correct company."
msgstr ""
"Er is geen werknemer op de verlofaanvraag ingesteld. Zorg ervoor dat je bij "
"het juiste bedrijf bent ingelogd."

#. module: hr_work_entry_holidays
#: model:ir.model,name:hr_work_entry_holidays.model_hr_leave
#: model:ir.model.fields,field_description:hr_work_entry_holidays.field_hr_work_entry__leave_id
msgid "Time Off"
msgstr "Verlof"

#. module: hr_work_entry_holidays
#: model:ir.model,name:hr_work_entry_holidays.model_hr_leave_type
#: model:ir.model.fields,field_description:hr_work_entry_holidays.field_hr_work_entry_type__leave_type_ids
msgid "Time Off Type"
msgstr "Soort verlof"

#. module: hr_work_entry_holidays
#: model:ir.model.fields,field_description:hr_work_entry_holidays.field_hr_leave_type__work_entry_type_id
msgid "Work Entry Type"
msgstr "Werkboekingstype"

#. module: hr_work_entry_holidays
#: model:ir.model.fields,help:hr_work_entry_holidays.field_hr_work_entry_type__leave_type_ids
msgid "Work entry used in the payslip."
msgstr "Werkboeking gebruikt op de loonstrook"
