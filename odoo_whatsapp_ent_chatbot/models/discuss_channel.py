# Part of Odoo. See COPYRIGHT & LICENSE files for full copyright and licensing details.
import datetime

from odoo import api, fields, models, tools, Command, _
from markupsafe import Markup

from odoo.addons.test_lint.tests.test_override_signatures import methods_to_sanitize
from odoo.addons.web_editor.models.ir_qweb_fields import DateTime
from odoo.addons.whatsapp.tools import phone_validation as wa_phone_validation
import logging

_logger = logging.getLogger(__name__)


class ChatbotDiscussChannel(models.Model):
    _inherit = "discuss.channel"

    wa_chatbot_id = fields.Many2one(
        comodel_name="whatsapp.chatbot", string="Whatsapp Chatbot"
    )
    message_ids = fields.One2many(
        "mail.message",
        "res_id",
        string="Messages",
    )
    script_sequence = fields.Integer(string="Sequence", default=1)
    is_chatbot_ended = fields.Boolean(string="Inactivate Chatbot")

    agent_assigned_date = fields.Datetime('Agent Assigned')
    available_operator_id = fields.Many2one('res.partner')
    notifier_id = fields.Many2one('res.users')
    session_ids = fields.One2many('chat.session', 'channel_id')
    session_count = fields.Integer(compute="_compute_session_count", store=True)
    current_session_id = fields.Many2one('chat.session')

    def chatbot_activate(self):
        channels = self.search([])
        for rec in channels:
            if rec.is_chatbot_ended:
                rec.is_chatbot_ended = False
                rec.wa_chatbot_id = rec.wa_account_id.wa_chatbot_id.id
                if rec.current_session_id:
                    message_id = rec.current_session_id.message_ids[0] if rec.current_session_id.message_ids else None
                    mail_message_id = rec.current_session_id.mail_message_ids[
                        0] if rec.current_session_id.mail_message_ids else None

                    if not mail_message_id and message_id:
                        last_message_info = message_id
                    elif mail_message_id and message_id and message_id.create_date > mail_message_id.create_date:
                        last_message_info = message_id
                    else:
                        last_message_info = mail_message_id
                    rec.current_session_id.last_message_info = f"{last_message_info._name}/{last_message_info.id}" if last_message_info else None
                    rec.current_session_id.session_active = False

    def specific_chatbot_activate(self, channel_id):
        self = self.browse(channel_id)
        if self.is_chatbot_ended:
            self.is_chatbot_ended = False
            self.wa_chatbot_id = self.wa_account_id.wa_chatbot_id.id
            message_id = self.current_session_id.message_ids[0] if self.current_session_id.message_ids else None
            mail_message_id = self.current_session_id.mail_message_ids[
                0] if self.current_session_id.mail_message_ids else None

            if not mail_message_id or message_id.create_date > mail_message_id.create_date:
                last_message_info = message_id
            else:
                last_message_info = mail_message_id
            self.current_session_id.last_message_info = f"{last_message_info._name}/{last_message_info.id}" if last_message_info else None
            self.execute_command_leave()
            self.current_session_id.session_active = False
            return True
        self.execute_command_leave()
        return False

    def _notify_thread(self, message, msg_vals=False, **kwargs):
        res = super(ChatbotDiscussChannel, self)._notify_thread(
            message, msg_vals=msg_vals, **kwargs
        )
        _logger.info(f"{kwargs.get('whatsapp_inbound_msg_uid') = }")
        _logger.info(f"{msg_vals.get('message_type') = }")
        if not kwargs.get('whatsapp_inbound_msg_uid') and msg_vals.get(
                'message_type') == 'whatsapp_message':
            self.is_chatbot_ended = True
        if self.env.context.get('stop_recur'):
            return res
        if message:
            wa_account_id = self.wa_account_id
            # user_partner = (
            #         wa_account_id.notify_user_ids and wa_account_id.notify_user_ids[0] or []
            # )
            mail_message_id = message
            partner_id = mail_message_id.author_id or mail_message_id.wa_message_ids.customer_id
            if not partner_id:
                mobile_formatted = mail_message_id.wa_message_ids.mobile_number_formatted
                sender_name = mail_message_id.display_name
                partner_id = self.env['res.partner']._find_or_create_from_number(mobile_formatted, sender_name)
                print(f"partner_id new {partner_id}")
            if wa_account_id and wa_account_id.wa_chatbot_id and self:
                message.update({"wa_chatbot_id": wa_account_id.wa_chatbot_id.id})
                if not self.is_chatbot_ended:
                    message_script = (
                        self.env["whatsapp.chatbot"]
                        .search([("id", "=", wa_account_id.wa_chatbot_id.id)])
                        .mapped("step_type_ids")
                        .filtered(
                            lambda l: l.message
                                      == tools.html2plaintext(mail_message_id.body)
                        )
                    )

                    current__chat_seq_script = (
                        self.env["whatsapp.chatbot"]
                        .search([("id", "=", wa_account_id.wa_chatbot_id.id)])
                        .mapped("step_type_ids")
                        .filtered(lambda l: l.sequence == self.script_sequence)
                    )
                    if message_script:
                        chatbot_script_lines = message_script
                    elif current__chat_seq_script and current__chat_seq_script.step_call_type != 'action':
                        chatbot_script_lines = current__chat_seq_script
                    else:
                        chatbot_script_lines = wa_account_id.wa_chatbot_id.step_type_ids[0]

                    for chat in chatbot_script_lines:
                        action_operator_id = chat.action_id.operator_id
                        print('action_operator_id', action_operator_id)
                        team_lead_ids = action_operator_id.team_lead_ids.filtered(
                            lambda user: user.im_status == "online")
                        print('team_lead_ids ', team_lead_ids)
                        if team_lead_ids:
                            team_lead_id = team_lead_ids[0]
                            self.notifier_id = team_lead_id
                        user_partner = self.notifier_id or wa_account_id.notify_user_ids[0]
                        print('self.notifier_id', user_partner.name)
                        print('chat.step_call_type', chat.step_call_type)
                        if chat.sequence >= self.script_sequence:
                            self.write(
                                {
                                    "wa_chatbot_id": chat.whatsapp_chatbot_id.id
                                    if wa_account_id.wa_chatbot_id
                                       == chat.whatsapp_chatbot_id
                                    else False,
                                    "script_sequence": chat.sequence,
                                }
                            )
                        elif (
                                current__chat_seq_script
                                and current__chat_seq_script.parent_id
                                and current__chat_seq_script.parent_id == chat.parent_id
                        ):
                            for chat in chatbot_script_lines:
                                self.write(
                                    {
                                        "wa_chatbot_id": chat.whatsapp_chatbot_id.id,
                                        "script_sequence": chat.sequence,
                                    }
                                )
                        else:
                            first_script = (
                                self.env["whatsapp.chatbot"]
                                .search([("id", "=", self.wa_chatbot_id.id)])
                                .mapped("step_type_ids")
                                .filtered(lambda l: l.sequence == 1)
                            )

                            for chat in first_script:
                                self.write(
                                    {
                                        "wa_chatbot_id": chat.whatsapp_chatbot_id.id,
                                        "script_sequence": first_script.sequence,
                                    }
                                )
                        if chat.step_call_type in ["template", "interactive"]:
                            template = chat.template_id
                            print('template', template)
                            if template:
                                _logger.info(f'in template interactive partner is {partner_id} {partner_id.name}')
                                whatsapp_composer = (
                                    self.env["whatsapp.composer"]
                                    .with_user(user_partner.id)
                                    .with_context(
                                        {
                                            "active_id": partner_id.id,
                                            "is_chatbot": True,
                                            "wa_chatbot_id": self.wa_chatbot_id.id,
                                        }
                                    )
                                    .create(
                                        {
                                            "phone": partner_id.mobile,
                                            "wa_template_id": template.id,
                                            "res_model": template.model_id.model,
                                            'res_ids': f"{partner_id.ids}"
                                        }
                                    )
                                )
                                print('whatsapp_composer', whatsapp_composer)
                                whatsapp_composer.with_context(
                                    prevent_open_channel_direct=True)._send_whatsapp_template()

                        elif chat.step_call_type == "message":
                            self.with_user(user_partner.id).sudo().message_post(
                                body=chat.answer,
                                message_type="whatsapp_message",
                            )
                        elif chat.step_call_type == "action":
                            if chat.action_id.binding_model_id.model == "crm.lead":
                                lead_message = (
                                        "Dear "
                                        + partner_id.name
                                        + ", We are pleased to inform you that your lead has been successfully generated. Our team will be in touch with you shortly."
                                )
                                self.with_context({'stop_recur': True}).with_user(user_partner.id).sudo().message_post(
                                    body=lead_message, message_type="whatsapp_message"
                                )
                                self.env["crm.lead"].with_user(
                                    user_partner.id
                                ).sudo().create(
                                    {
                                        "name": partner_id.name + " WA ChatBot Lead ",
                                        "partner_id": partner_id.id,
                                        "email_from": partner_id.email
                                        if partner_id.email
                                        else False,
                                        "mobile": partner_id.mobile,
                                        "user_id": user_partner.id,
                                        "type": "lead",
                                        "description": "Lead created by Chatbot for customer "
                                                       + partner_id.name,
                                    }
                                )
                            if (
                                    chat.action_id.binding_model_id.model
                                    == "helpdesk.ticket"
                            ):
                                ticket_message = (
                                        "Dear "
                                        + partner_id.name
                                        + ", We are pleased to inform you that your Ticket has been raised. Our team will be in touch with you shortly."
                                )
                                self.with_context({'stop_recur': True}).with_user(user_partner.id).sudo().message_post(
                                    body=ticket_message, message_type="whatsapp_message"
                                )
                                self.env["helpdesk.ticket"].with_user(
                                    user_partner.id
                                ).sudo().create(
                                    {
                                        "name": partner_id.name + " WA ChatBot Ticket ",
                                        "partner_id": partner_id.id,
                                        "partner_phone": partner_id.mobile,
                                        "user_id": user_partner.id,
                                        "description": "Ticket raised by Chatbot for customer "
                                                       + partner_id.name,
                                    }
                                )
                            if (
                                    chat.action_id.binding_model_id.model
                                    == "discuss.channel"
                            ):
                                is_working_hours = self.env['work.hours'].is_now_within_work_day()
                                if is_working_hours:
                                    available_operator = False
                                    user_ids = action_operator_id.group_line_ids.mapped('user_id')
                                    active_operator = user_ids.filtered(lambda user: user.im_status == "online")
                                    leader_id = action_operator_id.team_lead_id
                                    leader_id = leader_id.partner_id

                                    if active_operator:
                                        available_operator = self.assign_next_operator(active_operator)
                                        if available_operator:
                                            added_operator = (
                                                self.channel_partner_ids.filtered(
                                                    lambda x: x.id == available_operator.id
                                                )
                                            )
                                            if added_operator:
                                                self.write(
                                                    {
                                                        "is_chatbot_ended": True,
                                                        "wa_chatbot_id": False,
                                                    }
                                                )
                                            else:
                                                self.write(
                                                    {
                                                        "channel_partner_ids": [
                                                            (4, available_operator.id)
                                                        ],
                                                        "is_chatbot_ended": True,
                                                        "wa_chatbot_id": False,
                                                    }
                                                )
                                            ##
                                            added_leader = (
                                                self.channel_partner_ids.filtered(
                                                    lambda x: x.id == leader_id.id
                                                )
                                            )
                                            if not added_leader:
                                                self.write(
                                                    {
                                                        "channel_partner_ids": [
                                                            (4, leader_id.id)
                                                        ],
                                                        "is_chatbot_ended": True,
                                                        "wa_chatbot_id": False,
                                                    }
                                                )
                                            ##
                                            mail_channel_partner = (
                                                self.env["discuss.channel.member"]
                                                .sudo()
                                                .search(
                                                    [
                                                        ("channel_id", "=", self.id),
                                                        (
                                                            "partner_id",
                                                            "=",
                                                            available_operator.id,
                                                        ),
                                                    ]
                                                )
                                            )
                                            mail_channel_partner.write({"is_pinned": True})
                                            wait_message = "We are connecting you with one of our experts. Please wait a moment."
                                            print('channel_member_ids before',
                                                  [m.partner_id.name for m in self.channel_member_ids])
                                            self.with_context({'stop_recur': True}).with_user(
                                                user_partner.id).sudo().message_post(
                                                body=wait_message,
                                                message_type="whatsapp_message",
                                            )
                                            user_message = (
                                                    "You are now chatting with "
                                                    + available_operator.name
                                            )
                                            self.with_context({'stop_recur': True}).with_user(
                                                user_partner.id).sudo().message_post(
                                                body=user_message,
                                                message_type="whatsapp_message",
                                            )
                                            print('2')
                                            self.handle_session_assigning(available_operator, message.wa_message_ids,
                                                                          True)
                                    else:
                                        print('if partner in memebrs',
                                              [me.partner_id.name for me in self.channel_member_ids])
                                        print('user_partner', user_partner.name)
                                        # if user_partner.id not in self.channel_member_ids.ids:
                                        #     partners = [member.partner_id.id for member in self.channel_member_ids]
                                        #     print('partners', partners)
                                        #     self.channel_member_ids = None
                                        #     self.channel_member_ids = [Command.create({'partner_id': partner}) for
                                        #                                partner in partners]
                                        # print('if partner in memebrs',
                                        #       [me.partner_id.name for me in self.channel_member_ids])
                                        no_user_message = "Apologies, but there are currently no active operators available."
                                        self.with_context({'stop_recur': True}).with_user(
                                            user_partner.id).sudo().message_post(
                                            body=no_user_message,
                                            message_type="whatsapp_message",
                                        )
                                        user_message = (
                                            "We will connect you with one shortly."
                                        )
                                        self.sudo().with_context({'stop_recur': True}).with_user(
                                            user_partner.id).sudo().message_post(
                                            body=user_message,
                                            message_type="whatsapp_message",
                                        )
                                else:
                                    wait_message = """
                                    \u202Bعزيزي العميل ،
                                    يرجى العلم بأن مواعيد العمل الرسمية لدينا تبدأ من السبت الى الخميس من العاشرة صباحا وحتى العاشرة مساء ،
                                    سيتم التواصل معكم من خلالنا مع بداية مواعيد العمل لدينا ،
                                    نسعد دائما بتواصلكم معنا🙂
                                
                                    \u202ADear customer,
                                    Please note that our official working hours start from Saturday to Thursday from Ten o'clock in the morning until Ten o'clock in the evening,
                                    You will be contacted through us at the beginning of our working hours,
                                    We are always happy to contact you🙂
                                    """
                                    self.with_context({'stop_recur': True}).with_user(
                                        user_partner.id).sudo().message_post(
                                        body=wait_message,
                                        message_type="whatsapp_message",
                                    )
                else:
                    open_session_id = self.current_session_id
                    if open_session_id:
                        self.handle_session_assigning(open_session_id.agent_id, message.wa_message_ids or message)

        return res

    @api.returns('self')
    def _get_whatsapp_channel(self, whatsapp_number, wa_account_id, sender_name=False, create_if_not_found=False,
                              related_message=False):
        """ Creates a whatsapp channel.

        :param str whatsapp_number: whatsapp phone number of the customer. It should
          be formatted according to whatsapp standards, aka {country_code}{national_number}.

        :returns: whatsapp discussion discuss.channel
        """
        # be somewhat defensive with number, as it is used in various flows afterwards
        # notably in 'message_post' for the number, and called by '_process_messages'
        base_number = whatsapp_number if whatsapp_number.startswith('+') else f'+{whatsapp_number}'
        wa_number = base_number.lstrip('+')
        wa_formatted = wa_phone_validation.wa_phone_format(
            self.env.company,
            number=base_number,
            force_format="WHATSAPP",
            raise_exception=False,
        ) or wa_number
        wa_formatted = self._phone_format(number=wa_formatted, force_format='INTERNATIONAL')

        related_record = False
        responsible_partners = self.env['res.partner']
        channel_domain = [
            ('whatsapp_number', '=', wa_formatted),
            ('wa_account_id', '=', wa_account_id.id)
        ]
        current_message_id = self._context.get('current_message_id')
        _logger.info(f'{current_message_id = }')
        _logger.info(f'{related_message = }')
        if current_message_id:
            current_message = self.env['whatsapp.message'].browse(current_message_id)
            _logger.info(f'exist {current_message_id = }')
            # _logger.info(f'exist mail {current_message_id.mail_message_id if current_message_id else current_message_id = }')
            related_message = current_message.mail_message_id if current_message.exists() else related_message
        if related_message:
            related_record = self.env[related_message.model].browse(related_message.res_id)
            _logger.info(f'final {related_message = }')
            # responsible_partners = related_record._whatsapp_get_responsible(
            #     related_message=related_message,
            #     related_record=related_record,
            #     whatsapp_account=wa_account_id,
            # ).partner_id
            responsible_partners = related_message.author_id

            if 'message_ids' in related_record:
                record_messages = related_record.message_ids
            else:
                record_messages = self.env['mail.message'].search([
                    ('model', '=', related_record._name),
                    ('res_id', '=', related_record.id),
                    ('message_type', '!=', 'user_notification'),
                ])
            # channel_domain += [
            #     ('whatsapp_mail_message_id', 'in', record_messages.ids),
            # ]
        channel = self.sudo().search(channel_domain, order='create_date desc', limit=1)
        _logger.info(f'find channel {channel = }')
        _logger.info(f'channel members {[m.partner_id.name for m in channel.channel_member_ids]}')
        # if responsible_partners:
        #     channel = channel.filtered(
        #         lambda c: all(r in c.channel_member_ids.partner_id for r in responsible_partners))

        print('ammmm', channel.notifier_id, channel.notifier_id.name if channel.notifier_id else '')
        partners_to_notify = channel.notifier_id.partner_id
        record_name = related_message.record_name
        if not record_name and related_message.res_id:
            record_name = self.env[related_message.model].browse(related_message.res_id).display_name
        whatsapp_template = self._context.get('whatsapp_template')
        _logger.info(f'{create_if_not_found = }')
        _logger.info(f'{whatsapp_template = }')
        _logger.info(f'partners_to_notify: {[a.name for a in partners_to_notify]}')
        print('my members ', [l.partner_id.name for l in channel.channel_member_ids])
        # print(channel.channel_member_ids.partner_id - partners_to_notify.partner_id)
        if partners_to_notify.id not in channel.channel_member_ids.partner_id.ids:
            channel.channel_member_ids = [Command.create({'partner_id': partner.id}) for partner in
                                          partners_to_notify]
            print('my members 2 ', [l.partner_id.name for l in channel.channel_member_ids])
        # if channel:
        #     if partners_to_notify.id not in channel.channel_member_ids.ids:
        #         channel.channel_member_ids = [Command.create({'partner_id': partner.id}) for partner in
        #                                       partners_to_notify]
        if not channel and create_if_not_found or whatsapp_template and not channel:
            channel = self.sudo().with_context(tools.clean_context(self.env.context)).create({
                'name': f"{wa_formatted} ({record_name})" if record_name else wa_formatted,
                'channel_type': 'whatsapp',
                'whatsapp_number': wa_formatted,
                'whatsapp_partner_id': self.env['res.partner']._find_or_create_from_number(wa_formatted,
                                                                                           sender_name).id,
                'wa_account_id': wa_account_id.id,
                'whatsapp_mail_message_id': related_message.id if related_message else None,
            })
            _logger.info(f'not found channel create new one {channel}')
            partners_to_notify += channel.whatsapp_partner_id
            _logger.info(f'partners_to_notify 1 {partners_to_notify}')
            if related_message:
                print('related_message', related_message.body)
                # Add message in channel about the related document
                info = _("Related %(model_name)s: ",
                         model_name=self.env['ir.model']._get(related_message.model).display_name)
                url = Markup('{base_url}/web#model={model}&id={res_id}').format(
                    base_url=self.get_base_url(), model=related_message.model, res_id=related_message.res_id)
                related_record_name = related_message.record_name
                if not related_record_name:
                    related_record_name = self.env[related_message.model].browse(related_message.res_id).display_name
                channel.message_post(
                    body=Markup('<p>{info}<a target="_blank" href="{url}">{related_record_name}</a></p>').format(
                        info=info, url=url, related_record_name=related_record_name),
                    message_type='comment',
                    author_id=self.env.ref('base.partner_root').id,
                    subtype_xmlid='mail.mt_note',
                )
                if hasattr(related_record, 'message_post'):
                    # Add notification in document about the new message and related channel
                    info = _("A new WhatsApp channel is created for this document")
                    url = Markup('{base_url}/web#model=discuss.channel&id={channel_id}').format(
                        base_url=self.get_base_url(), channel_id=channel.id)
                    related_record.message_post(
                        author_id=self.env.ref('base.partner_root').id,
                        body=Markup('<p>{info}<a target="_blank" class="o_whatsapp_channel_redirect"'
                                    'data-oe-id="{channel_id}" href="{url}">{channel_name}</a></p>').format(
                            info=info, url=url, channel_id=channel.id, channel_name=channel.display_name),
                        message_type='comment',
                        subtype_xmlid='mail.mt_note',
                    )
            _logger.info(f"{partners_to_notify = }")
            _logger.info(f"{channel.whatsapp_partner_id = }")
            _logger.info(f"{wa_account_id.notify_user_ids.partner_id = }")

            _logger.info(partners_to_notify == channel.whatsapp_partner_id)

            if partners_to_notify == channel.whatsapp_partner_id and wa_account_id.notify_user_ids.partner_id:
                partners_to_notify += wa_account_id.notify_user_ids.partner_id
                _logger.info(f"2- {partners_to_notify = }")
            channel.channel_member_ids = [Command.clear()] + [Command.create({'partner_id': partner.id}) for partner in
                                                              partners_to_notify]
            _logger.info(f'channel.channel_member_ids: {channel.channel_member_ids}')
            channel._broadcast(partners_to_notify.ids)
        return channel

    # def get_next_operator(self, chat, wa_account_id):
    #     action_operator_id = chat.action_id.operator_id
    #     active_operator = wa_account_id.wa_chatbot_id.mapped(
    #         "user_ids"
    #     ).filtered(lambda
    #                    user: user.operator_id.id == action_operator_id.id and user.im_status == "online")
    #     operator_partner_ids = active_operator.mapped('partner_id')
    #     channels_ids = self.search([
    #         ('channel_type', '=', 'whatsapp'),
    #         ('wa_chatbot_id', '=', wa_account_id.wa_chatbot_id.id)
    #     ])
    #     operator_ids = channels_ids.mapped('channel_partner_ids').filtered(
    #         lambda partner_ids: partner_ids.id in operator_partner_ids.ids)
    #     result = {}
    #     for channels_id in channels_ids:
    #         for op in operator_ids:
    #             if op.id in channels_id.channel_partner_ids.ids:
    #                 if result.get(op.id):
    #                     result[op.id] += 1
    #                 else:
    #                     result[op.id] = 1
    #     minimum = min(result, key=result.get)
    #     return self.env['res.partner'].browse(minimum)

    def assign_next_operator(self, users):
        user_assignment = self.env['user.assignment'].search([], limit=1)
        if not user_assignment:
            user_assignment = self.env['user.assignment'].create({})

        if user_assignment.last_assigned_user_id:
            try:
                last_index = users.ids.index(user_assignment.last_assigned_user_id.id)
                next_index = (last_index + 1) % len(users)
                _logger.info(f'last index: {last_index} -- next index: {next_index}')
            except:
                next_index = 0
        else:
            _logger.info('start indexing from begin')
            next_index = 0

        next_user = users[next_index]
        _logger.info(f'next user: {next_user.partner_id.name}')

        user_assignment.write({'last_assigned_user_id': next_user.id})
        return next_user.partner_id

    def action_unfollow_all(self):
        partner = self.env.user.partner_id
        for channel in self:
            if channel.channel_type == 'whatsapp' \
                    and ((channel.whatsapp_mail_message_id \
                          and channel.whatsapp_mail_message_id.author_id == partner) \
                         or len(channel.channel_member_ids) <= 2):
                msg = _(
                    "You can't leave this channel. As you are the owner of this WhatsApp channel, you can only delete it.")
                channel._send_transient_message(partner, msg)
                return

            channel.message_unsubscribe(partner.ids)
            member = self.env['discuss.channel.member'].search(
                [('channel_id', '=', channel.id), ('partner_id', '=', partner.id)])
            if not member:
                return True
            channel_info = channel._channel_info()[0]  # must be computed before leaving the channel (access rights)
            member.unlink()
            # side effect of unsubscribe that wasn't taken into account because
            # channel_info is called before actually unpinning the channel
            channel_info['is_pinned'] = False
            self.env['bus.bus']._sendone(partner, 'discuss.channel/leave', channel_info)
            notification = Markup('<div class="o_mail_notification">%s</div>') % _('left the channel')
            # sudo: mail.message - post as sudo since the user just unsubscribed from the channel
            channel.sudo().message_post(body=notification, subtype_xmlid="mail.mt_comment", author_id=partner.id)
            self.env['bus.bus']._sendone(channel, 'mail.record/insert', {
                'Thread': {
                    'channelMembers': [('DELETE', {'id': member.id})],
                    'id': channel.id,
                    'memberCount': channel.member_count,
                    'model': "discuss.channel",
                }
            })

    def channel_pins(self, pinned=False):
        for rec in self:
            member = self.env['discuss.channel.member'].search(
                [('partner_id', '=', self.env.user.partner_id.id), ('channel_id', '=', rec.id),
                 ('is_pinned', '!=', pinned)])
            if member:
                member.write({'is_pinned': pinned})
            if not pinned:
                self.env['bus.bus']._sendone(self.env.user.partner_id, 'discuss.channel/unpin', {'id': rec.id})
            else:
                self.env['bus.bus']._sendone(self.env.user.partner_id, 'mail.record/insert',
                                             {"Thread": rec._channel_info()[0]})

    def handle_session_assigning(self, available_operator, wa_message_ids=None, is_client_start=False):
        wa_message_id = wa_message_ids[0] if wa_message_ids else None
        _logger.info(f'wa_message_id  {wa_message_id}')
        if not wa_message_ids:
            _logger.error('no watsapp message from message in _notify')
        open_session = self.current_session_id
        if open_session and open_session.session_active:
            if wa_message_id._name == 'mail.message':
                open_session.mail_message_ids = [Command.link(wa_message_id.id)]
                creator_partner_id = wa_message_id.author_id
            else:
                open_session.message_ids = [Command.link(wa_message_id.id)]
                creator_partner_id = wa_message_id.create_uid.partner_id
            if not open_session.agent_response_time and creator_partner_id.id == available_operator.id and (
                    is_client_start or open_session.is_client_start):
                _logger.info('agent response time added')
                open_session.agent_response_time = datetime.datetime.now()
        else:
            if wa_message_id._name == 'mail.message':
                wa_message_id = None
            vals = {
                'channel_id': self.id,
                'is_client_start': is_client_start,
                'client_start_time': datetime.datetime.now() if is_client_start else None,
                'agent_id': available_operator.id,
                'first_message_id': wa_message_id.id if wa_message_id else None,
                'message_ids': [Command.link(wa_message_id.id)] if wa_message_id else None
            }
            current_session_id = self.env['chat.session'].sudo().create(vals)
            self.current_session_id = current_session_id.id
            _logger.info(f"creation of session {vals}")

    @api.depends('session_ids')
    def _compute_session_count(self):
        for rec in self:
            rec.session_count = len(rec.session_ids)


class ChatbotMailMessage(models.Model):
    _inherit = "mail.message"

    wa_chatbot_id = fields.Many2one(
        comodel_name="whatsapp.chatbot", string="Whatsapp Chatbot"
    )
    session_id = fields.Many2one('chat.session')

    @api.model_create_multi
    def create(self, vals_list):
        for vals in vals_list:
            if self._context.get("wa_chatbot_id"):
                whatsapp_chatbot = self.env["whatsapp.chatbot"].search(
                    [("id", "=", self._context.get("wa_chatbot_id"))]
                )
                if whatsapp_chatbot:
                    vals.update(
                        {
                            "wa_chatbot_id": whatsapp_chatbot.id,
                        }
                    )
        return super(ChatbotMailMessage, self).create(vals_list)
