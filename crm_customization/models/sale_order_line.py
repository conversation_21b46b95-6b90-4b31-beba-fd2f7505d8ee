from odoo import models, fields, api, _
from odoo.addons.event_sale.models.sale_order import SaleOrder
from odoo.exceptions import ValidationError


class ShSaleOrder(models.Model):
    _inherit = 'sale.order'

    can_edit = fields.Boolean(compute="_compute_can_edit", store=False)

    def _compute_can_edit(self):
        for order in self:
            # Check if the user belongs to either group
            order.can_edit = self.env.user.has_group('crm_customization.group_sale_price_edit') or \
                             self.env.user.has_group('sales_team.group_sale_price_edit')

    def create_registrations(self):
        for so in self:
            if not any(line.product_type == 'event' for line in so.order_line):
                continue
            so_lines_missing_events = so.order_line.filtered(
                lambda line: line.product_type == 'event' and not line.event_id)
            if so_lines_missing_events:
                so_lines_descriptions = "".join(
                    f"\n- {so_line_description.name}" for so_line_description in so_lines_missing_events)
                raise ValidationError(
                    _("Please make sure all your event related lines are configured before confirming this order:%s",
                      so_lines_descriptions))
            # Initialize registrations
            so.order_line._init_registrations()
            if len(self) == 1:
                return self.env['ir.actions.act_window'].with_context(
                    default_sale_order_id=so.id
                )._for_xml_id('event_sale.action_sale_order_event_registration')


def action_confirm(self):
    res = super(SaleOrder, self).action_confirm()
    for so in self:
        if not any(line.product_type == 'event' for line in so.order_line):
            continue
        so_lines_missing_events = so.order_line.filtered(
            lambda line: line.product_type == 'event' and not line.event_id)
        if so_lines_missing_events:
            so_lines_descriptions = "".join(
                f"\n- {so_line_description.name}" for so_line_description in so_lines_missing_events)
            raise ValidationError(
                _("Please make sure all your event related lines are configured before confirming this order:%s",
                  so_lines_descriptions))
        # Initialize registrations
        # so.order_line._init_registrations()
        # if len(self) == 1:
        #     return self.env['ir.actions.act_window'].with_context(
        #         default_sale_order_id=so.id
        #     )._for_xml_id('event_sale.action_sale_order_event_registration')
    return res


SaleOrder.action_confirm = action_confirm


class SaleOrderLine(models.Model):
    _inherit = 'sale.order.line'

    analytic_account_id = fields.Many2one('account.analytic.account')

    _sql_constraints = [
        # ('accountable_required_fields',
        #  "CHECK(display_type IS NOT NULL OR (product_id IS NOT NULL AND product_uom IS NOT NULL))",
        #  "Missing required fields on accountable sale order line."),
        # ('non_accountable_null_fields',
        #  "CHECK(display_type IS NULL OR (product_id IS NULL AND price_unit = 0 AND product_uom_qty = 0 AND product_uom IS NULL AND customer_lead = 0))",
        #  "Forbidden values on non-accountable sale order line"),
    ]

    @api.onchange('discount')
    def _onchange_discount(self):
        if self.discount and self.order_id:
            message = (
                f"Discount changed to {self.discount}% "
                f"for product '{self.product_id.name}'. "
            )
            self._origin.order_id.message_post(body=message)
