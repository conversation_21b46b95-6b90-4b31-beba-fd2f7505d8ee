# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* website_event_track_gantt
# 
# Translators:
# <PERSON><PERSON> <<EMAIL>>, 2023
# <PERSON><PERSON> <<EMAIL>>, 2023
# <PERSON>, 2023
# <PERSON> <<EMAIL>>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-10-26 22:22+0000\n"
"PO-Revision-Date: 2023-10-26 23:10+0000\n"
"Last-Translator: <PERSON> Ke<PERSON> <<EMAIL>>, 2023\n"
"Language-Team: Hebrew (https://app.transifex.com/odoo/teams/41243/he/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: he\n"
"Plural-Forms: nplurals=4; plural=(n == 1 && n % 1 == 0) ? 0 : (n == 2 && n % 1 == 0) ? 1: (n % 10 == 0 && n % 1 == 0 && n > 10) ? 2 : 3;\n"

#. module: website_event_track_gantt
#: model_terms:ir.ui.view,arch_db:website_event_track_gantt.event_track_view_gantt
msgid "<strong>Proposed By — </strong>"
msgstr ""

#. module: website_event_track_gantt
#: model_terms:ir.ui.view,arch_db:website_event_track_gantt.event_track_view_gantt
msgid "<strong>Start — </strong>"
msgstr ""

#. module: website_event_track_gantt
#. odoo-javascript
#: code:addons/website_event_track_gantt/static/src/event_track_form_view.js:0
#, python-format
msgid "Are you sure you want to delete this track?"
msgstr ""

#. module: website_event_track_gantt
#: model_terms:ir.ui.view,arch_db:website_event_track_gantt.event_track_view_form_in_gantt
msgid "Delete"
msgstr "מחק"

#. module: website_event_track_gantt
#: model_terms:ir.ui.view,arch_db:website_event_track_gantt.event_track_view_form_in_gantt
msgid "Discard"
msgstr "בטל"

#. module: website_event_track_gantt
#: model:ir.model,name:website_event_track_gantt.model_event_event
msgid "Event"
msgstr "אירוע"

#. module: website_event_track_gantt
#: model:ir.model,name:website_event_track_gantt.model_event_track
msgid "Event Track"
msgstr "מעקב ארוע"

#. module: website_event_track_gantt
#: model_terms:ir.ui.view,arch_db:website_event_track_gantt.event_track_view_form_in_gantt
msgid "Save"
msgstr "שמור"

#. module: website_event_track_gantt
#: model:ir.model.fields,field_description:website_event_track_gantt.field_event_event__track_gantt_initial_date
msgid "Track Gantt Initial Date"
msgstr ""

#. module: website_event_track_gantt
#: model:ir.model.fields,field_description:website_event_track_gantt.field_event_event__track_gantt_scale
msgid "Track Gantt Scale"
msgstr ""

#. module: website_event_track_gantt
#: model_terms:ir.ui.view,arch_db:website_event_track_gantt.event_event_view_form
#: model_terms:ir.ui.view,arch_db:website_event_track_gantt.event_track_view_gantt
msgid "Tracks"
msgstr ""

#. module: website_event_track_gantt
#: model_terms:ir.ui.view,arch_db:website_event_track_gantt.event_track_view_form_in_gantt
msgid "Unschedule"
msgstr "הסרה מהלו\"ז"
