# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* pos_pricer
# 
# Translators:
# Wil Odoo, 2024
# Abe Manyo, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-01-31 15:25+0000\n"
"PO-Revision-Date: 2024-02-07 08:49+0000\n"
"Last-Translator: Abe Manyo, 2024\n"
"Language-Team: Indonesian (https://app.transifex.com/odoo/teams/41243/id/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: id\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: pos_pricer
#: model:ir.model.constraint,message:pos_pricer.constraint_pricer_tag_name_unique
msgid "A Pricer tag with this barcode id already exists"
msgstr "Tag PRicer dengan id barcode ini sudah tersedia"

#. module: pos_pricer
#: model:ir.model.fields,field_description:pos_pricer.field_pricer_tag__pricer_store_id
msgid "Associated Pricer Store"
msgstr "Toko Pricer Terkait"

#. module: pos_pricer
#: model:ir.model.fields,field_description:pos_pricer.field_pricer_tag__product_id
msgid "Associated Product"
msgstr "Produk Terkait"

#. module: pos_pricer
#: model:ir.model.fields,field_description:pos_pricer.field_pricer_store__auth_url
msgid "Auth Url"
msgstr "Url Autentikasi"

#. module: pos_pricer
#: model:ir.model.fields,field_description:pos_pricer.field_pricer_store__create_or_update_products_url
msgid "Create Or Update Products Url"
msgstr "Buat atau Update URL Produ"

#. module: pos_pricer
#: model:ir.model.fields,field_description:pos_pricer.field_pricer_store__create_uid
#: model:ir.model.fields,field_description:pos_pricer.field_pricer_tag__create_uid
msgid "Created by"
msgstr "Dibuat oleh"

#. module: pos_pricer
#: model:ir.model.fields,field_description:pos_pricer.field_pricer_store__create_date
#: model:ir.model.fields,field_description:pos_pricer.field_pricer_tag__create_date
msgid "Created on"
msgstr "Dibuat pada"

#. module: pos_pricer
#: model:ir.model.fields,help:pos_pricer.field_pricer_store__last_update_datetime
msgid "Date and time of the last synchronization with Pricer"
msgstr "Tanggal dan waktu sinkronisasi terakhir dengan Pricer"

#. module: pos_pricer
#: model:ir.model.fields,field_description:pos_pricer.field_pricer_store__display_name
#: model:ir.model.fields,field_description:pos_pricer.field_pricer_tag__display_name
msgid "Display Name"
msgstr "Nama Tampilan"

#. module: pos_pricer
#. odoo-python
#: code:addons/pos_pricer/models/pricer_store.py:0
#, python-format
msgid "Error: %s - %s"
msgstr "Error: %s - %s"

#. module: pos_pricer
#. odoo-python
#: code:addons/pos_pricer/models/pricer_store.py:0
#, python-format
msgid "Error: check Pricer credentials"
msgstr "Error: periksa kredensial Pricer"

#. module: pos_pricer
#. odoo-python
#: code:addons/pos_pricer/models/pricer_store.py:0
#, python-format
msgid "Failed to unlink Pricer tag %s at API url %s"
msgstr "Gagal untuk membatalkan tag Pricer %s di url API %s"

#. module: pos_pricer
#: model:ir.model.fields,field_description:pos_pricer.field_pricer_store__id
#: model:ir.model.fields,field_description:pos_pricer.field_pricer_tag__id
msgid "ID"
msgstr "ID"

#. module: pos_pricer
#: model:ir.model.fields,help:pos_pricer.field_pricer_store__pricer_store_identifier
msgid "Identifier of the store in the Pricer system"
msgstr "Pengidentifikasi toko di sistem Pricer"

#. module: pos_pricer
#: model:ir.model.fields,help:pos_pricer.field_pricer_tag__name
msgid "It is recommended to use a barcode scanner for input"
msgstr "Disarankan untuk menggunakan scanner barcode untuk input"

#. module: pos_pricer
#: model:ir.model.fields,field_description:pos_pricer.field_pricer_store__last_update_datetime
msgid "Last Update"
msgstr "Terakhir diperbarui"

#. module: pos_pricer
#: model:ir.model.fields,field_description:pos_pricer.field_pricer_store__last_update_status_message
msgid "Last Update Status"
msgstr "Status Updater Terakhir"

#. module: pos_pricer
#: model:ir.model.fields,field_description:pos_pricer.field_pricer_store__write_uid
#: model:ir.model.fields,field_description:pos_pricer.field_pricer_tag__write_uid
msgid "Last Updated by"
msgstr "Terakhir Diperbarui oleh"

#. module: pos_pricer
#: model:ir.model.fields,field_description:pos_pricer.field_pricer_store__write_date
#: model:ir.model.fields,field_description:pos_pricer.field_pricer_tag__write_date
msgid "Last Updated on"
msgstr "Terakhir Diperbarui pada"

#. module: pos_pricer
#: model:ir.model.fields,field_description:pos_pricer.field_pricer_store__link_tags_url
msgid "Link Tags Url"
msgstr "Link Tag Url"

#. module: pos_pricer
#: model:ir.model.fields,help:pos_pricer.field_pricer_store__pricer_login
msgid "Login of your Pricer account"
msgstr "Login akun Pricer Anda"

#. module: pos_pricer
#: model:ir.actions.server,name:pos_pricer.pricer_sync_cron_ir_actions_server
msgid "POS Pricer: tags update synchronization "
msgstr "POS Pricer: sinkronisasi update tag"

#. module: pos_pricer
#: model:ir.model.fields,help:pos_pricer.field_pricer_store__pricer_password
msgid "Password of your Pricer account"
msgstr "Password akun Pricer Anda"

#. module: pos_pricer
#: model:ir.ui.menu,name:pos_pricer.pos_menu_pricer_configuration
#: model_terms:ir.ui.view,arch_db:pos_pricer.product_template_form_view_pricers
msgid "Pricer"
msgstr "Pricer"

#. module: pos_pricer
#: model:ir.model.fields,field_description:pos_pricer.field_product_product__pricer_display_price
#: model:ir.model.fields,field_description:pos_pricer.field_product_template__pricer_display_price
msgid "Pricer Display Price"
msgstr "Pricer Tampilkan Harga"

#. module: pos_pricer
#: model:ir.model.fields,field_description:pos_pricer.field_pricer_store__pricer_login
msgid "Pricer Login"
msgstr "Pricer Login"

#. module: pos_pricer
#: model:ir.model.fields,field_description:pos_pricer.field_pricer_store__pricer_password
msgid "Pricer Password"
msgstr "Pricer Password"

#. module: pos_pricer
#: model:ir.model.fields,field_description:pos_pricer.field_product_product__pricer_product_to_create_or_update
#: model:ir.model.fields,field_description:pos_pricer.field_product_template__pricer_product_to_create_or_update
msgid "Pricer Product To Create Or Update"
msgstr "Pricer Produk Untuk Dibuat atau Diupdate"

#. module: pos_pricer
#: model:ir.model.fields,field_description:pos_pricer.field_pricer_tag__pricer_product_to_link
msgid "Pricer Product To Link"
msgstr "Pricer Produk Untuk Di-link"

#. module: pos_pricer
#: model:ir.model.fields,field_description:pos_pricer.field_product_product__pricer_store_id
#: model:ir.model.fields,field_description:pos_pricer.field_product_template__pricer_store_id
#: model_terms:ir.ui.view,arch_db:pos_pricer.pricer_pricer_store_form_view
#: model_terms:ir.ui.view,arch_db:pos_pricer.pricer_pricer_store_view_list
msgid "Pricer Store"
msgstr "Pricer Toko"

#. module: pos_pricer
#: model:ir.model.fields,field_description:pos_pricer.field_pricer_store__pricer_store_identifier
msgid "Pricer Store ID"
msgstr "Pricer ID Toko"

#. module: pos_pricer
#. odoo-python
#: code:addons/pos_pricer/models/pricer_store.py:0
#, python-format
msgid ""
"Pricer Store ID must only contain lowercase a-z, 0-9 or '-' and not start "
"with '-'"
msgstr ""
"Pricer ID Toko hanya boleh memiliki huruf kecil a-z, 0-9 atau '-' dan tidak "
"dimulai denga n'-'"

#. module: pos_pricer
#: model:ir.model.fields,help:pos_pricer.field_pricer_store__name
msgid "Pricer Store name in Odoo database"
msgstr "Pricer Nama toko di database Odoo"

#. module: pos_pricer
#: model:ir.model,name:pos_pricer.model_pricer_store
msgid "Pricer Store regrouping pricer tags"
msgstr "Pricer Toko mengelompokkan ulang tag pricer"

#. module: pos_pricer
#: model:ir.actions.act_window,name:pos_pricer.action_open_pricer_stores
#: model:ir.ui.menu,name:pos_pricer.menu_pos_pricer_stores
msgid "Pricer Stores"
msgstr "Pricer Toko-Toko"

#. module: pos_pricer
#: model:ir.model.fields,field_description:pos_pricer.field_pricer_tag__name
msgid "Pricer Tag Barcode ID"
msgstr "Pricer Tag ID Barcode"

#. module: pos_pricer
#: model:ir.actions.act_window,name:pos_pricer.action_open_pricer_tags
#: model:ir.model.fields,field_description:pos_pricer.field_pricer_store__pricer_tag_ids
#: model:ir.ui.menu,name:pos_pricer.menu_pos_pricer_tags
msgid "Pricer Tags"
msgstr "Pricer Tag-Tag"

#. module: pos_pricer
#: model:ir.model.fields,field_description:pos_pricer.field_pricer_store__pricer_tenant_name
msgid "Pricer Tenant Name"
msgstr "Pricer Nama Penyewa"

#. module: pos_pricer
#: model:ir.model,name:pos_pricer.model_pricer_tag
msgid "Pricer electronic tag"
msgstr "Pricer tag elektroni"

#. module: pos_pricer
#: model_terms:ir.ui.view,arch_db:pos_pricer.pricer_pricer_tag_view_list
msgid "Pricer tag"
msgstr "Pricer tag"

#. module: pos_pricer
#: model:ir.model.fields,field_description:pos_pricer.field_product_product__pricer_tag_ids
#: model:ir.model.fields,field_description:pos_pricer.field_product_template__pricer_tag_ids
msgid "Pricer tags ids"
msgstr "Pricer id-id tag"

#. module: pos_pricer
#: model:ir.model,name:pos_pricer.model_product_template
msgid "Product"
msgstr "Produk"

#. module: pos_pricer
#: model:ir.model.fields,field_description:pos_pricer.field_pricer_store__product_ids
msgid "Products"
msgstr "Produk"

#. module: pos_pricer
#: model:ir.model.fields,help:pos_pricer.field_pricer_store__last_update_status_message
msgid "Status message of the last synchronization with Pricer"
msgstr "Pesan status sinkronisasi terakhir dengan Pricer"

#. module: pos_pricer
#: model:ir.model.fields,field_description:pos_pricer.field_pricer_store__name
msgid "Store Name"
msgstr "Nama Toko"

#. module: pos_pricer
#. odoo-python
#: code:addons/pos_pricer/models/pricer_tag.py:0
#, python-format
msgid ""
"Tag id should be a 17 characters string composed of a letter followed by 16 "
"digits"
msgstr ""
"ID Tag harus merupakan urutan 17 karakter terdiri dari huruf diikuti 16 "
"angka"

#. module: pos_pricer
#: model:ir.model.fields,help:pos_pricer.field_pricer_tag__pricer_store_id
#: model:ir.model.fields,help:pos_pricer.field_product_product__pricer_store_id
#: model:ir.model.fields,help:pos_pricer.field_product_template__pricer_store_id
msgid ""
"This product will be linked to and displayed on the Pricer tags of the store"
" selected here"
msgstr ""
"Produk ini akan dihubungkan ke dan ditampilkan pada tag Pricer dari toko "
"yang dipilih di sini"

#. module: pos_pricer
#: model:ir.model.fields,help:pos_pricer.field_product_product__pricer_tag_ids
#: model:ir.model.fields,help:pos_pricer.field_product_template__pricer_tag_ids
msgid ""
"This product will be linked to and displayed on the Pricer tags with ids "
"listed here. It is recommended to use a barcode scanner"
msgstr ""
"Produk ini akan dihubungkan ke dan ditampilkan pada tag-tag Pricer dengan id"
" yang tertulis di sini. Disarankan untuk menggunakan scanner barcode"

#. module: pos_pricer
#: model_terms:ir.ui.view,arch_db:pos_pricer.pricer_pricer_store_view_list
msgid "Update all tags"
msgstr "Update semua tag"

#. module: pos_pricer
#. odoo-python
#: code:addons/pos_pricer/models/pricer_store.py:0
#, python-format
msgid "Update successfully sent to Pricer"
msgstr "Update sukses dikirim ke Pricer"

#. module: pos_pricer
#: model_terms:ir.ui.view,arch_db:pos_pricer.pricer_pricer_store_view_list
msgid "Update tags"
msgstr "Update tag-tag"

#. module: pos_pricer
#: model:ir.model.fields,help:pos_pricer.field_pricer_store__pricer_tenant_name
msgid "Your company identifier at Pricer"
msgstr "Pengidentifikasi perusahaan Anda di Pricer"
