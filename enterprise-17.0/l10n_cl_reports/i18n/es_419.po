# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* l10n_cl_reports
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.2alpha1+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-02-08 08:15+0000\n"
"PO-Revision-Date: 2023-02-08 08:15+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: l10n_cl_reports
#: model:account.report.column,name:l10n_cl_reports.cl_eightcolumns_report_column_assets
msgid "Assets"
msgstr "Activos"

#. module: l10n_cl_reports
#: model:account.report.column,name:l10n_cl_reports.account_financial_report_f29_column
msgid "Balance"
msgstr "Saldo"

#. module: l10n_cl_reports
#: model:account.report,name:l10n_cl_reports.cl_eightcolumns_report
msgid "Chilean Fiscal Balance (8 Columns)"
msgstr "Balanza fiscal chilena (8 columnas)"

#. module: l10n_cl_reports
#: model:ir.model,name:l10n_cl_reports.model_l10n_cl_report_handler
msgid "Chilean Report Custom Handler"
msgstr "Informe chileno Manipulador personalizado"

#. module: l10n_cl_reports
#: model:ir.model,name:l10n_cl_reports.model_res_company
msgid "Companies"
msgstr "Compañías"

#. module: l10n_cl_reports
#: model:ir.model,name:l10n_cl_reports.model_res_config_settings
msgid "Config Settings"
msgstr "Opciones de Configuración"

#. module: l10n_cl_reports
#: model:account.report.column,name:l10n_cl_reports.cl_eightcolumns_report_column_credit
msgid "Credit"
msgstr "Crédito"

#. module: l10n_cl_reports
#: model:account.report.column,name:l10n_cl_reports.cl_eightcolumns_report_column_creditor
msgid "Creditor"
msgstr "Acreedor"

#. module: l10n_cl_reports
#: model:account.report.column,name:l10n_cl_reports.cl_eightcolumns_report_column_debit
msgid "Debit"
msgstr "Débito"

#. module: l10n_cl_reports
#: model:account.report.column,name:l10n_cl_reports.cl_eightcolumns_report_column_debitor
msgid "Debitor"
msgstr "Deudor"

#. module: l10n_cl_reports
#: model:account.report.line,name:l10n_cl_reports.account_financial_report_f29_line_0102
msgid "Exempt Sales"
msgstr "Ventas Exentas"

#. module: l10n_cl_reports
#: model:ir.model.fields,field_description:l10n_cl_reports.field_res_company__l10n_cl_report_fpp_value
#: model:ir.model.fields,field_description:l10n_cl_reports.field_res_config_settings__l10n_cl_report_fpp_value
#: model_terms:ir.ui.view,arch_db:l10n_cl_reports.res_config_settings_f29_view_form
msgid "FPP (%)"
msgstr "FPP (%)"

#. module: l10n_cl_reports
#: model:account.report.column,name:l10n_cl_reports.cl_eightcolumns_report_column_gain
msgid "Gain"
msgstr "Gane"

#. module: l10n_cl_reports
#: model_terms:ir.ui.view,arch_db:l10n_cl_reports.res_config_settings_f29_view_form
msgid ""
"If you use a proportional factor, select this to be used during the F29 "
"proposal for the fiscal year."
msgstr "Si utiliza un factor proporcional, selecciónelo para utilizarlo durante la propuesta F29 para el ejercicio fiscal."

#. module: l10n_cl_reports
#: model:account.report.column,name:l10n_cl_reports.cl_eightcolumns_report_column_liabilities
msgid "Liabilities"
msgstr "Pasivo"

#. module: l10n_cl_reports
#: model:account.report.column,name:l10n_cl_reports.cl_eightcolumns_report_column_loss
msgid "Loss"
msgstr "Pérdida"

#. module: l10n_cl_reports
#: model:account.report.line,name:l10n_cl_reports.account_financial_report_f29_line_0302
msgid "Net Purchases Taxed VAT Common Use"
msgstr "Compra Netas Gravadas IVA Uso Comun"

#. module: l10n_cl_reports
#: model:account.report.line,name:l10n_cl_reports.account_financial_report_f29_line_0101
msgid "Net Sales Taxed VAT"
msgstr "Ventas Netas Gravadas IVA"

#. module: l10n_cl_reports
#: model:account.report.line,name:l10n_cl_reports.account_financial_report_f29_line_0301
msgid "Net Taxable Purchases Recoverable VAT"
msgstr "Compras Netas Gravadas IVA Recuperable"

#. module: l10n_cl_reports
#: model:account.report.line,name:l10n_cl_reports.account_financial_report_f29_line_0607
msgid "PPM"
msgstr "PPM"

#. module: l10n_cl_reports
#: model_terms:ir.ui.view,arch_db:l10n_cl_reports.res_config_settings_f29_view_form
msgid "PPM (%)"
msgstr "PPM (%)"

#. module: l10n_cl_reports
#: model_terms:ir.ui.view,arch_db:l10n_cl_reports.res_config_settings_f29_view_form
msgid "PPM for the fiscal year"
msgstr "PPM para el ejercicio fiscal"

#. module: l10n_cl_reports
#: model:account.report.line,name:l10n_cl_reports.account_financial_report_f29_line_0606
msgid "PPM rate (%)"
msgstr "Tasa de PPM (%)"

#. module: l10n_cl_reports
#. odoo-python
#: code:addons/l10n_cl_reports/models/account_eightcolumns_report.py:0
#, python-format
msgid "Previous years unallocated earnings"
msgstr "Beneficios no asignados de ejercicios anteriores"

#. module: l10n_cl_reports
#. odoo-python
#: code:addons/l10n_cl_reports/models/account_eightcolumns_report.py:0
#, python-format
msgid "Profit and Loss"
msgstr "Pérdidas y ganancias"

#. module: l10n_cl_reports
#: model_terms:ir.ui.view,arch_db:l10n_cl_reports.res_config_settings_f29_view_form
msgid "Proportional Factor for the fiscal year"
msgstr "Factor proporcional para el ejercicio fiscal"

#. module: l10n_cl_reports
#: model:account.report,name:l10n_cl_reports.account_financial_report_f29
msgid "Proposal F29"
msgstr "Propuesta F29"

#. module: l10n_cl_reports
#: model:account.report.line,name:l10n_cl_reports.account_financial_report_f29_line_0103
msgid "Proposed Ratio Factor (%)"
msgstr "Factor de Proporción Propuesto (%)"

#. module: l10n_cl_reports
#: model:account.report.line,name:l10n_cl_reports.account_financial_report_f29_line_0303
msgid "Purchases Non-recoverable VAT"
msgstr "Compras Iva No Recuperable"

#. module: l10n_cl_reports
#: model:account.report.line,name:l10n_cl_reports.account_financial_report_f29_line_0306
msgid "Purchases Not Taxed With VAT"
msgstr "Compras No Gravadas Con Iva"

#. module: l10n_cl_reports
#: model:account.report.line,name:l10n_cl_reports.account_financial_report_f29_line_0305
msgid "Purchases of fixed assets"
msgstr "Compras de Activo Fijo"

#. module: l10n_cl_reports
#: model:account.report.line,name:l10n_cl_reports.account_financial_report_f29_line_0501
msgid "Recoverable VAT"
msgstr "IVA Recuperable"

#. module: l10n_cl_reports
#: model:account.report.line,name:l10n_cl_reports.account_financial_report_f29_line_0603
msgid "Remaining CF"
msgstr "Remanente de CF"

#. module: l10n_cl_reports
#: model_terms:ir.ui.view,arch_db:l10n_cl_reports.res_config_settings_f29_view_form
msgid ""
"Select the value for % PPM that should be used during the current fiscal "
"year."
msgstr "Seleccione el valor de % PPM que debe utilizarse durante el ejercicio en curso."

#. module: l10n_cl_reports
#. odoo-python
#: code:addons/l10n_cl_reports/models/account_eightcolumns_report.py:0
#, python-format
msgid "Subtotal"
msgstr "Subtotal"

#. module: l10n_cl_reports
#: model:account.report.line,name:l10n_cl_reports.account_financial_report_f29_line_0304
msgid "Supermarket Shopping"
msgstr "Compras Supermercado"

#. module: l10n_cl_reports
#: model:ir.model.fields,field_description:l10n_cl_reports.field_res_company__l10n_cl_report_tasa_ppm
#: model:ir.model.fields,field_description:l10n_cl_reports.field_res_config_settings__l10n_cl_report_tasa_ppm
msgid "PPM rate (%)"
msgstr "Tasa PPM (%)"

#. module: l10n_cl_reports
#: model:account.report.line,name:l10n_cl_reports.account_financial_report_f29_line_03
msgid "Tax Base Purchases"
msgstr "Base Imponible Compras"

#. module: l10n_cl_reports
#: model:account.report.line,name:l10n_cl_reports.account_financial_report_f29_line_01
msgid "Taxable Sales Base"
msgstr "Base Imponible Ventas"

#. module: l10n_cl_reports
#: model:account.report.line,name:l10n_cl_reports.account_financial_report_f29_line_04
msgid "Taxes Paid on Purchase"
msgstr "Impuestos Pagados en la Compra"

#. module: l10n_cl_reports
#: model:account.report.line,name:l10n_cl_reports.account_financial_report_f29_line_02
msgid "Taxes Paid on Sale"
msgstr "Impuestos Originados por la Venta"

#. module: l10n_cl_reports
#. odoo-python
#: code:addons/l10n_cl_reports/models/account_eightcolumns_report.py:0
#, python-format
msgid "Total"
msgstr "Total"

#. module: l10n_cl_reports
#: model:account.report.line,name:l10n_cl_reports.account_financial_report_f29_line_0307
msgid "Total Net Purchases"
msgstr "Total Neto Compras"

#. module: l10n_cl_reports
#: model:account.report.line,name:l10n_cl_reports.account_financial_report_f29_line_0104
msgid "Total Sales"
msgstr "Total Ventas"

#. module: l10n_cl_reports
#: model:account.report.line,name:l10n_cl_reports.account_financial_report_f29_line_0608
msgid "Total Taxes for the Period (Negative: Balance in Favor of the Company)"
msgstr "Total de Impuesto Periodo (Negativo: Saldo a Favor de la Compañía)"

#. module: l10n_cl_reports
#: model:account.report.line,name:l10n_cl_reports.account_financial_report_f29_line_06
msgid "Totals"
msgstr "Totales"

#. module: l10n_cl_reports
#: model:account.report.line,name:l10n_cl_reports.account_financial_report_f29_line_05
msgid "VAT Base Tax Credit Affected by FP"
msgstr "Base IVA Credito Fiscal Afectada por FP"

#. module: l10n_cl_reports
#: model:account.report.line,name:l10n_cl_reports.account_financial_report_f29_line_0402
#: model:account.report.line,name:l10n_cl_reports.account_financial_report_f29_line_0502
msgid "VAT Common Use"
msgstr "IVA Uso Comun"

#. module: l10n_cl_reports
#: model:account.report.line,name:l10n_cl_reports.account_financial_report_f29_line_0401
msgid "VAT Paid Purchases Recoverable"
msgstr "IVA Pagado Compras Recuperables"

#. module: l10n_cl_reports
#: model:account.report.line,name:l10n_cl_reports.account_financial_report_f29_line_0602
msgid "VAT Payable (Negative: Balance in Favor of the Company)"
msgstr "IVA a Pagar (Negativo: Saldo a Favor de la Compañía)"

#. module: l10n_cl_reports
#: model:account.report.line,name:l10n_cl_reports.account_financial_report_f29_line_0405
msgid "VAT Purchases Fixed Assets Common Use"
msgstr "IVA Compras Activo Fijo Uso Comun"

#. module: l10n_cl_reports
#: model:account.report.line,name:l10n_cl_reports.account_financial_report_f29_line_0505
msgid "VAT Purchases Fixed Assets Earmarked Sales Exempt Sales"
msgstr "IVA Compras Activo Fijo Destinados Ventas Exentas"

#. module: l10n_cl_reports
#: model:account.report.line,name:l10n_cl_reports.account_financial_report_f29_line_0507
msgid "VAT Purchases Fixed Assets Non Recoverable Sales"
msgstr "IVA Compras Activo Fijo Ventas No Recuperables"

#. module: l10n_cl_reports
#: model:account.report.line,name:l10n_cl_reports.account_financial_report_f29_line_0506
msgid "VAT Purchases Fixed Assets Sales Common Use"
msgstr "IVA Compras Activo Fijo Ventas Uso Comun"

#. module: l10n_cl_reports
#: model:account.report.line,name:l10n_cl_reports.account_financial_report_f29_line_0504
msgid "VAT Purchases Supermarket Common Use"
msgstr "IVA Compras Supermercado Uso Comun"

#. module: l10n_cl_reports
#: model:account.report.line,name:l10n_cl_reports.account_financial_report_f29_line_0404
msgid "VAT Purchases of Fixed Assets Destined for Exempt Sales"
msgstr "IVA Compras Activo Fijo Destinados a Ventas Exentas"

#. module: l10n_cl_reports
#: model:account.report.line,name:l10n_cl_reports.account_financial_report_f29_line_0406
msgid "VAT Purchases of Non-recoverable Fixed Assets"
msgstr "IVA Compras Activo Fijo No Recuperables"

#. module: l10n_cl_reports
#: model:account.report.line,name:l10n_cl_reports.account_financial_report_f29_line_0403
msgid "VAT Shopping Supermarket"
msgstr "IVA Compras Supermercado"

#. module: l10n_cl_reports
#: model:account.report.line,name:l10n_cl_reports.account_financial_report_f29_line_0601
msgid "VAT Tax Credit"
msgstr "IVA Credito Fiscal"

#. module: l10n_cl_reports
#: model:account.report.line,name:l10n_cl_reports.account_financial_report_f29_line_0201
msgid "VAT Tax Debit"
msgstr "IVA Debito Fiscal"

#. module: l10n_cl_reports
#: model:account.report.line,name:l10n_cl_reports.account_financial_report_f29_line_0605
msgid "Withholding Fees"
msgstr "Retencion Honorarios"

#. module: l10n_cl_reports
#: model:account.report.line,name:l10n_cl_reports.account_financial_report_f29_line_0604
msgid "Workers' Tax"
msgstr "Impuesto a los Trabajadores"
