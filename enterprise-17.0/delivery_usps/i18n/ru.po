# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* delivery_usps
# 
# Translators:
# <PERSON><PERSON><PERSON><PERSON><PERSON>, 2023
# <PERSON> <yeli<PERSON><EMAIL>>, 2023
# <PERSON>, 2023
# Wil Odoo, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-10-26 22:20+0000\n"
"PO-Revision-Date: 2023-10-26 23:10+0000\n"
"Last-Translator: Wil Odoo, 2024\n"
"Language-Team: Russian (https://app.transifex.com/odoo/teams/41243/ru/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ru\n"
"Plural-Forms: nplurals=4; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<12 || n%100>14) ? 1 : n%10==0 || (n%10>=5 && n%10<=9) || (n%100>=11 && n%100<=14)? 2 : 3);\n"

#. module: delivery_usps
#: model:ir.model.fields.selection,name:delivery_usps.selection__delivery_carrier__usps_intl_non_delivery_option__abandon
msgid "Abandon"
msgstr "Сдаться"

#. module: delivery_usps
#: model:ir.model.fields,field_description:delivery_usps.field_delivery_carrier__usps_account_validated
msgid "Account Validated"
msgstr "Аккаунт подтвержден"

#. module: delivery_usps
#: model:ir.model.fields,help:delivery_usps.field_delivery_carrier__usps_account_validated
msgid "Check this box if your account is validated by USPS"
msgstr "Установите этот флажок, если ваша учетная запись проверена USPS"

#. module: delivery_usps
#. odoo-python
#: code:addons/delivery_usps/models/usps_request.py:0
#, python-format
msgid "Company phone number is invalid. Please insert a US phone number."
msgstr "Недопустимый номер телефона компании. Укажите номер телефона в США."

#. module: delivery_usps
#: model:ir.model.fields,field_description:delivery_usps.field_delivery_carrier__usps_content_type
msgid "Content Type"
msgstr "Тип содержимого"

#. module: delivery_usps
#: model:ir.model.fields,field_description:delivery_usps.field_delivery_carrier__usps_delivery_nature
msgid "Delivery Nature"
msgstr "Характер доставки"

#. module: delivery_usps
#: model:ir.model.fields.selection,name:delivery_usps.selection__delivery_carrier__usps_content_type__documents
msgid "Documents"
msgstr "Документы"

#. module: delivery_usps
#: model:ir.model.fields.selection,name:delivery_usps.selection__delivery_carrier__usps_delivery_nature__domestic
msgid "Domestic"
msgstr "В пределах страны"

#. module: delivery_usps
#. odoo-python
#: code:addons/delivery_usps/models/delivery_usps.py:0
#, python-format
msgid ""
"Error:\n"
"%s"
msgstr ""
"Ошибка:\n"
"%s"

#. module: delivery_usps
#: model:ir.model.fields.selection,name:delivery_usps.selection__delivery_carrier__usps_service__express
msgid "Express"
msgstr "Экспресс"

#. module: delivery_usps
#: model:ir.model.fields.selection,name:delivery_usps.selection__delivery_carrier__usps_service__first_class
msgid "First Class"
msgstr "Первый класс"

#. module: delivery_usps
#: model:ir.model.fields.selection,name:delivery_usps.selection__delivery_carrier__usps_first_class_mail_type__flat
msgid "Flat"
msgstr "Плоский"

#. module: delivery_usps
#: model:ir.model.fields.selection,name:delivery_usps.selection__delivery_carrier__usps_mail_type__flatrate
msgid "Flat Rate"
msgstr "Единая ставка"

#. module: delivery_usps
#: model:ir.model.fields.selection,name:delivery_usps.selection__delivery_carrier__usps_domestic_regular_container__flat_rate_box
#: model:ir.model.fields.selection,name:delivery_usps.selection__delivery_carrier__usps_international_regular_container__flatratebox
#: model:ir.model.fields.selection,name:delivery_usps.selection__delivery_carrier__usps_mail_type__flatratebox
msgid "Flat Rate Box"
msgstr "Плоская тарифная коробка"

#. module: delivery_usps
#: model:ir.model.fields.selection,name:delivery_usps.selection__delivery_carrier__usps_domestic_regular_container__flat_rate_envelope
#: model:ir.model.fields.selection,name:delivery_usps.selection__delivery_carrier__usps_international_regular_container__flatrateenv
msgid "Flat Rate Envelope"
msgstr "Плоский тарифный конверт"

#. module: delivery_usps
#: model:ir.model.fields.selection,name:delivery_usps.selection__delivery_carrier__usps_content_type__gift
msgid "Gift"
msgstr "Подарок"

#. module: delivery_usps
#: model:ir.model.fields.selection,name:delivery_usps.selection__delivery_carrier__usps_delivery_nature__international
msgid "International"
msgstr "Международный"

#. module: delivery_usps
#: model_terms:ir.ui.view,arch_db:delivery_usps.view_delivery_carrier_form_with_provider_usps
msgid "Label Format"
msgstr "Формат этикетки"

#. module: delivery_usps
#: model:ir.model.fields.selection,name:delivery_usps.selection__delivery_carrier__usps_size_container__large
msgid "Large"
msgstr "Большой"

#. module: delivery_usps
#: model:ir.model.fields.selection,name:delivery_usps.selection__delivery_carrier__usps_mail_type__largeenvelope
msgid "Large Envelope"
msgstr "Большой конверт"

#. module: delivery_usps
#: model:ir.model.fields.selection,name:delivery_usps.selection__delivery_carrier__usps_domestic_regular_container__lg_flat_rate_box
msgid "Large Flat Rate Box"
msgstr "Большая плоская тариф коробки"

#. module: delivery_usps
#: model:ir.model.fields.selection,name:delivery_usps.selection__delivery_carrier__usps_domestic_regular_container__legal_flat_rate_envelope
#: model:ir.model.fields.selection,name:delivery_usps.selection__delivery_carrier__usps_international_regular_container__legalflatrateenv
msgid "Legal Flat Rate Envelope"
msgstr "Юридический плоский тариф конверта"

#. module: delivery_usps
#: model:ir.model.fields.selection,name:delivery_usps.selection__delivery_carrier__usps_first_class_mail_type__letter
#: model:ir.model.fields.selection,name:delivery_usps.selection__delivery_carrier__usps_mail_type__letter
msgid "Letter"
msgstr "Письмо"

#. module: delivery_usps
#: model:ir.model.fields,field_description:delivery_usps.field_delivery_carrier__usps_machinable
msgid "Machinable"
msgstr "Обрабатываемый"

#. module: delivery_usps
#: model:ir.model.fields.selection,name:delivery_usps.selection__delivery_carrier__usps_domestic_regular_container__md_flat_rate_box
msgid "Medium Flat Rate Box"
msgstr "Средняя плоская коробка тарифов"

#. module: delivery_usps
#: model:ir.model.fields.selection,name:delivery_usps.selection__delivery_carrier__usps_content_type__merchandise
msgid "Merchandise"
msgstr "Коммерческие"

#. module: delivery_usps
#: model:ir.model.fields,field_description:delivery_usps.field_delivery_carrier__usps_intl_non_delivery_option
msgid "Non delivery option"
msgstr "Без доставки"

#. module: delivery_usps
#: model:ir.model.fields.selection,name:delivery_usps.selection__delivery_carrier__usps_container__nonrectangular
msgid "Non-rectangular"
msgstr "Непрямоугольная"

#. module: delivery_usps
#: model_terms:ir.ui.view,arch_db:delivery_usps.view_delivery_carrier_form_with_provider_usps
msgid "Options"
msgstr "Опции"

#. module: delivery_usps
#: model:ir.model.fields.selection,name:delivery_usps.selection__delivery_carrier__usps_label_file_type__pdf
msgid "PDF"
msgstr "PDF"

#. module: delivery_usps
#: model:ir.model.fields.selection,name:delivery_usps.selection__delivery_carrier__usps_mail_type__package
msgid "Package"
msgstr "Пакет"

#. module: delivery_usps
#: model:ir.model.fields,field_description:delivery_usps.field_delivery_carrier__usps_custom_container_girth
msgid "Package Girth"
msgstr "Обхват упаковки"

#. module: delivery_usps
#: model:ir.model.fields,field_description:delivery_usps.field_delivery_carrier__usps_custom_container_height
msgid "Package Height"
msgstr "Высота упаковки"

#. module: delivery_usps
#: model:ir.model.fields,field_description:delivery_usps.field_delivery_carrier__usps_custom_container_length
msgid "Package Length"
msgstr "Длина упаковки"

#. module: delivery_usps
#: model:ir.model.fields.selection,name:delivery_usps.selection__delivery_carrier__usps_first_class_mail_type__package_service
msgid "Package Service"
msgstr "Служба посылок"

#. module: delivery_usps
#: model:ir.model.fields,field_description:delivery_usps.field_delivery_carrier__usps_custom_container_width
msgid "Package Width"
msgstr "Ширина упаковки"

#. module: delivery_usps
#: model:ir.model.fields.selection,name:delivery_usps.selection__delivery_carrier__usps_domestic_regular_container__padded_flat_rate_envelope
#: model:ir.model.fields.selection,name:delivery_usps.selection__delivery_carrier__usps_international_regular_container__paddedflatrateenv
msgid "Padded Flat Rate Envelope"
msgstr "Мягкий конверт малого размера"

#. module: delivery_usps
#: model:ir.model.fields.selection,name:delivery_usps.selection__delivery_carrier__usps_first_class_mail_type__parcel
msgid "Parcel"
msgstr "Пакет"

#. module: delivery_usps
#: model:ir.model.fields,help:delivery_usps.field_delivery_carrier__usps_machinable
msgid ""
"Please check on USPS website to ensure that your package is machinable."
msgstr ""
"Пожалуйста, проверьте на сайте USPS, чтобы убедиться, что ваш пакет можно "
"обрабатывать."

#. module: delivery_usps
#. odoo-python
#: code:addons/delivery_usps/models/usps_request.py:0
#, python-format
msgid ""
"Please choose another service (maximum weight of this service is 4 pounds)"
msgstr ""
"Пожалуйста, выберите другую услугу (максимальный вес этой услуги - 4 фунта)"

#. module: delivery_usps
#. odoo-python
#: code:addons/delivery_usps/models/usps_request.py:0
#, python-format
msgid "Please enter a valid ZIP code in recipient address"
msgstr "Укажите действительный почтовый индекс в адресе получателя"

#. module: delivery_usps
#. odoo-python
#: code:addons/delivery_usps/models/usps_request.py:0
#, python-format
msgid "Please enter a valid ZIP code in your Company address"
msgstr "Введите действительный почтовый индекс в адрес вашей компании"

#. module: delivery_usps
#. odoo-python
#: code:addons/delivery_usps/models/usps_request.py:0
#, python-format
msgid "Please provide at least one item to ship."
msgstr "Пожалуйста, укажите хотя бы один предмет для отправки."

#. module: delivery_usps
#. odoo-python
#: code:addons/delivery_usps/models/usps_request.py:0
#, python-format
msgid ""
"Please set country U.S.A in your company address, Service is only available "
"for U.S.A"
msgstr ""
"Пожалуйста, укажите страну США в адресе вашей компании, Сервис доступен "
"только для США"

#. module: delivery_usps
#: model:ir.model.fields.selection,name:delivery_usps.selection__delivery_carrier__usps_first_class_mail_type__postcard
msgid "Postcard"
msgstr "Открытка"

#. module: delivery_usps
#: model:ir.model.fields.selection,name:delivery_usps.selection__delivery_carrier__usps_service__priority
msgid "Priority"
msgstr "Приоритет"

#. module: delivery_usps
#: model:ir.model.fields,field_description:delivery_usps.field_delivery_carrier__delivery_type
msgid "Provider"
msgstr "Провайдер"

#. module: delivery_usps
#. odoo-python
#: code:addons/delivery_usps/models/usps_request.py:0
#, python-format
msgid "Quantity for each move line should be less than 1000."
msgstr "Количество для каждой линии движения должно быть меньше 1000."

#. module: delivery_usps
#. odoo-python
#: code:addons/delivery_usps/models/usps_request.py:0
#, python-format
msgid "Recipient address cannot be found. Please check the address exists."
msgstr ""
"Адрес получателя не найден. Пожалуйста, проверьте, существует ли этот адрес."

#. module: delivery_usps
#: model:ir.model.fields.selection,name:delivery_usps.selection__delivery_carrier__usps_container__rectangular
msgid "Rectangular"
msgstr "Прямоугольный"

#. module: delivery_usps
#: model:ir.model.fields.selection,name:delivery_usps.selection__delivery_carrier__usps_intl_non_delivery_option__redirect
msgid "Redirect"
msgstr "Перенаправление"

#. module: delivery_usps
#: model:ir.model.fields,field_description:delivery_usps.field_delivery_carrier__usps_redirect_partner_id
msgid "Redirect Partner"
msgstr "Партнер переадресации"

#. module: delivery_usps
#: model:ir.model.fields.selection,name:delivery_usps.selection__delivery_carrier__usps_size_container__regular
msgid "Regular"
msgstr "Обычный"

#. module: delivery_usps
#: model:ir.model.fields.selection,name:delivery_usps.selection__delivery_carrier__usps_container__variable
msgid "Regular < 12 inch"
msgstr "Обычный <12 дюймов"

#. module: delivery_usps
#: model:ir.model.fields.selection,name:delivery_usps.selection__delivery_carrier__usps_content_type__return
#: model:ir.model.fields.selection,name:delivery_usps.selection__delivery_carrier__usps_intl_non_delivery_option__return
msgid "Return"
msgstr "Вернуться"

#. module: delivery_usps
#: model:ir.model.fields.selection,name:delivery_usps.selection__delivery_carrier__usps_content_type__sample
msgid "Sample"
msgstr "Пример"

#. module: delivery_usps
#. odoo-python
#: code:addons/delivery_usps/models/delivery_usps.py:0
#, python-format
msgid "Shipment #%s has been cancelled"
msgstr "Отправление №%s было отменено"

#. module: delivery_usps
#. odoo-python
#: code:addons/delivery_usps/models/delivery_usps.py:0
#, python-format
msgid "Shipment created into USPS"
msgstr "Отправление создано в USPS"

#. module: delivery_usps
#. odoo-python
#: code:addons/delivery_usps/models/delivery_usps.py:0
#, python-format
msgid "Shipment created into USPS <br/> <b>Tracking Number: </b>%s"
msgstr "Отправление создано в USPS <br/> <b>Номер отслеживания </b>:%s"

#. module: delivery_usps
#: model:ir.model,name:delivery_usps.model_delivery_carrier
msgid "Shipping Methods"
msgstr "Методы доставки"

#. module: delivery_usps
#: model:ir.model.fields.selection,name:delivery_usps.selection__delivery_carrier__usps_domestic_regular_container__sm_flat_rate_box
msgid "Small Flat Rate Box"
msgstr "Малая плоская коробка тарифа"

#. module: delivery_usps
#: model:ir.model.fields.selection,name:delivery_usps.selection__delivery_carrier__usps_domestic_regular_container__sm_flat_rate_envelope
msgid "Small Flat Rate Envelope"
msgstr "Конверт малого размера"

#. module: delivery_usps
#: model:ir.model.fields.selection,name:delivery_usps.selection__delivery_carrier__usps_label_file_type__tif
msgid "TIF"
msgstr "TIF"

#. module: delivery_usps
#. odoo-python
#: code:addons/delivery_usps/models/usps_request.py:0
#, python-format
msgid ""
"The address of your company is missing or wrong (Missing field(s) :  \n"
" %s)"
msgstr ""
"Адрес вашей компании отсутствует или неправильно (Отсутствующих полей: \n"
"%s)"

#. module: delivery_usps
#. odoo-python
#: code:addons/delivery_usps/models/usps_request.py:0
#, python-format
msgid ""
"The estimated shipping price cannot be computed because the weight is missing for the following product(s): \n"
" %s"
msgstr ""
"Расчетная цена доставки не может быть рассчитана, поскольку отсутствует вес для следующего товара (товаров):\n"
" %s"

#. module: delivery_usps
#. odoo-python
#: code:addons/delivery_usps/models/usps_request.py:0
#, python-format
msgid ""
"The recipient address is missing or wrong (Missing field(s) :  \n"
" %s)"
msgstr ""
"Адрес получателя отсутствует или неправильно (отсутствующих полей: \n"
"%s)"

#. module: delivery_usps
#. odoo-python
#: code:addons/delivery_usps/models/usps_request.py:0
#, python-format
msgid "The selected USPS service (%s) cannot be used to deliver this package."
msgstr ""
"Выбранная служба USPS (%s) не может быть использована для доставки этого "
"пакета."

#. module: delivery_usps
#. odoo-python
#: code:addons/delivery_usps/models/delivery_usps.py:0
#, python-format
msgid "Tracking Number:"
msgstr "Трек номер:"

#. module: delivery_usps
#: model:ir.model.fields,field_description:delivery_usps.field_delivery_carrier__usps_international_regular_container
msgid "Type of USPS International regular container"
msgstr "Тип регулярного контейнера USPS международный"

#. module: delivery_usps
#: model:ir.model.fields,field_description:delivery_usps.field_delivery_carrier__usps_domestic_regular_container
msgid "Type of USPS domestic regular container"
msgstr "Тип внутреннего регулярного контейнера USPS"

#. module: delivery_usps
#: model:ir.model.fields,field_description:delivery_usps.field_delivery_carrier__usps_container
msgid "Type of container"
msgstr "Тип контейнера"

#. module: delivery_usps
#: model:ir.model.fields.selection,name:delivery_usps.selection__delivery_carrier__delivery_type__usps
msgid "USPS"
msgstr "USPS"

#. module: delivery_usps
#: model_terms:ir.ui.view,arch_db:delivery_usps.view_delivery_carrier_form_with_provider_usps
msgid "USPS Configuration"
msgstr "Конфигурация USPS"

#. module: delivery_usps
#: model:delivery.carrier,name:delivery_usps.delivery_carrier_usps_domestic
#: model:product.template,name:delivery_usps.product_product_delivery_usps_domestic_product_template
msgid "USPS Domestic Flat Rate Envelope"
msgstr "USPS внутренний единый тарифный ящик"

#. module: delivery_usps
#. odoo-python
#: code:addons/delivery_usps/models/usps_request.py:0
#, python-format
msgid ""
"USPS Domestic is used only to ship inside of the U.S.A. Please change the "
"delivery method into USPS International."
msgstr ""
"USPS Domestic используется только для доставки внутри США. Пожалуйста, "
"измените способ доставки на USPS International."

#. module: delivery_usps
#: model:ir.model.fields,field_description:delivery_usps.field_delivery_carrier__usps_first_class_mail_type
msgid "USPS First Class Mail Type"
msgstr "Тип почты первого класса USPS"

#. module: delivery_usps
#: model:delivery.carrier,name:delivery_usps.delivery_carrier_usps_international
#: model:product.template,name:delivery_usps.product_product_delivery_usps_international_product_template
msgid "USPS International Flat Rate Box"
msgstr "USPS Международный единый тарифный ящик"

#. module: delivery_usps
#. odoo-python
#: code:addons/delivery_usps/models/usps_request.py:0
#, python-format
msgid ""
"USPS International is used only to ship outside of the U.S.A. Please change "
"the delivery method into USPS Domestic."
msgstr ""
"USPS International используется только для доставки за пределы США. "
"Пожалуйста, измените способ доставки на USPS Domestic."

#. module: delivery_usps
#: model:ir.model.fields,field_description:delivery_usps.field_delivery_carrier__usps_label_file_type
msgid "USPS Label File Type"
msgstr "Тип файла ярлыка USPS"

#. module: delivery_usps
#: model:ir.model.fields,field_description:delivery_usps.field_delivery_carrier__usps_mail_type
msgid "USPS Mail Type"
msgstr "Почтовый тип USPS"

#. module: delivery_usps
#: model:ir.model.fields,field_description:delivery_usps.field_delivery_carrier__usps_service
msgid "USPS Service"
msgstr "Служба USPS"

#. module: delivery_usps
#: model_terms:ir.ui.view,arch_db:delivery_usps.res_config_settings_view_form_sale
#: model_terms:ir.ui.view,arch_db:delivery_usps.res_config_settings_view_form_stock
msgid "USPS Shipping Methods"
msgstr "Способы доставки USPS"

#. module: delivery_usps
#: model:ir.model.fields,field_description:delivery_usps.field_delivery_carrier__usps_username
msgid "USPS User ID"
msgstr "ID пользователя USPS"

#. module: delivery_usps
#: model:ir.model.fields,field_description:delivery_usps.field_delivery_carrier__usps_size_container
msgid "Usps Size Container"
msgstr "Контейнер размера Usps"

#. module: delivery_usps
#. odoo-python
#: code:addons/delivery_usps/models/usps_request.py:0
#, python-format
msgid "Your company or recipient ZIP code is incorrect."
msgstr "Неверный почтовый индекс компании или получателя."

#. module: delivery_usps
#: model_terms:ir.ui.view,arch_db:delivery_usps.view_delivery_carrier_form_with_provider_usps
msgid "inch"
msgstr "дюйм"
