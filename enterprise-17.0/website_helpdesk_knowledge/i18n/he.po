# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* website_helpdesk_knowledge
# 
# Translators:
# <PERSON> <<EMAIL>>, 2023
# <PERSON>, 2023
# <AUTHOR> <EMAIL>, 2023
# NoaFarkash, 2023
# yael terner, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-10-26 22:22+0000\n"
"PO-Revision-Date: 2023-10-26 23:10+0000\n"
"Last-Translator: yael terner, 2023\n"
"Language-Team: Hebrew (https://app.transifex.com/odoo/teams/41243/he/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: he\n"
"Plural-Forms: nplurals=4; plural=(n == 1 && n % 1 == 0) ? 0 : (n == 2 && n % 1 == 0) ? 1: (n % 10 == 0 && n % 1 == 0 && n > 10) ? 2 : 3;\n"

#. module: website_helpdesk_knowledge
#: model_terms:ir.ui.view,arch_db:website_helpdesk_knowledge.search_result
msgid ", by"
msgstr ", מאת"

#. module: website_helpdesk_knowledge
#: model_terms:ir.ui.view,arch_db:website_helpdesk_knowledge.search_result
msgid "<i class=\"fa fa-fw fa-2x fa-book\" title=\"Knowledge Article\"/>"
msgstr ""

#. module: website_helpdesk_knowledge
#: model:ir.model.fields,field_description:website_helpdesk_knowledge.field_helpdesk_team__website_article_id
msgid "Article"
msgstr "מאמר"

#. module: website_helpdesk_knowledge
#: model:ir.model.fields,help:website_helpdesk_knowledge.field_helpdesk_team__website_article_id
msgid ""
"Article on which customers will land by default, and to which the search "
"will be restricted."
msgstr ""

#. module: website_helpdesk_knowledge
#. odoo-python
#: code:addons/website_helpdesk_knowledge/models/helpdesk.py:0
#: model_terms:ir.ui.view,arch_db:website_helpdesk_knowledge.knowledge_base_articles_card
#, python-format
msgid "Articles"
msgstr "מאמרים"

#. module: website_helpdesk_knowledge
#: model_terms:ir.ui.view,arch_db:website_helpdesk_knowledge.knowledge_base_articles_card
msgid "Browse articles"
msgstr ""

#. module: website_helpdesk_knowledge
#: model_terms:ir.ui.view,arch_db:website_helpdesk_knowledge.knowledge_base_articles_card
msgid "Favorites"
msgstr "מועדפים"

#. module: website_helpdesk_knowledge
#: model:ir.model,name:website_helpdesk_knowledge.model_helpdesk_team
msgid "Helpdesk Team"
msgstr "צוות תמיכה"

#. module: website_helpdesk_knowledge
#: model:ir.model,name:website_helpdesk_knowledge.model_knowledge_article
msgid "Knowledge Article"
msgstr "מאמר ידע"

#. module: website_helpdesk_knowledge
#: model:ir.model.fields,field_description:website_helpdesk_knowledge.field_helpdesk_team__website_latest_articles
msgid "Latest Articles"
msgstr ""

#. module: website_helpdesk_knowledge
#: model_terms:ir.ui.view,arch_db:website_helpdesk_knowledge.knowledge_base_articles_card
msgid "Quick Links"
msgstr ""

#. module: website_helpdesk_knowledge
#: model_terms:ir.ui.view,arch_db:website_helpdesk_knowledge.knowledge_base_articles_card
msgid "Search our documentation for answers to common questions"
msgstr ""

#. module: website_helpdesk_knowledge
#: model:ir.model.fields,field_description:website_helpdesk_knowledge.field_helpdesk_team__show_knowledge_base_article
msgid "Show Knowledge Base Article"
msgstr "הצג מאמר מאגר הידע"

#. module: website_helpdesk_knowledge
#. odoo-python
#: code:addons/website_helpdesk_knowledge/models/knowledge_article.py:0
#, python-format
msgid ""
"You cannot delete, unpublish or set a parent on an article that is used by a"
" helpdesk team."
msgstr ""
