# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* account_accountant
# 
# Translators:
# a75f12d3d37ea5bf159c4b3e85eb30e7_0fa6927, 2023
# <PERSON>, 2024
# <PERSON><PERSON>, 2024
# <PERSON><PERSON><PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-01-05 13:45+0000\n"
"PO-Revision-Date: 2023-10-26 23:09+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON>, 2024\n"
"Language-Team: Portuguese (Brazil) (https://app.transifex.com/odoo/teams/41243/pt_BR/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: pt_BR\n"
"Plural-Forms: nplurals=3; plural=(n == 0 || n == 1) ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/models/bank_rec_widget_line.py:0
#, python-format
msgid ""
"%(display_name_html)s with an open amount of %(open_amount)s will be fully "
"reconciled by the transaction."
msgstr ""
"%(display_name_html)s com um valor em aberto de %(open_amount)s será "
"reconciliado totalmente pela transação."

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/models/bank_rec_widget_line.py:0
#, python-format
msgid ""
"%(display_name_html)s with an open amount of %(open_amount)s will be reduced"
" by %(amount)s."
msgstr ""
"%(display_name_html)s com um valor em aberto de %(open_amount)s será "
"reduzido em %(amount)s."

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_form
msgid "-> Reconcile"
msgstr "-> Reconciliar"

#. module: account_accountant
#: model_terms:digest.tip,tip_description:account_accountant.digest_tip_account_accountant_0
msgid "<b class=\"tip_title\">Tip: Bulk update journal items</b>"
msgstr "<b class=\"tip_title\">Dica: atualize itens de diário em massa</b>"

#. module: account_accountant
#: model_terms:digest.tip,tip_description:account_accountant.digest_tip_account_accountant_1
msgid ""
"<b class=\"tip_title\">Tip: Find an Accountant or register your Accounting "
"Firm</b>"
msgstr ""
"<b class=\"tip_title\">Dica: encontre um contador ou registre seu escritório"
" de contabilidade</b>"

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/js/tours/account_accountant.js:0
#, python-format
msgid ""
"<br>See how to manage your customer invoices in the "
"<b>Customers/Invoices</b> menu"
msgstr ""
"<br>Veja como gerenciar suas faturas de clientes no menu "
"<b>Clientes/faturas</b> "

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.account_reconcile_model_form_inherit_account_accountant
msgid ""
"<i title=\"Run manually\" role=\"img\" aria-label=\"Run manually\" class=\"fa fa-refresh\"/>\n"
"                            Run manually"
msgstr ""
"<i title=\"Run manually\" role=\"img\" aria-label=\"Run manually\" class=\"fa fa-refresh\"/>\n"
"                            Executar manualmente"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_change_lock_date
msgid "<i>Lock all journal entries</i>"
msgstr "<i>Travar todos os lançamentos de diário</i>"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_change_lock_date
msgid "<i>Lock specific journal entries</i>"
msgstr "<i>Travar lançamentos de diário específicos</i>"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_move_form_inherit
msgid "<span class=\"o_stat_text\">1 Bank Transaction</span>"
msgstr "<span class=\"o_stat_text\">1 transação bancária</span>"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_move_form_inherit
msgid "<span class=\"o_stat_text\">Bank Statement</span>"
msgstr "<span class=\"o_stat_text\">Extrato bancário</span>"

#. module: account_accountant
#: model_terms:digest.tip,tip_description:account_accountant.digest_tip_account_accountant_1
msgid "<span class=\"tip_button_text\">Find an Accountant</span>"
msgstr "<span class=\"tip_button_text\">Encontre um contador</span>"

#. module: account_accountant
#: model_terms:digest.tip,tip_description:account_accountant.digest_tip_account_accountant_1
msgid "<span class=\"tip_button_text\">Register your Accounting Firm</span>"
msgstr ""
"<span class=\"tip_button_text\">Registre o seu escritório de "
"contabilidade</span>"

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/js/tours/account_accountant.js:0
#, python-format
msgid ""
"<strong><b>Good job!</b> You went through all steps of this tour.</strong>"
msgstr ""
"<strong><b>Muito bem!</b> Você passou por todas as etapas desta "
"tour.</strong>"

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/bank_rec_form.xml:0
#: code:addons/account_accountant/static/src/components/bank_reconciliation/kanban.js:0
#: model:ir.model,name:account_accountant.model_account_account
#: model:ir.model.fields,field_description:account_accountant.field_account_reconcile_wizard__account_id
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__account_id
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_move_line_reconcile_search
#, python-format
msgid "Account"
msgstr "Conta"

#. module: account_accountant
#: model:ir.model,name:account_accountant.model_account_chart_template
msgid "Account Chart Template"
msgstr "Modelo de plano da contas"

#. module: account_accountant
#: model:ir.actions.act_window,name:account_accountant.action_account_group_tree
#: model:ir.ui.menu,name:account_accountant.menu_account_group
msgid "Account Groups"
msgstr "Grupos de contas"

#. module: account_accountant
#: model:ir.actions.act_window,name:account_accountant.account_tag_action
#: model:ir.ui.menu,name:account_accountant.account_tag_menu
msgid "Account Tags"
msgstr "Marcadores de conta"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_reconcile_wizard__transfer_from_account_id
msgid "Account Transfer From"
msgstr "Transferência de conta de"

#. module: account_accountant
#: model:ir.model,name:account_accountant.model_account_auto_reconcile_wizard
msgid "Account automatic reconciliation wizard"
msgstr "Assistente de reconciliação automática de conta"

#. module: account_accountant
#: model:ir.model,name:account_accountant.model_account_reconcile_wizard
msgid "Account reconciliation wizard"
msgstr "Assistente de reconciliação de conta"

#. module: account_accountant
#: model:ir.model.fields,help:account_accountant.field_res_config_settings__deferred_expense_account_id
msgid "Account used for deferred expenses"
msgstr "Conta usada para despesas diferidas"

#. module: account_accountant
#: model:ir.model.fields,help:account_accountant.field_res_config_settings__deferred_revenue_account_id
msgid "Account used for deferred revenues"
msgstr "Conta usada para receitas diferidas"

#. module: account_accountant
#: model:ir.ui.menu,name:account_accountant.menu_accounting
#: model_terms:ir.ui.view,arch_db:account_accountant.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:account_accountant.res_partner_view_form
msgid "Accounting"
msgstr "Financeiro"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_change_lock_date
msgid "Accounting Period Closing"
msgstr "Fechamento de exercício contábil"

#. module: account_accountant
#: model:ir.actions.act_window,name:account_accountant.action_view_account_change_lock_date
msgid "Accounting closing dates"
msgstr "Datas de fechamento contábil"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_auto_reconcile_wizard__account_ids
msgid "Accounts"
msgstr "Contas"

#. module: account_accountant
#: model:ir.model.fields.selection,name:account_accountant.selection__account_auto_reconcile_wizard__search_mode__zero_balance
msgid "Accounts with zero balances"
msgstr "Contas com saldo zerado"

#. module: account_accountant
#: model_terms:ir.actions.act_window,help:account_accountant.account_tag_action
msgid "Add a new tag"
msgstr "Adicionar uma nova tag"

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/js/tours/account_accountant.js:0
#, python-format
msgid ""
"After the data extraction, check and validate the bill. If no vendor has "
"been found, add one before validating."
msgstr ""
"Após a extração de dados, verifique e valide a conta. Se nenhum fornecedor "
"for encontrado, adicione um antes de validar."

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/finish_buttons.xml:0
#, python-format
msgid "All Transactions"
msgstr "Todas as transações"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_change_lock_date__fiscalyear_lock_date
msgid "All Users Lock Date"
msgstr "Data de travamento de todos os usuários"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_reconcile_wizard__reco_model_autocomplete_ids
msgid "All reconciliation models"
msgstr "Todos os modelos de reconciliação"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_reconcile_wizard__allow_partials
msgid "Allow partials"
msgstr "Permitir parciais"

#. module: account_accountant
#: model:res.groups,name:account_accountant.group_fiscal_year
msgid "Allow to define fiscal years of more or less than a year"
msgstr "Permitir definir anos fiscais de mais ou menos de um ano"

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/bank_rec_form.xml:0
#: model:ir.model.fields,field_description:account_accountant.field_account_reconcile_wizard__amount_currency
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_move_line_reconcile_search
#, python-format
msgid "Amount"
msgstr "Valor"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.res_config_settings_view_form
msgid "Amount Computation"
msgstr "Cálculo do valor"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__amount_currency
msgid "Amount Currency"
msgstr "Moeda do valor"

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/bank_rec_form.xml:0
#: code:addons/account_accountant/static/src/components/bank_reconciliation/kanban.js:0
#, python-format
msgid "Amount in Currency"
msgstr "Valor na moeda"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_reconcile_wizard__amount
msgid "Amount in company currency"
msgstr "Valor na moeda da empresa"

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/wizard/account_reconcile_wizard.py:0
#, python-format
msgid ""
"An entry will transfer %(amount)s from %(from_account)s to %(to_account)s."
msgstr ""
"Um lançamento transferirá %(amount)s de %(from_account)s para "
"%(to_account)s."

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/bank_rec_form.xml:0
#, python-format
msgid "Analytic"
msgstr "Analítico"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__analytic_distribution
msgid "Analytic Distribution"
msgstr "Distribuição analítica"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__analytic_distribution_search
msgid "Analytic Distribution Search"
msgstr "Busca na distribuição analítica"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__analytic_precision
msgid "Analytic Precision"
msgstr "Precisão analítica"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_res_config_settings__use_anglo_saxon
msgid "Anglo-Saxon Accounting"
msgstr "Contabilidade anglo-saxã"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_bank_statement_form_with_buttons
msgid "Are you sure you want to delete this statement?"
msgstr "Tem certeza de que deseja excluir este extrato?"

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/move_line_list_reconcile/move_line_list_reconcile.xml:0
#: code:addons/account_accountant/static/src/components/move_line_list_reconcile/move_line_list_reconcile.xml:0
#, python-format
msgid "Auto-reconcile"
msgstr "Autorreconciliar"

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/wizard/account_auto_reconcile_wizard.py:0
#, python-format
msgid "Automatically Reconciled Entries"
msgstr "Lançamentos reconciliados automaticamente"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget__available_reco_model_ids
msgid "Available Reco Model"
msgstr "Modelo de reconciliação disponível"

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/finish_buttons.xml:0
#, python-format
msgid "Back to"
msgstr "Voltar para"

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/global_info.xml:0
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__balance
#, python-format
msgid "Balance"
msgstr "Saldo"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_digest_digest__kpi_account_bank_cash
msgid "Bank & Cash Moves"
msgstr "Movimentações bancárias e de caixa"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__bank_account
msgid "Bank Account"
msgstr "Conta bancária"

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/models/account_bank_statement.py:0
#, python-format
msgid "Bank Reconciliation"
msgstr "Reconciliação bancária"

#. module: account_accountant
#: model:ir.model,name:account_accountant.model_account_bank_statement
#: model_terms:ir.ui.view,arch_db:account_accountant.view_bank_statement_form_bank_rec_widget
msgid "Bank Statement"
msgstr "Extrato bancário"

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/models/account_bank_statement.py:0
#, python-format
msgid "Bank Statement %s.pdf"
msgstr "Extrato bancário %s.pdf"

#. module: account_accountant
#: model:ir.model,name:account_accountant.model_account_bank_statement_line
msgid "Bank Statement Line"
msgstr "Linha de extrato bancário"

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/models/account_bank_statement.py:0
#, python-format
msgid "Bank Statement.pdf"
msgstr "Extrato bancário.pdf"

#. module: account_accountant
#: model:ir.model,name:account_accountant.model_bank_rec_widget
msgid "Bank reconciliation widget for a single statement line"
msgstr "Widget de reconciliação bancária para linhas individuais de extrato"

#. module: account_accountant
#: model:ir.model.fields.selection,name:account_accountant.selection__res_company__deferred_amount_computation_method__day
msgid "Based on days"
msgstr "Baseado em dias"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_change_lock_date
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_reconcile_model_widget_wizard
#: model_terms:ir.ui.view,arch_db:account_accountant.view_bank_statement_line_form_bank_rec_widget
msgid "Cancel"
msgstr "Cancelar"

#. module: account_accountant
#: model:ir.model,name:account_accountant.model_account_change_lock_date
msgid "Change Lock Date"
msgstr "Alterar data de bloqueio"

#. module: account_accountant
#: model:ir.model.fields,help:account_accountant.field_account_reconcile_wizard__to_check
msgid ""
"Check if you are not certain of all the information of the counterpart."
msgstr ""
"Verifique caso não tenha certeza sobre todas as informações da contraparte."

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/move_line_list/move_line_list.xml:0
#, python-format
msgid "Choose a line to preview its attachments."
msgstr "Escolha uma linha para visualizar os anexos."

#. module: account_accountant
#: model_terms:ir.actions.act_window,help:account_accountant.actions_account_fiscal_year
msgid "Click here to create a new fiscal year."
msgstr "Clique aqui para criar um novo ano fiscal."

#. module: account_accountant
#: model_terms:digest.tip,tip_description:account_accountant.digest_tip_account_accountant_1
msgid ""
"Click here to find an accountant or if you want to list out your accounting "
"services on Odoo"
msgstr ""
"Clique aqui para encontrar um contador ou se quiser listar seus serviços de "
"contabilidade no Odoo"

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/js/tours/account_accountant.js:0
#, python-format
msgid ""
"Click on a fetched bank transaction to start the reconciliation process."
msgstr ""
"Clique em uma transação bancária recuperada para iniciar o processo de "
"reconciliação."

#. module: account_accountant
#: model:ir.model,name:account_accountant.model_res_company
msgid "Companies"
msgstr "Empresas"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_auto_reconcile_wizard__company_id
#: model:ir.model.fields,field_description:account_accountant.field_account_fiscal_year__company_id
#: model:ir.model.fields,field_description:account_accountant.field_account_reconcile_wizard__company_id
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget__company_id
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__company_id
msgid "Company"
msgstr "Empresa"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_reconcile_wizard__company_currency_id
msgid "Company currency"
msgstr "Moeda da empresa"

#. module: account_accountant
#: model:ir.model,name:account_accountant.model_res_config_settings
msgid "Config Settings"
msgstr "Configurações"

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/rainbowman_content.xml:0
#, python-format
msgid "Congrats, you're all done!"
msgstr "Parabéns, você concluiu tudo!"

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/js/tours/account_accountant.js:0
#, python-format
msgid "Connect your bank and get your latest transactions."
msgstr "Conecte seu banco e obtenha suas últimas transações."

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/wizard/account_reconcile_wizard.py:0
#, python-format
msgid "Couldn't find an appropriate currency to reconcile selected lines."
msgstr ""
"Não foi possível encontrar uma moeda apropriada para reconciliar as linhas "
"selecionadas."

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_reconcile_model_widget_wizard
msgid "Counterpart Values"
msgstr "Valores de contrapartida"

#. module: account_accountant
#: model:ir.actions.act_window,name:account_accountant.action_bank_statement_form_bank_rec_widget
msgid "Create Statement"
msgstr "Criar demonstrativo"

#. module: account_accountant
#: model_terms:ir.actions.act_window,help:account_accountant.action_account_group_tree
msgid "Create a new account group"
msgstr "Criar um novo grupo de contas"

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/bank_rec_form.xml:0
#, python-format
msgid "Create model"
msgstr "Criar modelo"

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/js/tours/account_accountant.js:0
#, python-format
msgid ""
"Create your first vendor bill.<br/><br/><i>Tip: If you don’t have one on "
"hand, use our sample bill.</i>"
msgstr ""
"Crie sua primeira fatura de fornecedor.<br/><br/><i>Dica: se você não tiver "
"uma em mãos, use nossa fatura de amostra</i>."

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_auto_reconcile_wizard__create_uid
#: model:ir.model.fields,field_description:account_accountant.field_account_change_lock_date__create_uid
#: model:ir.model.fields,field_description:account_accountant.field_account_fiscal_year__create_uid
#: model:ir.model.fields,field_description:account_accountant.field_account_reconcile_wizard__create_uid
msgid "Created by"
msgstr "Criado por"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_auto_reconcile_wizard__create_date
#: model:ir.model.fields,field_description:account_accountant.field_account_change_lock_date__create_date
#: model:ir.model.fields,field_description:account_accountant.field_account_fiscal_year__create_date
#: model:ir.model.fields,field_description:account_accountant.field_account_reconcile_wizard__create_date
msgid "Created on"
msgstr "Criado em"

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/kanban.js:0
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__credit
#, python-format
msgid "Credit"
msgstr "Crédito"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_bank_statement_line__cron_last_check
msgid "Cron Last Check"
msgstr "Última verificação do cron"

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/kanban.js:0
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__currency_id
#: model_terms:ir.ui.view,arch_db:account_accountant.view_move_line_reconcile_tree
#, python-format
msgid "Currency"
msgstr "Moeda"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_reconcile_wizard__reco_currency_id
msgid "Currency to use for reconciliation"
msgstr "Moeda utilizada para reconciliação"

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/models/bank_rec_widget.py:0
#, python-format
msgid "Customer/Vendor"
msgstr "Cliente/fornecedor"

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/bank_rec_form.xml:0
#: code:addons/account_accountant/static/src/components/bank_reconciliation/kanban.js:0
#: model:ir.model.fields,field_description:account_accountant.field_account_reconcile_wizard__date
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__date
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_move_line_reconcile_search
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_move_line_search_bank_rec_widget
#: model_terms:ir.ui.view,arch_db:account_accountant.view_bank_statement_line_search_bank_rec_widget
#, python-format
msgid "Date"
msgstr "Data"

#. module: account_accountant
#: model:ir.model.fields,help:account_accountant.field_account_move_line__deferred_end_date
msgid "Date at which the deferred expense/revenue ends"
msgstr "Data na qual a despesa/receita diferida termina"

#. module: account_accountant
#: model:ir.model.fields,help:account_accountant.field_account_move_line__deferred_start_date
msgid "Date at which the deferred expense/revenue starts"
msgstr "Data na qual a despesa/receita diferida começa"

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/kanban.js:0
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__debit
#, python-format
msgid "Debit"
msgstr "Débito"

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/models/account_move.py:0
#, python-format
msgid "Deferral of %s"
msgstr "Adiamento de %s"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_res_company__deferred_amount_computation_method
#: model:ir.model.fields,field_description:account_accountant.field_res_config_settings__deferred_amount_computation_method
msgid "Deferred Amount Computation Method"
msgstr "Método de computação de valor diferido"

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/models/account_move.py:0
#: model:ir.model.fields,field_description:account_accountant.field_account_bank_statement_line__deferred_move_ids
#: model:ir.model.fields,field_description:account_accountant.field_account_move__deferred_move_ids
#: model:ir.model.fields,field_description:account_accountant.field_account_payment__deferred_move_ids
#: model_terms:ir.ui.view,arch_db:account_accountant.view_move_form_inherit
#, python-format
msgid "Deferred Entries"
msgstr "Lançamentos diferidos"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_res_config_settings__deferred_journal_id
msgid "Deferred Entries Journal"
msgstr "Diário de lançamentos diferidos"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_bank_statement_line__deferred_entry_type
#: model:ir.model.fields,field_description:account_accountant.field_account_move__deferred_entry_type
#: model:ir.model.fields,field_description:account_accountant.field_account_payment__deferred_entry_type
msgid "Deferred Entry Type"
msgstr "Tipo de lançamento diferido"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_res_company__deferred_expense_account_id
#: model:ir.model.fields,field_description:account_accountant.field_res_config_settings__deferred_expense_account_id
#: model:ir.model.fields.selection,name:account_accountant.selection__account_move__deferred_entry_type__expense
msgid "Deferred Expense"
msgstr "Despesa diferida"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_res_company__deferred_journal_id
msgid "Deferred Journal"
msgstr "Diário diferido"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_res_company__deferred_revenue_account_id
#: model:ir.model.fields,field_description:account_accountant.field_res_config_settings__deferred_revenue_account_id
#: model:ir.model.fields.selection,name:account_accountant.selection__account_move__deferred_entry_type__revenue
msgid "Deferred Revenue"
msgstr "Receita diferida"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.res_config_settings_view_form
msgid "Define fiscal years of more or less than one year"
msgstr "Definir anos fiscais de mais ou menos que um ano"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_bank_statement_form_with_buttons
msgid "Delete"
msgstr "Excluir"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_bank_statement_line_search_bank_rec_widget
msgid "Deposits"
msgstr "Depósitos"

#. module: account_accountant
#: model:ir.model,name:account_accountant.model_digest_digest
msgid "Digest"
msgstr "Resumo"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_auto_reconcile_wizard
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_reconcile_wizard
#: model_terms:ir.ui.view,arch_db:account_accountant.view_bank_statement_form_with_buttons
msgid "Discard"
msgstr "Descartar"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_move_line_reconcile_tree
msgid "Discount Amount"
msgstr "Valor do desconto"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_move_line_reconcile_tree
msgid "Discount Date"
msgstr "Data do desconto"

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/bank_rec_form.xml:0
#, python-format
msgid "Discuss"
msgstr "Mensagens"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_reconcile_wizard__display_allow_partials
msgid "Display Allow Partials"
msgstr "Exibir Permitir parciais"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_auto_reconcile_wizard__display_name
#: model:ir.model.fields,field_description:account_accountant.field_account_change_lock_date__display_name
#: model:ir.model.fields,field_description:account_accountant.field_account_fiscal_year__display_name
#: model:ir.model.fields,field_description:account_accountant.field_account_reconcile_wizard__display_name
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget__display_name
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__display_name
msgid "Display Name"
msgstr "Nome exibido"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__display_stroked_amount_currency
msgid "Display Stroked Amount Currency"
msgstr "Exibir moeda do valor corrigido"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__display_stroked_balance
msgid "Display Stroked Balance"
msgstr "Exibir saldo corrigido"

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/models/digest.py:0
#, python-format
msgid "Do not have access, skip this data for user's digest email"
msgstr ""
"Se não tiver acesso, ignore esses dados para o e-mail de resumo do usuário"

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/kanban.js:0
#, python-format
msgid "Edit Statement"
msgstr "Editar demonstrativo"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_fiscal_year__date_to
#: model:ir.model.fields,field_description:account_accountant.field_account_move_line__deferred_end_date
msgid "End Date"
msgstr "Data final"

#. module: account_accountant
#: model:ir.model.fields,help:account_accountant.field_account_fiscal_year__date_to
msgid "Ending Date, included in the fiscal year."
msgstr "Data final, incluída no ano fiscal."

#. module: account_accountant
#: model:ir.model.fields.selection,name:account_accountant.selection__res_company__deferred_amount_computation_method__month
msgid "Equal per month"
msgstr "Igual por mês"

#. module: account_accountant
#: model:ir.model.fields,help:account_accountant.field_res_company__invoicing_switch_threshold
#: model:ir.model.fields,help:account_accountant.field_res_config_settings__invoicing_switch_threshold
msgid ""
"Every payment and invoice before this date will receive the 'From Invoicing'"
" status, hiding all the accounting entries related to it. Use this option "
"after installing Accounting if you were using only Invoicing before, before "
"importing all your actual accounting data in to Odoo."
msgstr ""
"Cada pagamento e fatura antes desta data receberá o status “Da fatura”, "
"ocultando todos os lançamentos financeiros relacionados a ela. Use esta "
"opção após a instalação do Financeiro se antes você estava usando somente o "
"Faturamento, antes de importar todos os seus dados financeiros atuais para o"
" Odoo."

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/models/bank_rec_widget.py:0
#, python-format
msgid "Exchange Difference: %s"
msgstr "Diferença de câmbio: %s"

#. module: account_accountant
#: model:ir.actions.act_window,name:account_accountant.action_open_auto_reconcile_wizard
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_auto_reconcile_wizard
msgid "Find Entries to Reconcile Automatically"
msgstr "Encontrar lançamentos para reconciliar automaticamente"

#. module: account_accountant
#: model:ir.model,name:account_accountant.model_account_fiscal_year
#: model_terms:ir.ui.view,arch_db:account_accountant.res_config_settings_view_form
msgid "Fiscal Year"
msgstr "Ano fiscal"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.action_account_fiscal_year_form
msgid "Fiscal Year 2018"
msgstr "Ano fiscal 2018"

#. module: account_accountant
#: model:ir.actions.act_window,name:account_accountant.actions_account_fiscal_year
#: model:ir.model.fields,field_description:account_accountant.field_res_config_settings__group_fiscal_year
#: model:ir.ui.menu,name:account_accountant.menu_account_fiscal_year
#: model_terms:ir.ui.view,arch_db:account_accountant.res_config_settings_view_form
msgid "Fiscal Years"
msgstr "Anos fiscais"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_res_config_settings__fiscalyear_last_day
msgid "Fiscalyear Last Day"
msgstr "Último dia do ano fiscal"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_res_config_settings__fiscalyear_last_month
msgid "Fiscalyear Last Month"
msgstr "Último mês do ano fiscal"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__flag
msgid "Flag"
msgstr "Sinalizar"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_reconcile_wizard__force_partials
msgid "Force Partials"
msgstr "Forçar parciais"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__force_price_included_taxes
msgid "Force Price Included Taxes"
msgstr "Forçar preço incluindo impostos"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget__form_index
msgid "Form Index"
msgstr "Índice do formulário"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_auto_reconcile_wizard__from_date
msgid "From"
msgstr "De"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_move_line_reconcile_search
msgid "From Trade Payable accounts"
msgstr "De contas a pagar comerciais"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_move_line_reconcile_search
msgid "From Trade Receivable accounts"
msgstr "De contas a receber comerciais"

#. module: account_accountant
#: model_terms:digest.tip,tip_description:account_accountant.digest_tip_account_accountant_0
msgid ""
"From any list view, select multiple records and the list becomes editable. "
"If you update a cell, selected records are updated all at once. Use this "
"feature to update multiple journal entries from the General Ledger, or any "
"Journal view."
msgstr ""
"A partir de qualquer visualização de lista, selecione vários registros e a "
"lista se torna editável. Se você atualizar uma célula, os registros "
"selecionados são atualizados todos de uma só vez. Use este recurso para "
"atualizar vários lançamentos de diário do Livro Razão, ou qualquer visão de "
"diário."

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_res_company__generate_deferred_expense_entries_method
#: model:ir.model.fields,field_description:account_accountant.field_res_config_settings__generate_deferred_expense_entries_method
msgid "Generate Deferred Expense Entries Method"
msgstr "Gerar método de lançamentos de despesas diferidos"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_res_company__generate_deferred_revenue_entries_method
#: model:ir.model.fields,field_description:account_accountant.field_res_config_settings__generate_deferred_revenue_entries_method
msgid "Generate Deferred Revenue Entries Method"
msgstr "Gerar método de lançamentos de receitas diferidos"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.res_config_settings_view_form
msgid "Generate Expense Entries"
msgstr "Gerar lançamentos de despesas"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.res_config_settings_view_form
msgid "Generate Revenue Entries"
msgstr "Gerar lançamentos de receitas"

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/js/tours/account_accountant.js:0
#: code:addons/account_accountant/static/src/js/tours/account_accountant.js:0
#: code:addons/account_accountant/static/src/js/tours/account_accountant.js:0
#, python-format
msgid "Go to invoicing"
msgstr "Ir para faturamento"

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/js/tours/account_accountant.js:0
#, python-format
msgid ""
"Great! Let’s continue.<br/><br/><i>Tip: If you choose to upload your bill, "
"don’t forget to attach it.</i>"
msgstr ""
"Ótimo! Vamos continuar.<br/><br/><i>Dica: se você optar por carregar sua "
"conta, não esqueça de anexá-la.</i>"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_move_line_reconcile_search
msgid "Group By"
msgstr "Agrupar por"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__group_tax_id
msgid "Group Tax"
msgstr "Imposto de grupo"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_move_line__has_deferred_moves
msgid "Has Deferred Moves"
msgstr "Tem movimentações diferidas"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_auto_reconcile_wizard__id
#: model:ir.model.fields,field_description:account_accountant.field_account_change_lock_date__id
#: model:ir.model.fields,field_description:account_accountant.field_account_fiscal_year__id
#: model:ir.model.fields,field_description:account_accountant.field_account_reconcile_wizard__id
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget__id
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__id
msgid "ID"
msgstr "ID"

#. module: account_accountant
#: model:ir.model.fields,help:account_accountant.field_bank_rec_widget__st_line_to_check
msgid ""
"If this checkbox is ticked, it means that the user was not sure of all the "
"related information at the time of the creation of the move and that the "
"move needs to be checked again."
msgstr ""
"Se esta caixa de seleção estiver marcada, significa que o usuário não tinha "
"certeza de todas as informações relacionadas no momento da criação da "
"movimentação e que a movimentação precisa ser verificada novamente."

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_move_line_search_bank_rec_widget
msgid "Incoming"
msgstr "Entrada"

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/models/res_config_settings.py:0
#, python-format
msgid ""
"Incorrect fiscal year date: day is out of range for month. Month: %s; Day: "
"%s"
msgstr ""
"Data do ano fiscal incorreta: o dia está fora do intervalo do mês. Mês: %s; "
"Dia: %s"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__index
msgid "Index"
msgstr "Índice"

#. module: account_accountant
#: model:ir.model.fields.selection,name:account_accountant.selection__bank_rec_widget__state__invalid
msgid "Invalid"
msgstr "Inválido"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_bank_statement_line_search_bank_rec_widget
msgid "Invalid statements"
msgstr "Extratos inválidos"

#. module: account_accountant
#: model:ir.model.fields,help:account_accountant.field_bank_rec_widget__state
msgid ""
"Invalid: The bank transaction can't be validate since the suspense account is still involved\n"
"Valid: The bank transaction can be validated.\n"
"Reconciled: The bank transaction has already been processed. Nothing left to do."
msgstr ""
"Inválido: a transação bancária não pode ser validada já que a conta transitória ainda está envolvida.\n"
"Válido: a transação bancária pode ser validada.\n"
"Reconciliado: a transação bancária já foi processada. Não há mais nada a fazer."

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_move_line_list_bank_rec_widget
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_move_line_reconcile_search
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_move_line_search_bank_rec_widget
#: model_terms:ir.ui.view,arch_db:account_accountant.view_bank_statement_line_search_bank_rec_widget
#: model_terms:ir.ui.view,arch_db:account_accountant.view_move_line_reconcile_tree
msgid "Invoice Date"
msgstr "Data da fatura"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_res_company__invoicing_switch_threshold
#: model:ir.model.fields,field_description:account_accountant.field_res_config_settings__invoicing_switch_threshold
msgid "Invoicing Switch Threshold"
msgstr "Limiar do switch de faturamento"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget__st_line_is_reconciled
msgid "Is Reconciled"
msgstr "Foi reconciliado"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_reconcile_wizard__is_transfer_required
msgid "Is an account transfer required"
msgstr "É uma transferência de conta necessária"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_reconcile_wizard__transfer_warning_message
msgid "Is an account transfer required to reconcile"
msgstr "É uma transferência de conta necessária para reconciliar"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_reconcile_wizard__can_silently_reconcile
msgid "Is silent reconcile possible"
msgstr "A reconciliação silenciosa é possível"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_reconcile_wizard__lock_date_violated_warning_message
msgid "Is the date violating the lock date of moves"
msgstr "A data viola o período de bloqueio das movimentações"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.account_journal_dashboard_kanban_view
msgid "Items"
msgstr "Itens "

#. module: account_accountant
#: model:ir.model,name:account_accountant.model_account_journal
#: model:ir.model.fields,field_description:account_accountant.field_account_reconcile_wizard__journal_id
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget__st_line_journal_id
#: model_terms:ir.ui.view,arch_db:account_accountant.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_move_line_reconcile_search
#: model_terms:ir.ui.view,arch_db:account_accountant.view_bank_statement_line_search_bank_rec_widget
msgid "Journal"
msgstr "Diário"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget__journal_currency_id
msgid "Journal Currency"
msgstr "Moeda do diário"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_change_lock_date__period_lock_date
msgid "Journal Entries Lock Date"
msgstr "Período de bloqueio dos lançamentos de diário"

#. module: account_accountant
#: model:ir.model,name:account_accountant.model_account_move
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget__move_id
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_move_line_reconcile_search
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_move_line_search_bank_rec_widget
#: model_terms:ir.ui.view,arch_db:account_accountant.view_bank_statement_line_search_bank_rec_widget
#: model_terms:ir.ui.view,arch_db:account_accountant.view_move_line_reconcile_tree
msgid "Journal Entry"
msgstr "Lançamento de diário"

#. module: account_accountant
#: model:ir.model,name:account_accountant.model_account_move_line
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_move_line_reconcile_search
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_move_line_search_bank_rec_widget
msgid "Journal Item"
msgstr "Item do diário"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_move_line_reconcile_tree
msgid "Journal Items"
msgstr "Itens do diário"

#. module: account_accountant
#: model:ir.actions.act_window,name:account_accountant.action_move_line_posted_unreconciled
msgid "Journal Items to reconcile"
msgstr "Itens de diário a reconciliar"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_move_line_reconcile_search
msgid "Journal items where matching number isn't set"
msgstr "Itens de diário em que o número correspondente não está definido"

#. module: account_accountant
#: model:ir.model.fields,help:account_accountant.field_res_config_settings__deferred_journal_id
msgid "Journal used for deferred entries"
msgstr "Diário utilizado para lançamentos diferidos"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_digest_digest__kpi_account_bank_cash_value
msgid "Kpi Account Bank Cash Value"
msgstr "KPI - Valor em dinheiro no banco da conta "

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/bank_rec_form.xml:0
#: model:ir.model.fields,field_description:account_accountant.field_account_reconcile_wizard__label
#, python-format
msgid "Label"
msgstr "Rótulo"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.res_config_settings_view_form
msgid "Last Day"
msgstr "Último dia"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_auto_reconcile_wizard__write_uid
#: model:ir.model.fields,field_description:account_accountant.field_account_change_lock_date__write_uid
#: model:ir.model.fields,field_description:account_accountant.field_account_fiscal_year__write_uid
#: model:ir.model.fields,field_description:account_accountant.field_account_reconcile_wizard__write_uid
msgid "Last Updated by"
msgstr "Última atualização por"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_auto_reconcile_wizard__write_date
#: model:ir.model.fields,field_description:account_accountant.field_account_change_lock_date__write_date
#: model:ir.model.fields,field_description:account_accountant.field_account_fiscal_year__write_date
#: model:ir.model.fields,field_description:account_accountant.field_account_reconcile_wizard__write_date
msgid "Last Updated on"
msgstr "Última atualização em"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_auto_reconcile_wizard
msgid "Launch"
msgstr "Executar"

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/js/tours/account_accountant.js:0
#, python-format
msgid "Let’s automate your bills, bank transactions and accounting processes."
msgstr ""
"Vamos automatizar suas contas, transações bancárias e processos financeiros."

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/js/tours/account_accountant.js:0
#, python-format
msgid "Let’s go back to the dashboard."
msgstr "Vamos voltar ao painel."

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/js/tours/account_accountant.js:0
#, python-format
msgid "Let’s see how a bill looks like in form view."
msgstr "Vamos ver uma fatura na visualização de formulário."

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/js/tours/account_accountant.js:0
#, python-format
msgid ""
"Let’s use AI to fill in the form<br/><br/><i>Tip: If the OCR is not done "
"yet, wait a few more seconds and try again.</i>"
msgstr ""
"Vamos usar IA para preencher o formulário<br/><br/><i>Dica: se o OCR ainda "
"não estiver pronto, aguarde mais alguns segundos e tente novamente</i>."

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_auto_reconcile_wizard__line_ids
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget__line_ids
msgid "Line"
msgstr "Linha"

#. module: account_accountant
#: model:ir.model,name:account_accountant.model_bank_rec_widget_line
msgid "Line of the bank reconciliation widget"
msgstr "Linha do widget de reconciliação bancária"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_res_config_settings__fiscalyear_lock_date
msgid "Lock Date for All Users"
msgstr "Bloquear a data para todos os usuários"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_res_config_settings__period_lock_date
msgid "Lock Date for Non-Advisers"
msgstr "Período de bloqueio para não consultores"

#. module: account_accountant
#: model:ir.ui.menu,name:account_accountant.menu_action_change_lock_date
msgid "Lock Dates"
msgstr "Bloquear datas"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_change_lock_date
msgid "Management Closing"
msgstr "Fechamento da gestão"

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/bank_rec_form.xml:0
#, python-format
msgid "Manual Operations"
msgstr "Operações Manuais"

#. module: account_accountant
#: model:ir.model.fields.selection,name:account_accountant.selection__res_company__generate_deferred_expense_entries_method__manual
#: model:ir.model.fields.selection,name:account_accountant.selection__res_company__generate_deferred_revenue_entries_method__manual
msgid "Manually & Grouped"
msgstr "Manualmente e agrupado"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__manually_modified
msgid "Manually Modified"
msgstr "Modificado manualmente"

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/list_view_switcher.js:0
#: code:addons/account_accountant/static/src/components/matching_link_widget/matching_link_widget.xml:0
#, python-format
msgid "Match"
msgstr "Corresponder"

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/bank_rec_form.xml:0
#, python-format
msgid "Match Existing Entries"
msgstr "Conciliar lançamentos existentes"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_bank_statement_line_kanban_bank_rec_widget
#: model_terms:ir.ui.view,arch_db:account_accountant.view_bank_statement_line_search_bank_rec_widget
msgid "Matched"
msgstr "Identificado"

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/models/account_payment.py:0
#, python-format
msgid "Matched Transactions"
msgstr "Transações correspondidas"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget__matching_rules_allow_auto_reconcile
msgid "Matching Rules Allow Auto Reconcile"
msgstr "Regras de correspondência para permitir autorreconciliação"

#. module: account_accountant
#: model:ir.model.fields,help:account_accountant.field_res_config_settings__deferred_amount_computation_method
msgid "Method used to compute the amount of deferred entries"
msgstr "Método usado para computar o valor dos lançamentos diferidos"

#. module: account_accountant
#: model:ir.model.fields,help:account_accountant.field_res_config_settings__generate_deferred_expense_entries_method
msgid "Method used to generate deferred expense entries"
msgstr "Método usado para gerar lançamentos de despesas diferidas"

#. module: account_accountant
#: model:ir.model.fields,help:account_accountant.field_res_config_settings__generate_deferred_revenue_entries_method
msgid "Method used to generate deferred revenue entries"
msgstr "Método usado para gerar lançamentos de receita diferidos"

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/models/bank_rec_widget.py:0
#, python-format
msgid "Misc"
msgstr "Diversos"

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/bank_rec_form.xml:0
#, python-format
msgid "More"
msgstr "Mais"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_move_line__move_attachment_ids
msgid "Move Attachment"
msgstr "Mover anexo"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_reconcile_wizard__move_line_ids
msgid "Move lines to reconcile"
msgstr "Linhas de movimentação a reconciliar"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_fiscal_year__name
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__name
msgid "Name"
msgstr "Nome"

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/bank_rec_form.xml:0
#, python-format
msgid "New"
msgstr "Novo"

#. module: account_accountant
#: model:ir.actions.act_window,name:account_accountant.action_bank_statement_line_form_bank_rec_widget
#: model_terms:ir.ui.view,arch_db:account_accountant.account_journal_dashboard_kanban_view
#: model_terms:ir.ui.view,arch_db:account_accountant.view_bank_statement_line_form_bank_rec_widget
msgid "New Transaction"
msgstr "Nova transação"

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/move_line_list/move_line_list.xml:0
#, python-format
msgid "No attachments linked."
msgstr "Sem arquivos anexados."

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_bank_statement_line_search_bank_rec_widget
msgid "No statement"
msgstr "Nenhum extrato"

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/models/account_bank_statement.py:0
#, python-format
msgid "No transactions matching your filters were found."
msgstr "Não foram encontradas transações correspondentes aos seus filtros."

#. module: account_accountant
#: model:ir.model.fields,help:account_accountant.field_res_config_settings__tax_lock_date
msgid ""
"No users can edit journal entries related to a tax prior and inclusive of "
"this date."
msgstr ""
"Nenhum usuário pode editar itens do diário relacionados aos impostos em "
"datas anteriores inclusive desta data."

#. module: account_accountant
#: model:ir.model.fields,help:account_accountant.field_res_config_settings__fiscalyear_lock_date
msgid ""
"No users, including Advisers, can edit accounts prior to and inclusive of "
"this date. Use it for fiscal year locking for example."
msgstr ""
"Nenhum usuário, incluindo consultores, pode editar contas anteriores, "
"inclusive desta data. Use isso para bloqueio de ano fiscal, por exemplo."

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_bank_statement_line_search_bank_rec_widget
msgid "Not Matched"
msgstr "Não identificado"

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/bank_rec_form.xml:0
#: code:addons/account_accountant/static/src/components/bank_reconciliation/bank_rec_form.xml:0
#: model_terms:ir.ui.view,arch_db:account_accountant.view_bank_statement_line_search_bank_rec_widget
#: model_terms:ir.ui.view,arch_db:account_accountant.view_bank_statement_line_tree_bank_rec_widget
#, python-format
msgid "Notes"
msgstr "Notas"

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/models/account_bank_statement.py:0
#, python-format
msgid "Nothing to do here!"
msgstr "Nada para fazer!"

#. module: account_accountant
#: model:ir.model.fields.selection,name:account_accountant.selection__res_company__generate_deferred_expense_entries_method__on_validation
msgid "On bill validation"
msgstr "Na validação da conta"

#. module: account_accountant
#: model:ir.model.fields.selection,name:account_accountant.selection__res_company__generate_deferred_revenue_entries_method__on_validation
msgid "On invoice validation"
msgstr "Na validação da fatura"

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/wizard/account_change_lock_date.py:0
#, python-format
msgid "Only Billing Administrators are allowed to change lock dates!"
msgstr ""
"Somente os Administradores de faturamento estão autorizados a mudar as datas"
" de bloqueio!"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_reconcile_wizard
msgid ""
"Only partial reconciliation is possible. Proceed in multiple steps if you "
"want to full reconcile."
msgstr ""
"Somente a reconciliação parcial é possível. Prossiga em várias etapas se "
"quiser fazer a reconciliação completa."

#. module: account_accountant
#: model:ir.model.fields,help:account_accountant.field_res_config_settings__period_lock_date
msgid ""
"Only users with the 'Adviser' role can edit accounts prior to and inclusive "
"of this date. Use it for period locking inside an open fiscal year, for "
"example."
msgstr ""
"Somente usuários com a função 'Consultor' podem editar contas anteriores, "
"incluindo esta data. Use isso para bloquear um período dentro do ano fiscal "
"aberto, por exemplo."

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_move_line_list_bank_rec_widget
msgid "Open Amount"
msgstr "Valor em aberto"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_move_line_list_bank_rec_widget
msgid "Open Amount in Currency"
msgstr "Valor em aberto na moeda"

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/models/bank_rec_widget.py:0
#, python-format
msgid "Open balance: %s"
msgstr "Saldo aberto: %s"

#. module: account_accountant
#: model:ir.model.fields.selection,name:account_accountant.selection__account_auto_reconcile_wizard__search_mode__one_to_one
msgid "Opposite balances one by one"
msgstr "Saldos opostos um por um"

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/models/account_move.py:0
#, python-format
msgid "Original Deferred Entries"
msgstr "Lançamentos diferidos originais"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_bank_statement_line__deferred_original_move_ids
#: model:ir.model.fields,field_description:account_accountant.field_account_move__deferred_original_move_ids
#: model:ir.model.fields,field_description:account_accountant.field_account_payment__deferred_original_move_ids
msgid "Original Invoices"
msgstr "Faturas originais"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_move_line_reconcile_search
#: model_terms:ir.ui.view,arch_db:account_accountant.view_move_line_reconcile_tree
msgid "Originator Tax"
msgstr "Imposto originário"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_move_line_search_bank_rec_widget
msgid "Outgoing"
msgstr "Saída"

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/bank_rec_form.xml:0
#: code:addons/account_accountant/static/src/components/bank_reconciliation/kanban.js:0
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget__partner_id
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__partner_id
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_move_line_reconcile_search
#, python-format
msgid "Partner"
msgstr "Parceiro"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__partner_currency_id
msgid "Partner Currency"
msgstr "Moeda do parceiro"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget__partner_name
msgid "Partner Name"
msgstr "Nome do parceiro"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__partner_payable_account_id
msgid "Partner Payable Account"
msgstr "Conta a pagar do parceiro"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__partner_payable_amount
msgid "Partner Payable Amount"
msgstr "Valor a pagar do parceiro"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__partner_receivable_account_id
msgid "Partner Receivable Account"
msgstr "Conta a receber do parceiro"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__partner_receivable_amount
msgid "Partner Receivable Amount"
msgstr "Valor a receber do parceiro"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_auto_reconcile_wizard__partner_ids
msgid "Partners"
msgstr "Parceiros"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_move_line_reconcile_search
msgid "Payable"
msgstr "A pagar"

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/bank_rec_form.xml:0
#, python-format
msgid "Payable:"
msgstr "Pagável:"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_payment_form_inherit_account_accountant
msgid "Payment Matching"
msgstr "Identificação de pagamento"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_bank_statement_line__payment_state_before_switch
#: model:ir.model.fields,field_description:account_accountant.field_account_move__payment_state_before_switch
#: model:ir.model.fields,field_description:account_accountant.field_account_payment__payment_state_before_switch
msgid "Payment State Before Switch"
msgstr "Situação do pagamento antes da troca"

#. module: account_accountant
#: model:ir.model,name:account_accountant.model_account_payment
#: model_terms:ir.ui.view,arch_db:account_accountant.view_bank_statement_line_search_bank_rec_widget
msgid "Payments"
msgstr "Pagamentos"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.account_journal_dashboard_kanban_view
msgid "Payments Matching"
msgstr "Pagamentos correspondentes"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_change_lock_date
msgid "Pick a date to lock"
msgstr "Escolha uma data para travar"

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/models/account_move.py:0
#, python-format
msgid "Please set the deferred accounts in the accounting settings."
msgstr "Defina as contas diferidas nas configurações financeiras."

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/models/account_move.py:0
#, python-format
msgid "Please set the deferred journal in the accounting settings."
msgstr "Defina o diário diferido nas configurações financeiras."

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.res_config_settings_view_form
msgid "Post Deferred entries in:"
msgstr "Fazer lançamentos diferidos em:"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_res_company__predict_bill_product
#: model:ir.model.fields,field_description:account_accountant.field_res_config_settings__predict_bill_product
msgid "Predict Bill Product"
msgstr "Predizer produto da fatura de fornecedor"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.res_config_settings_view_form
msgid "Predict vendor bill product"
msgstr "Predizer produto da fatura de fornecedor"

#. module: account_accountant
#: model:ir.model,name:account_accountant.model_account_reconcile_model
msgid ""
"Preset to create journal entries during a invoices and payments matching"
msgstr ""
"Configuração para criar entradas do diário quando ocorre uma correspondência"
" entre faturas e pagamentos"

#. module: account_accountant
#: model:ir.model.fields,help:account_accountant.field_account_change_lock_date__fiscalyear_lock_date
msgid ""
"Prevents Journal Entry creation or modification up to the defined date "
"inclusive for all users. As a closed period, all accounting operations are "
"prohibited."
msgstr ""
"Impede a criação ou modificação de lançamentos de diário manuais até a data "
"definida, para todos os usuários. Como um período fechado, todas as "
"operações financeiras são proibidas."

#. module: account_accountant
#: model:ir.model.fields,help:account_accountant.field_account_change_lock_date__period_lock_date
msgid ""
"Prevents Journal entries creation up to the defined date inclusive. Except "
"for Accountant users."
msgstr ""
"Impede a criação de lançamentos de diário manuais até a data definida. "
"Exceto para usuários Contadores."

#. module: account_accountant
#: model:ir.model.fields,help:account_accountant.field_account_change_lock_date__tax_lock_date
msgid ""
"Prevents Tax Returns modification up to the defined date inclusive (Journal "
"Entries involving taxes). The Tax Return Lock Date is automatically set when"
" the corresponding Journal Entry is posted."
msgstr ""
"Impede a modificação de declarações de imposto até a data definida "
"(lançamentos de diário manuais que envolvem impostos). A data de bloqueio da"
" declaração de imposto é definida automaticamente quando o lançamento de "
"diário manual correspondente é feito."

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_bank_statement_form_with_buttons
msgid "Print"
msgstr "Imprimir"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_move_line_reconcile_search
msgid "Receivable"
msgstr "Contas a receber"

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/bank_rec_form.xml:0
#, python-format
msgid "Receivable:"
msgstr "Recebível:"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_auto_reconcile_wizard__search_mode
#: model:ir.ui.menu,name:account_accountant.menu_account_reconcile
#: model_terms:ir.ui.view,arch_db:account_accountant.account_journal_dashboard_kanban_view
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_reconcile_wizard
#: model_terms:ir.ui.view,arch_db:account_accountant.view_move_line_reconcile_tree
#: model_terms:ir.ui.view,arch_db:account_accountant.view_move_line_tree
msgid "Reconcile"
msgstr "Reconciliar"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_reconcile_wizard
msgid "Reconcile & open"
msgstr "Reconciliar e abrir"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_reconcile_wizard__reco_account_id
msgid "Reconcile Account"
msgstr "Conta de reconciliação"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__reconcile_model_id
msgid "Reconcile Model"
msgstr "Modelo de reconciliação"

#. module: account_accountant
#: model:ir.model.fields.selection,name:account_accountant.selection__bank_rec_widget__state__reconciled
msgid "Reconciled"
msgstr "Reconciliado"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_reconcile_wizard__reco_model_id
msgid "Reconciliation model"
msgstr "Modelo de reconciliação"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.res_config_settings_view_form
msgid "Record cost of goods sold in your journal entries"
msgstr "Registre o custo de bens vendidos em seus lançamentos de diário"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_move_form_inherit
msgid "Related Purchase(s)"
msgstr "Compra(s) relacionada(s)"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_move_form_inherit
msgid "Related Sale(s)"
msgstr "Venda(s) relacionada(s)"

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/bank_rec_form.xml:0
#, python-format
msgid "Reset"
msgstr "Redefinir"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_move_line_reconcile_tree
msgid "Residual"
msgstr "Residual"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_move_line_reconcile_tree
msgid "Residual in Currency"
msgstr "Residual na moeda"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget__return_todo_command
msgid "Return Todo Command"
msgstr "Retornar comando todo"

#. module: account_accountant
#: model:ir.model,name:account_accountant.model_account_reconcile_model_line
msgid "Rules for the reconciliation model"
msgstr "Regras para o modelo de reconciliação"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_change_lock_date
#: model_terms:ir.ui.view,arch_db:account_accountant.view_bank_statement_form_with_buttons
msgid "Save"
msgstr "Salvar"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_bank_statement_line_form_bank_rec_widget
msgid "Save & Close"
msgstr "Salvar e fechar"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_bank_statement_line_form_bank_rec_widget
msgid "Save & New"
msgstr "Salvar e criar novo"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_move_line_reconcile_search
msgid "Search Journal Items to Reconcile"
msgstr "Buscar itens de diário a reconciliar"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget__selected_aml_ids
msgid "Selected Aml"
msgstr "AML selecionado"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget__selected_reco_model_id
msgid "Selected Reco Model"
msgstr "Modelo de reconciliação selecionado"

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/bank_rec_form.xml:0
#, python-format
msgid "Set as Checked"
msgstr "Definir como verificado"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_reconcile_wizard__single_currency_mode
msgid "Single Currency Mode"
msgstr "Modo de moeda única"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__source_aml_id
msgid "Source Aml"
msgstr "AML de origem"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__source_aml_move_id
msgid "Source Aml Move"
msgstr "Movimentação AML de origem"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__source_aml_move_name
msgid "Source Aml Move Name"
msgstr "Nome da movimentação AML de origem"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__source_amount_currency
msgid "Source Amount Currency"
msgstr "Moeda do valor de origem"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__source_balance
msgid "Source Balance"
msgstr "Saldo de origem"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__source_credit
msgid "Source Credit"
msgstr "Crédito de origem"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__source_debit
msgid "Source Debit"
msgstr "Débito de origem"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget__st_line_id
msgid "St Line"
msgstr "Linha do extrato"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget__st_line_transaction_details
msgid "St Line Transaction Details"
msgstr "Detalhes da transação da linha do extrato"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_fiscal_year__date_from
#: model:ir.model.fields,field_description:account_accountant.field_account_move_line__deferred_start_date
msgid "Start Date"
msgstr "Data de início"

#. module: account_accountant
#: model:ir.model.fields,help:account_accountant.field_account_fiscal_year__date_from
msgid "Start Date, included in the fiscal year."
msgstr "Data de início, incluída no ano fiscal."

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget__state
msgid "State"
msgstr "Situação"

#. module: account_accountant
#: model:ir.actions.server,name:account_accountant.action_bank_statement_attachment
#: model_terms:ir.ui.view,arch_db:account_accountant.view_bank_statement_line_kanban_bank_rec_widget
#: model_terms:ir.ui.view,arch_db:account_accountant.view_bank_statement_line_search_bank_rec_widget
msgid "Statement"
msgstr "Extrato"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_bank_statement_line_search_bank_rec_widget
msgid "Statement Line"
msgstr "Linha do extrato"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__suggestion_amount_currency
msgid "Suggestion Amount Currency"
msgstr "Moeda do valor de sugestão"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__suggestion_balance
msgid "Suggestion Balance"
msgstr "Saldo sugerido"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__suggestion_html
msgid "Suggestion Html"
msgstr "HTML da sugestão"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_move_line_list_bank_rec_widget
msgid "Suggestions"
msgstr "Sugestões"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_reconcile_wizard__tax_id
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__tax_ids
msgid "Tax"
msgstr "Imposto"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__tax_base_amount_currency
msgid "Tax Base Amount Currency"
msgstr "Moeda do valor de base de tributação"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_move_line_reconcile_tree
msgid "Tax Grids"
msgstr "Grades de impostos"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_res_config_settings__tax_lock_date
msgid "Tax Lock Date"
msgstr "Período de bloqueio fiscal"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__tax_repartition_line_id
msgid "Tax Repartition Line"
msgstr "Linha de repartição de imposto"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_change_lock_date__tax_lock_date
msgid "Tax Return Lock Date"
msgstr "Data de travamento para declaração de imposto"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__tax_tag_ids
msgid "Tax Tag"
msgstr "Marcador de imposto"

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/bank_rec_form.xml:0
#: code:addons/account_accountant/static/src/components/bank_reconciliation/kanban.js:0
#, python-format
msgid "Taxes"
msgstr "Impostos"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget__st_line_narration
msgid "Terms and Conditions"
msgstr "Termos e Condições"

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/rainbowman_content.xml:0
#, python-format
msgid "That's on average"
msgstr "Está na média"

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/wizard/account_reconcile_wizard.py:0
#, python-format
msgid ""
"The date you set violates the lock date of one of your entry. It will be "
"overriden by the following date : %(replacement_date)s"
msgstr ""
"A data que você definiu viola a data de travamento de um dos seus "
"lançamentos. Ela será substituída pela seguinte data: %(replacement_date)s"

#. module: account_accountant
#: model:ir.model.fields,help:account_accountant.field_account_bank_statement_line__deferred_move_ids
#: model:ir.model.fields,help:account_accountant.field_account_move__deferred_move_ids
#: model:ir.model.fields,help:account_accountant.field_account_payment__deferred_move_ids
msgid "The deferred entries created by this invoice"
msgstr "Os lançamentos diferidos criados por esta fatura"

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/models/account_fiscal_year.py:0
#, python-format
msgid "The ending date must not be prior to the starting date."
msgstr "A data final não pode ser anterior à data de início."

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/models/bank_rec_widget_line.py:0
#, python-format
msgid ""
"The invoice %(display_name_html)s with an open amount of %(open_amount)s "
"will be entirely paid by the transaction."
msgstr ""
"A fatura %(display_name_html)s com um valor em aberto de %(open_amount)s "
"será paga totalmente pela transação."

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/models/bank_rec_widget_line.py:0
#, python-format
msgid ""
"The invoice %(display_name_html)s with an open amount of %(open_amount)s "
"will be reduced by %(amount)s."
msgstr ""
"A fatura %(display_name_html)s com um valor em aberto de %(open_amount)s "
"será reduzida em %(amount)s."

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.res_config_settings_view_form
msgid ""
"The invoices up to this date will not be taken into account as accounting "
"entries"
msgstr ""
"As faturas até esta data não serão levadas em conta como lançamentos "
"financeiros"

#. module: account_accountant
#: model:ir.model.fields,help:account_accountant.field_account_bank_statement_line__deferred_original_move_ids
#: model:ir.model.fields,help:account_accountant.field_account_move__deferred_original_move_ids
#: model:ir.model.fields,help:account_accountant.field_account_payment__deferred_original_move_ids
msgid "The original invoices that created the deferred entries"
msgstr "As faturas originais que criaram os lançamentos diferidos"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.res_config_settings_view_form
msgid ""
"The system will try to predict the product on vendor bill lines based on the"
" label of the line"
msgstr ""
"O sistema tentará prever o produto nas linhas de fatura de fornecedor com "
"base na etiqueta da linha"

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/models/account_bank_statement.py:0
#, python-format
msgid ""
"This bank transaction has been automatically validated using the "
"reconciliation model '%s'."
msgstr ""
"Essa transação bancária foi validada automaticamente usando o modelo de "
"reconciliação '%s'."

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/wizard/account_reconcile_wizard.py:0
#, python-format
msgid "This can only be used on journal items"
msgstr "Isto só pode ser usado em itens de diário"

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/models/account_reconcile_model_line.py:0
#, python-format
msgid ""
"This reconciliation model can't be used in the manual reconciliation widget "
"because its configuration is not adapted"
msgstr ""
"Esse modelo de reconciliação não pode ser usado no widget de reconciliação "
"manual porque sua configuração não está adaptada"

#. module: account_accountant
#: model:digest.tip,name:account_accountant.digest_tip_account_accountant_0
msgid "Tip: Bulk update journal items"
msgstr "Dica: atualize itens de diário em massa"

#. module: account_accountant
#: model:digest.tip,name:account_accountant.digest_tip_account_accountant_1
msgid "Tip: Find an Accountant or register your Accounting Firm"
msgstr ""
"Dica: encontre um contador ou registre seu escritório de contabilidade"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_auto_reconcile_wizard__to_date
msgid "To"
msgstr "Para"

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/bank_rec_form.xml:0
#: code:addons/account_accountant/static/src/components/bank_reconciliation/bank_rec_form.xml:0
#: model:ir.model.fields,field_description:account_accountant.field_account_reconcile_wizard__to_check
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget__st_line_to_check
#: model_terms:ir.ui.view,arch_db:account_accountant.view_bank_statement_line_search_bank_rec_widget
#, python-format
msgid "To Check"
msgstr "A verificar"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_bank_statement_line_kanban_bank_rec_widget
msgid "To check"
msgstr "A verificar"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget__todo_command
msgid "Todo Command"
msgstr "Comando todo"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_move_line_reconcile_tree
msgid "Total Balance"
msgstr "Saldo total"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_move_line_reconcile_tree
msgid "Total Credit"
msgstr "Crédito total"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_move_line_reconcile_tree
msgid "Total Debit"
msgstr "Débito total"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_move_line_reconcile_tree
msgid "Total Residual"
msgstr "Total residual"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_move_line_reconcile_tree
msgid "Total Residual in Currency"
msgstr "Total residual na moeda"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_bank_statement_line_search_bank_rec_widget
msgid "Transaction"
msgstr "Transação"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget__transaction_currency_id
msgid "Transaction Currency"
msgstr "Moeda da transação"

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/bank_rec_form.xml:0
#: code:addons/account_accountant/static/src/components/bank_reconciliation/bank_rec_form.xml:0
#, python-format
msgid "Transaction Details"
msgstr "Detalhes da transação"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.account_journal_dashboard_kanban_view
msgid "Transactions"
msgstr "Transações"

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/wizard/account_reconcile_wizard.py:0
#, python-format
msgid "Transfer from %s"
msgstr "Transferir de %s"

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/wizard/account_reconcile_wizard.py:0
#, python-format
msgid "Transfer to %s"
msgstr "Transferir para %s"

#. module: account_accountant
#: model:ir.actions.server,name:account_accountant.auto_reconcile_bank_statement_line_ir_actions_server
msgid "Try to reconcile automatically your statement lines"
msgstr "Tente reconciliar automaticamente suas linhas de extrato"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_move_line_reconcile_search
msgid "Unreconciled"
msgstr "Não reconciliado"

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/models/res_company.py:0
#, python-format
msgid "Unreconciled statements lines"
msgstr "Linhas não reconciliadas de extratos"

#. module: account_accountant
#: model:ir.model.fields.selection,name:account_accountant.selection__bank_rec_widget__state__valid
msgid "Valid"
msgstr "Válido"

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/bank_rec_form.xml:0
#: code:addons/account_accountant/static/src/components/bank_reconciliation/bank_rec_form.xml:0
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_reconcile_model_widget_wizard
#, python-format
msgid "Validate"
msgstr "Validar"

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/list_view_switcher.js:0
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_move_line_list_bank_rec_widget
#, python-format
msgid "View"
msgstr "Visualização"

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/bank_rec_form.xml:0
#, python-format
msgid "View models"
msgstr "Ver modelos"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__wizard_id
msgid "Wizard"
msgstr "Assistente"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget__company_currency_id
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__company_currency_id
msgid "Wizard Company Currency"
msgstr "Assistente de moeda da empresa"

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/wizard/account_reconcile_wizard.py:0
#, python-format
msgid "Write-Off"
msgstr "Baixa"

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/wizard/account_reconcile_wizard.py:0
#, python-format
msgid "Write-Off Entry"
msgstr "Lançamento de baixa"

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/models/account_fiscal_year.py:0
#, python-format
msgid ""
"You can not have an overlap between two fiscal years, please correct the "
"start and/or end dates of your fiscal years."
msgstr ""
"Você não pode ter uma sobreposição entre dois anos fiscais, por favor "
"corrija as datas de início e/ou término de seus anos fiscais."

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/wizard/account_reconcile_wizard.py:0
#, python-format
msgid "You can only reconcile entries with up to two different accounts: %s"
msgstr ""
"Só é possível reconciliar lançamentos com até duas contas diferentes: %s"

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/models/account_move.py:0
#, python-format
msgid ""
"You cannot change the account for a deferred line in %(move_name)s if it has"
" already been deferred."
msgstr ""
"Você não pode alterar a conta de uma linha diferida em %(move_name)s se ela "
"já foi deferida."

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/models/account_move.py:0
#, python-format
msgid "You cannot create a deferred entry with a start date but no end date."
msgstr ""
"Não é possível criar um lançamento diferido com uma data de início, mas sem "
"data final."

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/models/account_move.py:0
#, python-format
msgid ""
"You cannot create a deferred entry with a start date later than the end "
"date."
msgstr ""
"Não é possível criar um lançamento diferido com uma data de início posterior"
" à data final."

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/models/account_move.py:0
#, python-format
msgid ""
"You cannot generate deferred entries for a miscellaneous journal entry."
msgstr ""
"Não é possível gerar lançamentos diferidos para um lançamento diverso manual"
" de diário."

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/models/account_move.py:0
#, python-format
msgid ""
"You cannot reset to draft an invoice that is grouped in deferral entry. You "
"can create a credit note instead."
msgstr ""
"Não é possível redefinir como provisória uma fatura agrupada em lançamento "
"de diferimento. Em vez disso, você pode criar uma nota de crédito."

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/wizard/account_change_lock_date.py:0
#, python-format
msgid "You cannot set a lock date in the future."
msgstr "Não é possível definir um período de bloqueio no futuro."

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/models/bank_rec_widget_line.py:0
#, python-format
msgid ""
"You might want to %(btn_start)sfully reconcile%(btn_end)s the document."
msgstr "Sugerimos %(btn_start)sreconciliar totalmente%(btn_end)s o documento."

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/models/bank_rec_widget_line.py:0
#, python-format
msgid ""
"You might want to make a %(btn_start)spartial reconciliation%(btn_end)s "
"instead."
msgstr ""
"Sugerimos fazer uma %(btn_start)sreconciliação parcial%(btn_end)s ao invés "
"disso."

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/models/bank_rec_widget_line.py:0
#, python-format
msgid "You might want to record a %(btn_start)spartial payment%(btn_end)s."
msgstr "Sugerimos registrar um %(btn_start)spagamento parcial%(btn_end)s."

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/models/bank_rec_widget_line.py:0
#, python-format
msgid ""
"You might want to set the invoice as %(btn_start)sfully paid%(btn_end)s."
msgstr ""
"Sugerimos definir a fatura como %(btn_start)stotalmente paga%(btn_end)s."

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/rainbowman_content.xml:0
#, python-format
msgid "You reconciled"
msgstr "Você reconciliou"

#. module: account_accountant
#: model:ir.model.fields.selection,name:account_accountant.selection__bank_rec_widget_line__flag__aml
msgid "aml"
msgstr "aml"

#. module: account_accountant
#: model:ir.model.fields.selection,name:account_accountant.selection__bank_rec_widget_line__flag__auto_balance
msgid "auto_balance"
msgstr "auto_balance"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_reconcile_model_widget_wizard
msgid "e.g. Bank Fees"
msgstr "por exemplo, taxas bancárias"

#. module: account_accountant
#: model:ir.model.fields.selection,name:account_accountant.selection__bank_rec_widget_line__flag__early_payment
msgid "early_payment"
msgstr "early_payment"

#. module: account_accountant
#: model:ir.model.fields.selection,name:account_accountant.selection__bank_rec_widget_line__flag__exchange_diff
msgid "exchange_diff"
msgstr "exchange_diff"

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/bank_rec_form.xml:0
#: code:addons/account_accountant/static/src/components/bank_reconciliation/bank_rec_form.xml:0
#, python-format
msgid "in"
msgstr "em"

#. module: account_accountant
#: model:ir.model.fields.selection,name:account_accountant.selection__bank_rec_widget_line__flag__liquidity
msgid "liquidity"
msgstr "liquidez"

#. module: account_accountant
#: model:ir.model.fields.selection,name:account_accountant.selection__bank_rec_widget_line__flag__manual
msgid "manual"
msgstr "manual"

#. module: account_accountant
#: model:ir.model.fields.selection,name:account_accountant.selection__bank_rec_widget_line__flag__new_aml
msgid "new_aml"
msgstr "new_aml"

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/rainbowman_content.xml:0
#, python-format
msgid "seconds per transaction."
msgstr "segundos por transação."

#. module: account_accountant
#: model:ir.model.fields.selection,name:account_accountant.selection__bank_rec_widget_line__flag__tax_line
msgid "tax_line"
msgstr "tax_line"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.account_journal_dashboard_kanban_view
msgid "to check"
msgstr "a verificar"

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/rainbowman_content.xml:0
#, python-format
msgid "transaction in"
msgstr "transação em"

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/rainbowman_content.xml:0
#, python-format
msgid "transactions in"
msgstr "transações em"
