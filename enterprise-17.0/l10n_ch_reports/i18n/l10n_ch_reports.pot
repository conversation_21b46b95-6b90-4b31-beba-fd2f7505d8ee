# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* l10n_ch_reports
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-11-09 08:20+0000\n"
"PO-Revision-Date: 2022-11-09 08:20+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: l10n_ch_reports
#: model:account.report.line,name:l10n_ch_reports.account_financial_report_line_ch_1
msgid "ASSETS"
msgstr ""

#. module: l10n_ch_reports
#: model:account.report.line,name:l10n_ch_reports.account_financial_report_line_ch_200
msgid "Accounts payable from goods and services"
msgstr ""

#. module: l10n_ch_reports
#: model:account.report.line,name:l10n_ch_reports.account_financial_report_line_ch_110
msgid "Accounts receivable from goods and services"
msgstr ""

#. module: l10n_ch_reports
#: model:account.report.line,name:l10n_ch_reports.account_financial_report_line_ch_130
msgid "Accrued revenue and deferred expense"
msgstr ""

#. module: l10n_ch_reports
#: model:account.report.expression,report_line_name:l10n_ch_reports.account_financial_report_line_ch_R_balance
#: model:account.report.line,name:l10n_ch_reports.account_financial_report_line_ch_R
msgid "Annual (net) profit or loss"
msgstr ""

#. module: l10n_ch_reports
#: model:account.report.expression,report_line_name:l10n_ch_reports.account_financial_report_line_ch_rai_balance
#: model:account.report.line,name:l10n_ch_reports.account_financial_report_line_ch_rai
msgid "Annual (net) profit or loss before taxes"
msgstr ""

#. module: l10n_ch_reports
#: model:account.report.expression,report_line_name:l10n_ch_reports.account_financial_report_line_ch_299_balance
#: model:account.report.line,name:l10n_ch_reports.account_financial_report_line_ch_299
msgid "Annual profit or annual loss"
msgstr ""

#. module: l10n_ch_reports
#: model:account.report.column,name:l10n_ch_reports.account_financial_report_l10n_ch_bilan_column
#: model:account.report.column,name:l10n_ch_reports.account_financial_report_l10n_ch_cdr_column
msgid "Balance"
msgstr ""

#. module: l10n_ch_reports
#: model:account.report,name:l10n_ch_reports.account_financial_report_l10n_ch_bilan
msgid "Balance Sheet"
msgstr ""

#. module: l10n_ch_reports
#: model:account.report.line,name:l10n_ch_reports.account_financial_report_line_ch_14
msgid "CAPITAL ASSETS"
msgstr ""

#. module: l10n_ch_reports
#: model:account.report.line,name:l10n_ch_reports.account_financial_report_line_ch_100
msgid "Cash"
msgstr ""

#. module: l10n_ch_reports
#: model:account.report.expression,report_line_name:l10n_ch_reports.account_financial_report_line_ch_39_balance
#: model:account.report.line,name:l10n_ch_reports.account_financial_report_line_ch_39
msgid ""
"Changes in inventories of unfinished and finished products and in non-"
"invoiced services"
msgstr ""

#. module: l10n_ch_reports
#: model:account.report.line,name:l10n_ch_reports.account_financial_report_line_ch_10
msgid "Current Assets"
msgstr ""

#. module: l10n_ch_reports
#: model:account.report.line,name:l10n_ch_reports.account_financial_report_line_ch_20
msgid "Current Liabilities"
msgstr ""

#. module: l10n_ch_reports
#: model:account.report.line,name:l10n_ch_reports.account_financial_report_line_ch_210
msgid "Current interest-bearing liabilities"
msgstr ""

#. module: l10n_ch_reports
#: model:account.report.line,name:l10n_ch_reports.account_financial_report_line_ch_230
msgid "Deferred revenue and accrued expense"
msgstr ""

#. module: l10n_ch_reports
#: model:account.report.expression,report_line_name:l10n_ch_reports.account_financial_report_line_ch_68_balance
#: model:account.report.line,name:l10n_ch_reports.account_financial_report_line_ch_68
msgid "Depreciation and valuation adjustments on capital asset items"
msgstr ""

#. module: l10n_ch_reports
#: model:account.report.expression,report_line_name:l10n_ch_reports.account_financial_report_line_ch_89_balance
#: model:account.report.line,name:l10n_ch_reports.account_financial_report_line_ch_89
msgid "Direct Taxes"
msgstr ""

#. module: l10n_ch_reports
#: model:account.report.expression,report_line_name:l10n_ch_reports.account_financial_report_line_ch_ebit_balance
#: model:account.report.line,name:l10n_ch_reports.account_financial_report_line_ch_ebit
msgid "Earnings before interest and taxes (EBIT)"
msgstr ""

#. module: l10n_ch_reports
#: model:account.report.expression,report_line_name:l10n_ch_reports.account_financial_report_line_ch_ebitda_balance
#: model:account.report.line,name:l10n_ch_reports.account_financial_report_line_ch_ebitda
msgid ""
"Earnings before interest, tax and depreciation and amortisation (EBITDA)"
msgstr ""

#. module: l10n_ch_reports
#: model:account.report.expression,report_line_name:l10n_ch_reports.account_financial_report_line_ch_ebt_balance
#: model:account.report.line,name:l10n_ch_reports.account_financial_report_line_ch_ebt
msgid "Earnings before taxes (EBT)"
msgstr ""

#. module: l10n_ch_reports
#: model:account.report.line,name:l10n_ch_reports.account_financial_report_line_ch_28
msgid "Equity"
msgstr ""

#. module: l10n_ch_reports
#: model:account.report.expression,report_line_name:l10n_ch_reports.account_financial_report_line_ch_4_balance
#: model:account.report.line,name:l10n_ch_reports.account_financial_report_line_ch_4
msgid "Expenses for materials, goods and services"
msgstr ""

#. module: l10n_ch_reports
#: model:account.report.expression,report_line_name:l10n_ch_reports.account_financial_report_line_ch_85_balance
#: model:account.report.line,name:l10n_ch_reports.account_financial_report_line_ch_85
msgid "Extraordinary, non-recurring or prior-period result"
msgstr ""

#. module: l10n_ch_reports
#: model:account.report.expression,report_line_name:l10n_ch_reports.account_financial_report_line_ch_69_balance
#: model:account.report.line,name:l10n_ch_reports.account_financial_report_line_ch_69
msgid "Financial Result"
msgstr ""

#. module: l10n_ch_reports
#: model:account.report.line,name:l10n_ch_reports.account_financial_report_line_ch_140
msgid "Financial assets"
msgstr ""

#. module: l10n_ch_reports
#: model:account.report.line,name:l10n_ch_reports.account_financial_report_line_ch_160
msgid "Immovable tangible assets"
msgstr ""

#. module: l10n_ch_reports
#: model:account.report.line,name:l10n_ch_reports.account_financial_report_line_ch_170
msgid "Intangible assets"
msgstr ""

#. module: l10n_ch_reports
#: model:account.report.line,name:l10n_ch_reports.account_financial_report_line_ch_120
msgid "Inventories"
msgstr ""

#. module: l10n_ch_reports
#: model:account.report.line,name:l10n_ch_reports.account_financial_report_line_ch_2
msgid "LIABILITIES AND EQUITY"
msgstr ""

#. module: l10n_ch_reports
#: model:account.report.line,name:l10n_ch_reports.account_financial_report_line_ch_290
msgid "Legal capital reserves"
msgstr ""

#. module: l10n_ch_reports
#: model:account.report.expression,report_line_name:l10n_ch_reports.account_financial_report_line_ch_290_a_balance
#: model:account.report.line,name:l10n_ch_reports.account_financial_report_line_ch_290_a
msgid "Legal reserves"
msgstr ""

#. module: l10n_ch_reports
#: model:account.report.line,name:l10n_ch_reports.account_financial_report_line_ch_24
msgid "Long-term Liabilities"
msgstr ""

#. module: l10n_ch_reports
#: model:account.report.line,name:l10n_ch_reports.account_financial_report_line_ch_240
msgid "Long-term interest-bearing liabilities"
msgstr ""

#. module: l10n_ch_reports
#: model:account.report.line,name:l10n_ch_reports.account_financial_report_line_ch_150
msgid "Movable tangible assets"
msgstr ""

#. module: l10n_ch_reports
#: model:account.report.expression,report_line_name:l10n_ch_reports.account_financial_report_line_ch_30_balance
#: model:account.report.line,name:l10n_ch_reports.account_financial_report_line_ch_30
msgid "Net sales from goods and services"
msgstr ""

#. module: l10n_ch_reports
#: model:account.report.expression,report_line_name:l10n_ch_reports.account_financial_report_line_ch_80_balance
#: model:account.report.line,name:l10n_ch_reports.account_financial_report_line_ch_80
msgid "Non-operational result"
msgstr ""

#. module: l10n_ch_reports
#: model:account.report.line,name:l10n_ch_reports.account_financial_report_line_ch_180
msgid "Non-paid-in share, corporate or foundation capital"
msgstr ""

#. module: l10n_ch_reports
#: model:account.report.expression,report_line_name:l10n_ch_reports.account_financial_report_line_ch_3_balance
#: model:account.report.line,name:l10n_ch_reports.account_financial_report_line_ch_3
msgid "Operating revenue from goods and services"
msgstr ""

#. module: l10n_ch_reports
#: model:account.report.line,name:l10n_ch_reports.account_financial_report_line_ch_220
msgid "Other current liabilities"
msgstr ""

#. module: l10n_ch_reports
#: model:account.report.line,name:l10n_ch_reports.account_financial_report_line_ch_250
msgid "Other long-term liabilities"
msgstr ""

#. module: l10n_ch_reports
#: model:account.report.expression,report_line_name:l10n_ch_reports.account_financial_report_line_ch_60_balance
#: model:account.report.line,name:l10n_ch_reports.account_financial_report_line_ch_60
msgid "Other operating expenses (without depreciation and financial result)"
msgstr ""

#. module: l10n_ch_reports
#: model:account.report.line,name:l10n_ch_reports.account_financial_report_line_ch_114
msgid "Other short-term receivables"
msgstr ""

#. module: l10n_ch_reports
#: model:account.report.line,name:l10n_ch_reports.account_financial_report_line_ch_148
msgid "Participations"
msgstr ""

#. module: l10n_ch_reports
#: model:account.report.expression,report_line_name:l10n_ch_reports.account_financial_report_line_ch_290_b_balance
#: model:account.report.expression,report_line_name:l10n_ch_reports.account_financial_report_line_ch_290_b_domain
#: model:account.report.line,name:l10n_ch_reports.account_financial_report_line_ch_290_b
msgid "Previous years' unallocated profit or loss"
msgstr ""

#. module: l10n_ch_reports
#: model:account.report,name:l10n_ch_reports.account_financial_report_l10n_ch_cdr
#: model:ir.actions.client,name:l10n_ch_reports.account_financial_report_l10n_ch_pl_action
msgid "Profit And Loss"
msgstr ""

#. module: l10n_ch_reports
#: model:account.report.expression,report_line_name:l10n_ch_reports.account_financial_report_line_ch_7_balance
#: model:account.report.line,name:l10n_ch_reports.account_financial_report_line_ch_7
msgid "Profit and loss from non-core business"
msgstr ""

#. module: l10n_ch_reports
#: model:account.report.line,name:l10n_ch_reports.account_financial_report_line_ch_260
msgid "Provisions"
msgstr ""

#. module: l10n_ch_reports
#: model:account.report.line,name:l10n_ch_reports.account_financial_report_line_ch_280
msgid "Share, corporate or foundation capital"
msgstr ""

#. module: l10n_ch_reports
#: model:account.report.line,name:l10n_ch_reports.account_financial_report_line_ch_106
msgid "Short-term assets with stock exchange price"
msgstr ""

#. module: l10n_ch_reports
#: model:account.report.expression,report_line_name:l10n_ch_reports.account_financial_report_line_ch_5_balance
#: model:account.report.line,name:l10n_ch_reports.account_financial_report_line_ch_5
msgid "Staff cost"
msgstr ""
