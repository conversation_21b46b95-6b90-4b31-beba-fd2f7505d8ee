# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* web_map
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-01-05 13:47+0000\n"
"PO-Revision-Date: 2023-10-26 23:10+0000\n"
"Language-Team: Armenian (https://app.transifex.com/odoo/teams/41243/hy/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: hy\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: web_map
#. odoo-javascript
#: code:addons/web_map/static/src/map_view/map_renderer.xml:0
#: code:addons/web_map/static/src/map_view/map_renderer.xml:0
#, python-format
msgid ""
".st0{opacity:0.3;enable-background:new;}\n"
"                .st1{fill:currentColor;stroke:#1A1919;stroke-width:3;stroke-miterlimit:10;}"
msgstr ""

#. module: web_map
#: model_terms:ir.ui.view,arch_db:web_map.res_config_settings_view_form
msgid ""
"<i class=\"oi oi-arrow-right\"/>\n"
"                                Sign up to MapBox to get a free token"
msgstr ""

#. module: web_map
#: model:ir.model,name:web_map.model_ir_actions_act_window_view
msgid "Action Window View"
msgstr ""

#. module: web_map
#. odoo-javascript
#: code:addons/web_map/static/src/map_view/map_renderer.js:0
#: code:addons/web_map/static/src/map_view/map_renderer.js:0
#, python-format
msgid "Address"
msgstr ""

#. module: web_map
#: model:ir.model,name:web_map.model_base
msgid "Base"
msgstr ""

#. module: web_map
#. odoo-javascript
#: code:addons/web_map/static/src/map_view/map_renderer.xml:0
#: code:addons/web_map/static/src/map_view/map_renderer.xml:0
#, python-format
msgid "Close"
msgstr ""

#. module: web_map
#: model:ir.model,name:web_map.model_res_config_settings
msgid "Config Settings"
msgstr ""

#. module: web_map
#: model:ir.model,name:web_map.model_res_partner
msgid "Contact"
msgstr ""

#. module: web_map
#: model:ir.model.fields,field_description:web_map.field_res_partner__contact_address_complete
#: model:ir.model.fields,field_description:web_map.field_res_users__contact_address_complete
msgid "Contact Address Complete"
msgstr ""

#. module: web_map
#: model:ir.model,name:web_map.model_ir_http
msgid "HTTP Routing"
msgstr ""

#. module: web_map
#. odoo-javascript
#: code:addons/web_map/static/src/map_view/map_view.js:0
#, python-format
msgid "Items"
msgstr ""

#. module: web_map
#. odoo-javascript
#: code:addons/web_map/static/src/map_view/map_renderer.xml:0
#, python-format
msgid "Locating new addresses..."
msgstr ""

#. module: web_map
#. odoo-javascript
#: code:addons/web_map/static/src/map_view/map_view.js:0
#: model:ir.model.fields.selection,name:web_map.selection__ir_actions_act_window_view__view_mode__map
#: model:ir.model.fields.selection,name:web_map.selection__ir_ui_view__type__map
#, python-format
msgid "Map"
msgstr ""

#. module: web_map
#: model_terms:ir.ui.view,arch_db:web_map.res_config_settings_view_form
msgid "Map Routes"
msgstr ""

#. module: web_map
#. odoo-javascript
#: code:addons/web_map/static/src/map_view/map_model.js:0
#, python-format
msgid "MapBox servers unreachable"
msgstr ""

#. module: web_map
#. odoo-javascript
#: code:addons/web_map/static/src/map_view/map_renderer.js:0
#, python-format
msgid "Name"
msgstr ""

#. module: web_map
#. odoo-javascript
#: code:addons/web_map/static/src/map_view/map_renderer.xml:0
#, python-format
msgid "Navigate to"
msgstr ""

#. module: web_map
#: model:ir.model.fields,help:web_map.field_res_config_settings__map_box_token
msgid "Necessary for some functionalities in the map view"
msgstr ""

#. module: web_map
#. odoo-javascript
#: code:addons/web_map/static/src/map_view/map_model.js:0
#, python-format
msgid "No"
msgstr ""

#. module: web_map
#. odoo-javascript
#: code:addons/web_map/static/src/map_view/map_model.js:0
#, python-format
msgid "None"
msgstr ""

#. module: web_map
#. odoo-javascript
#: code:addons/web_map/static/src/map_view/map_renderer.xml:0
#, python-format
msgid "Open"
msgstr ""

#. module: web_map
#. odoo-javascript
#: code:addons/web_map/static/src/map_view/map_model.js:0
#, python-format
msgid "OpenStreetMap's request limit exceeded, try again later."
msgstr ""

#. module: web_map
#: model_terms:ir.ui.view,arch_db:web_map.res_config_settings_view_form
msgid "Set a MapBox account to activate routes and style"
msgstr ""

#. module: web_map
#. odoo-javascript
#: code:addons/web_map/static/src/map_view/map_renderer.xml:0
#, python-format
msgid "Set up token"
msgstr ""

#. module: web_map
#. odoo-javascript
#: code:addons/web_map/static/src/map_view/map_model.js:0
#, python-format
msgid "Some routing points are too far apart"
msgstr ""

#. module: web_map
#. odoo-python
#: code:addons/web_map/models/res_config_settings.py:0
#, python-format
msgid "The MapBox server is unreachable"
msgstr ""

#. module: web_map
#. odoo-python
#: code:addons/web_map/models/res_config_settings.py:0
#, python-format
msgid "The token input is not valid"
msgstr ""

#. module: web_map
#. odoo-javascript
#: code:addons/web_map/static/src/map_view/map_model.js:0
#: code:addons/web_map/static/src/map_view/map_model.js:0
#: code:addons/web_map/static/src/map_view/map_model.js:0
#, python-format
msgid ""
"The view has switched to another provider but functionalities will be "
"limited"
msgstr ""

#. module: web_map
#. odoo-python
#: code:addons/web_map/models/res_config_settings.py:0
#, python-format
msgid "This referer is not authorized"
msgstr ""

#. module: web_map
#. odoo-javascript
#: code:addons/web_map/static/src/map_view/map_renderer.xml:0
#, python-format
msgid ""
"To get routing on your map, you first need to set up your MapBox token. It's"
" free."
msgstr ""

#. module: web_map
#: model_terms:ir.ui.view,arch_db:web_map.res_config_settings_view_form
msgid "Token"
msgstr ""

#. module: web_map
#: model:ir.model.fields,field_description:web_map.field_res_config_settings__map_box_token
msgid "Token Map Box"
msgstr ""

#. module: web_map
#. odoo-javascript
#: code:addons/web_map/static/src/map_view/map_model.js:0
#, python-format
msgid "Token invalid"
msgstr ""

#. module: web_map
#. odoo-javascript
#: code:addons/web_map/static/src/map_view/map_model.js:0
#, python-format
msgid "Too many requests, try again in a few minutes"
msgstr ""

#. module: web_map
#. odoo-javascript
#: code:addons/web_map/static/src/map_view/map_model.js:0
#, python-format
msgid "Too many routing points (maximum 25)"
msgstr ""

#. module: web_map
#. odoo-javascript
#: code:addons/web_map/static/src/map_view/map_model.js:0
#, python-format
msgid "Unauthorized connection"
msgstr ""

#. module: web_map
#. odoo-javascript
#: code:addons/web_map/static/src/map_view/map_renderer.xml:0
#, python-format
msgid "Undefined"
msgstr ""

#. module: web_map
#. odoo-javascript
#: code:addons/web_map/static/src/map_view/map_renderer.xml:0
#, python-format
msgid "Unsuccessful routing request:"
msgstr ""

#. module: web_map
#. odoo-javascript
#: code:addons/web_map/static/src/map_view/map_controller.js:0
#, python-format
msgid "Untitled"
msgstr ""

#. module: web_map
#: model:ir.model,name:web_map.model_ir_ui_view
msgid "View"
msgstr ""

#. module: web_map
#: model:ir.model.fields,field_description:web_map.field_ir_actions_act_window_view__view_mode
#: model:ir.model.fields,field_description:web_map.field_ir_ui_view__type
msgid "View Type"
msgstr ""

#. module: web_map
#. odoo-javascript
#: code:addons/web_map/static/src/map_view/map_renderer.xml:0
#, python-format
msgid "View in Google Maps"
msgstr ""

#. module: web_map
#. odoo-javascript
#: code:addons/web_map/static/src/map_view/map_model.js:0
#, python-format
msgid "Yes"
msgstr ""

#. module: web_map
#. odoo-python
#: code:addons/web_map/models/models.py:0
#, python-format
msgid "You need to set a Contact field on this model to use the Map View"
msgstr ""
