# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* account_asset_fleet
# 
# Translators:
# <AUTHOR> <EMAIL>, 2023
# <PERSON>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-10-26 22:20+0000\n"
"PO-Revision-Date: 2023-10-26 23:09+0000\n"
"Last-Translator: <PERSON>, 2023\n"
"Language-Team: Hungarian (https://app.transifex.com/odoo/teams/41243/hu/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: hu\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: account_asset_fleet
#. odoo-python
#: code:addons/account_asset_fleet/models/account_asset.py:0
#, python-format
msgid "All the lines should be from the same vehicle"
msgstr ""

#. module: account_asset_fleet
#: model:ir.model,name:account_asset_fleet.model_account_asset
msgid "Asset/Revenue Recognition"
msgstr "Eszköz/Bevétel aktiválás"

#. module: account_asset_fleet
#: model:ir.model,name:account_asset_fleet.model_account_move
msgid "Journal Entry"
msgstr "Könyvelési tétel"

#. module: account_asset_fleet
#: model:ir.model,name:account_asset_fleet.model_account_move_line
msgid "Journal Item"
msgstr "Napló tétel"

#. module: account_asset_fleet
#: model:ir.model.fields,field_description:account_asset_fleet.field_account_asset__vehicle_id
#: model_terms:ir.ui.view,arch_db:account_asset_fleet.view_account_asset_fleet_form
msgid "Vehicle"
msgstr "Jármű"
