# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* account_asset_fleet
# 
# Translators:
# Wil Odoo, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-10-26 22:20+0000\n"
"PO-Revision-Date: 2023-10-26 23:09+0000\n"
"Last-Translator: Wil Odoo, 2023\n"
"Language-Team: Arabic (https://app.transifex.com/odoo/teams/41243/ar/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ar\n"
"Plural-Forms: nplurals=6; plural=n==0 ? 0 : n==1 ? 1 : n==2 ? 2 : n%100>=3 && n%100<=10 ? 3 : n%100>=11 && n%100<=99 ? 4 : 5;\n"

#. module: account_asset_fleet
#. odoo-python
#: code:addons/account_asset_fleet/models/account_asset.py:0
#, python-format
msgid "All the lines should be from the same vehicle"
msgstr "يجب أن تكون كافة البنود من نفس المركبة "

#. module: account_asset_fleet
#: model:ir.model,name:account_asset_fleet.model_account_asset
msgid "Asset/Revenue Recognition"
msgstr "إثبات الأصل/الإيرادات "

#. module: account_asset_fleet
#: model:ir.model,name:account_asset_fleet.model_account_move
msgid "Journal Entry"
msgstr "قيد اليومية"

#. module: account_asset_fleet
#: model:ir.model,name:account_asset_fleet.model_account_move_line
msgid "Journal Item"
msgstr "عنصر اليومية"

#. module: account_asset_fleet
#: model:ir.model.fields,field_description:account_asset_fleet.field_account_asset__vehicle_id
#: model_terms:ir.ui.view,arch_db:account_asset_fleet.view_account_asset_fleet_form
msgid "Vehicle"
msgstr "المركبة"
