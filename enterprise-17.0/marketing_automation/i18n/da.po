# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* marketing_automation
# 
# Translators:
# <AUTHOR> <EMAIL>, 2023
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2023
# <PERSON><PERSON>, 2023
# <PERSON>, 2023
# <PERSON><PERSON>, 2023
# <PERSON><PERSON> <<EMAIL>>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-01-05 13:45+0000\n"
"PO-Revision-Date: 2023-10-26 23:10+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>, 2024\n"
"Language-Team: Danish (https://app.transifex.com/odoo/teams/41243/da/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: da\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_campaign__link_tracker_click_count
msgid "# Clicks"
msgstr "# Klik"

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_campaign__mailing_filter_count
msgid "# Favorite Filters"
msgstr ""

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_campaign__mass_mailing_count
msgid "# Mailings"
msgstr "# Mailinger"

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_campaign__total_participant_count
msgid "# of active and completed participants"
msgstr "# af aktive og fuldførte deltagere"

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_campaign__running_participant_count
msgid "# of active participants"
msgstr "# af aktive deltagere"

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_campaign__completed_participant_count
msgid "# of completed participants"
msgstr "# af fuldførte deltagere"

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_campaign__test_participant_count
msgid "# of test participants"
msgstr "# af test deltagere"

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_participant_view_form
msgid "<i class=\"fa fa-check-circle\"/> Bounced"
msgstr "<i class=\"fa fa-check-circle\"/> Blev afvist"

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_participant_view_form
msgid "<i class=\"fa fa-check-circle\"/> Clicked"
msgstr "<i class=\"fa fa-check-circle\"/> Klikkede"

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_participant_view_form
msgid "<i class=\"fa fa-check-circle\"/> Opened"
msgstr "<i class=\"fa fa-check-circle\"/> Åbnet"

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_participant_view_form
msgid "<i class=\"fa fa-check-circle\"/> Replied"
msgstr "<i class=\"fa fa-check-circle\"/> Svarede"

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_campaign_view_form
msgid ""
"<i class=\"fa fa-clock-o pe-1\" role=\"img\" aria-label=\"Select time\" "
"title=\"Select time\"/>"
msgstr ""
"<i class=\"fa fa-clock-o pe-1\" role=\"img\" aria-label=\"Select time\" "
"title=\"Select time\"/>"

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_campaign_view_form
msgid "<i class=\"fa fa-envelope-open-o\"/> Not opened within"
msgstr "<i class=\"fa fa-envelope-open-o\"/> Ikke åbnet indenfor"

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_campaign_view_form
msgid "<i class=\"fa fa-envelope-open-o\"/> Opened after"
msgstr "<i class=\"fa fa-envelope-open-o\"/> Åbnet efter"

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_campaign_view_form
msgid "<i class=\"fa fa-exclamation-circle\"/> Bounced after"
msgstr "<i class=\"fa fa-exclamation-circle\"/> Ikke Leveret efter"

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_campaign_view_form
msgid "<i class=\"fa fa-hand-pointer-o\"/> Clicked after"
msgstr "<i class=\"fa fa-hand-pointer-o\"/> Klikket efter"

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_campaign_view_form
msgid "<i class=\"fa fa-hand-pointer-o\"/> Not clicked within"
msgstr "<i class=\"fa fa-hand-pointer-o\"/> Ikke klikket indenfor"

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_campaign_view_form
msgid "<i class=\"fa fa-pie-chart\"/> Details"
msgstr "<i class=\"fa fa-pie-chart\"/> Detaljer"

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_campaign_view_form
msgid "<i class=\"fa fa-plus-circle\"/> Add child activity"
msgstr "<i class=\"fa fa-plus-circle\"/> Tilføj under aktivitet"

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_campaign_view_form
msgid "<i class=\"fa fa-reply\"/> Not replied within"
msgstr "<i class=\"fa fa-reply\"/> Ikke besvaret indenfor"

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_campaign_view_form
msgid "<i class=\"fa fa-reply\"/> Replied after"
msgstr "<i class=\"fa fa-reply\"/> Besvaret efter"

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_campaign_view_form
msgid ""
"<i data-trigger-type=\"activity\" class=\"fa fa-code-fork fa-rotate-180 fa-"
"flip-vertical o_ma_text_processed o_add_child_activity text-success\" "
"title=\"Add Another Activity\" role=\"img\" aria-label=\"Add Another "
"Activity\"/>"
msgstr ""
"<i data-trigger-type=\"activity\" class=\"fa fa-code-fork fa-rotate-180 fa-"
"flip-vertical o_ma_text_processed o_add_child_activity text-success\" "
"title=\"Add Another Activity\" role=\"img\" aria-label=\"Add Another "
"Activity\"/>"

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_activity_view_form
msgid "<span class=\"d-inline-block w-25\">after</span>"
msgstr ""

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_participant_view_form
msgid "<span class=\"o_form_label\">Record</span>"
msgstr "<span class=\"o_form_label\">Datasæt</span>"

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_campaign_view_form
msgid ""
"<span role=\"img\" title=\"Graph\" aria-label=\"Graph\" class=\"o_ma_activity_tab active\" data-tab-type=\"graph\">\n"
"                                                            <i class=\"fa fa-pie-chart\"/>\n"
"                                                        </span>\n"
"                                                        <span title=\"Filter\" role=\"img\" aria-label=\"Filter\" class=\"o_ma_activity_tab\" data-tab-type=\"filter\">\n"
"                                                            <i class=\"fa fa-filter\"/>\n"
"                                                        </span>"
msgstr ""
"<span role=\"img\" title=\"Graph\" aria-label=\"Graph\" class=\"o_ma_activity_tab active\" data-tab-type=\"graph\">\n"
"                                                            <i class=\"fa fa-pie-chart\"/>\n"
"                                                        </span>\n"
"                                                        <span title=\"Filter\" role=\"img\" aria-label=\"Filter\" class=\"o_ma_activity_tab\" data-tab-type=\"filter\">\n"
"                                                            <i class=\"fa fa-filter\"/>\n"
"                                                        </span>"

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_campaign_view_kanban
msgid "<span>Completed</span>"
msgstr "<span>Fuldført</span>"

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_campaign_view_kanban
msgid "<span>Running</span>"
msgstr "<span>Afvikles</span>"

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_campaign_view_kanban
msgid "<span>Total</span>"
msgstr "<span>Total</span>"

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_campaign_view_form
msgid "<strong>The workflow has been modified!</strong>"
msgstr "<strong>Workflowet er blevet modificeret!</strong>"

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_campaign__ab_testing_winner_mailing_id
msgid "A/B Campaign Winner Mailing"
msgstr ""

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_campaign__ab_testing_mailings_count
msgid "A/B Test Mailings #"
msgstr "A/B test mailforsendelser #"

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_campaign__ab_testing_completed
msgid "A/B Testing Campaign Finished"
msgstr "A/B Test kampagne afsluttet"

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_participant__trace_ids
msgid "Actions"
msgstr "Handlinger"

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_campaign__active
msgid "Active"
msgstr "Aktiv"

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_campaign__marketing_activity_ids
#: model_terms:ir.ui.view,arch_db:marketing_automation.mailing_mailing_view_form_marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.mailing_mailing_view_tree_marketing_automation
msgid "Activities"
msgstr "Aktiviteter"

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_activity__parent_id
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_trace__activity_id
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_activity_view_form
msgid "Activity"
msgstr "Aktivitet"

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_activity__activity_domain
msgid "Activity Filter"
msgstr "Aktivitet filter"

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_activity_view_form
msgid "Activity Name"
msgstr ""

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_activity__activity_type
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_trace__activity_type
msgid "Activity Type"
msgstr "Aktivitetstype"

#. module: marketing_automation
#: model:ir.model.fields,help:marketing_automation.field_marketing_activity__domain
msgid ""
"Activity will only be performed if record satisfies this domain, obtained "
"from the combination of the activity filter and its inherited filter"
msgstr ""
"Aktivitet vil kun udføres, hvis datasæt tilfredsstiller dette domæne, "
"indhentet fra kombinationen af aktivitets filtret og dens nedarvede filtre"

#. module: marketing_automation
#. odoo-python
#: code:addons/marketing_automation/models/marketing_campaign.py:0
#, python-format
msgid "Add Hot Category"
msgstr ""

#. module: marketing_automation
#. odoo-python
#: code:addons/marketing_automation/models/marketing_campaign.py:0
#, python-format
msgid "Add Tag"
msgstr "Tilføj Tag"

#. module: marketing_automation
#. odoo-python
#: code:addons/marketing_automation/models/marketing_campaign.py:0
#, python-format
msgid "Add To Confirmed List"
msgstr ""

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_campaign_view_form
msgid "Add new activity"
msgstr ""

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.mailing_mailing_view_form_marketing_activity
msgid "Add to Templates"
msgstr ""

#. module: marketing_automation
#. odoo-python
#: code:addons/marketing_automation/models/marketing_campaign.py:0
#, python-format
msgid "Add to list"
msgstr ""

#. module: marketing_automation
#. odoo-python
#: code:addons/marketing_automation/models/marketing_campaign.py:0
#, python-format
msgid "After 7 days"
msgstr "Efter 7 dage"

#. module: marketing_automation
#: model:ir.model.fields,help:marketing_automation.field_marketing_activity__allowed_parent_ids
msgid "All activities which can be the parent of this one"
msgstr "Alle aktiviteter som kan være nedarvende for denne"

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_campaign__social_post_ids
msgid "All related social media posts"
msgstr "Alle relateret social medie indlæg"

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_activity__allowed_parent_ids
msgid "Allowed parents"
msgstr "Tilladte nedarvende"

#. module: marketing_automation
#: model:ir.model.fields,help:marketing_automation.field_marketing_campaign__is_auto_campaign
msgid "Allows us to filter relevant Campaigns"
msgstr ""

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_activity__domain
msgid "Applied Filter"
msgstr "Anvendt filter"

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_campaign_view_form
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_campaign_view_search
msgid "Archived"
msgstr "Arkiveret"

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_campaign_view_form
msgid ""
"Are you sure you want to create a new participant for each matching record "
"that has not been used yet?"
msgstr ""

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.mailing_mailing_view_form_marketing_activity
msgid "Attach a file"
msgstr "Vedhæft fil"

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_campaign__is_auto_campaign
msgid "Automatically Generated Campaign"
msgstr ""

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_campaign_view_form
msgid ""
"Be aware that participants that had no more activities could be reintroduced"
" into the campaign and new traces could be created for them."
msgstr ""
"Vær opmærksom på at deltagere der ikke har nogen aktiviteter, kan "
"genintroduceres til kampagnen, og nye sporinger kan oprettes for dem."

#. module: marketing_automation
#. odoo-python
#: code:addons/marketing_automation/models/marketing_campaign.py:0
#, python-format
msgid "Blacklist Bounces"
msgstr ""

#. module: marketing_automation
#. odoo-python
#: code:addons/marketing_automation/models/marketing_campaign.py:0
#, python-format
msgid "Blacklist record"
msgstr ""

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_campaign_view_form
msgid "Bounced"
msgstr "Afvist"

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_campaign__bounced_ratio
msgid "Bounced Ratio"
msgstr "Antal afviste"

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_activity__campaign_id
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_campaign_test__campaign_id
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_participant__campaign_id
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_campaign_view_form
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_campaign_view_search
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_campaign_view_tree
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_participant_view_search
msgid "Campaign"
msgstr "Kampagne"

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_campaign__name
msgid "Campaign Identifier"
msgstr ""

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_campaign__title
msgid "Campaign Name"
msgstr "Kampagne navn"

#. module: marketing_automation
#: model:ir.actions.act_window,name:marketing_automation.marketing_campaign_action
#: model:ir.ui.menu,name:marketing_automation.marketing_campaign_menu
msgid "Campaigns"
msgstr "Kampagner"

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_campaign_test_view_form
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_participant_view_form
msgid "Cancel"
msgstr "Annullér"

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_activity_view_form
msgid "Cancel after"
msgstr "Annuller efter"

#. module: marketing_automation
#: model:ir.model.fields.selection,name:marketing_automation.selection__marketing_trace__state__canceled
msgid "Canceled"
msgstr "Annulleret"

#. module: marketing_automation
#. odoo-python
#: code:addons/marketing_automation/models/marketing_campaign.py:0
#, python-format
msgid "Check Bounce Contact"
msgstr ""

#. module: marketing_automation
#. odoo-python
#: code:addons/marketing_automation/models/marketing_campaign.py:0
#, python-format
msgid "Check Email Address"
msgstr ""

#. module: marketing_automation
#: model:ir.model.fields,help:marketing_automation.field_marketing_activity__validity_duration
msgid ""
"Check this to make sure your actions are not executed after a specific "
"amount of time after the scheduled date. (e.g. Time-limited offer, Upcoming "
"event, …)"
msgstr ""

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_activity__child_ids
msgid "Child Activities"
msgstr "Under aktiviteter"

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_campaign_view_form
msgid "Clicked"
msgstr "Klikkede"

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_trace__links_click_datetime
msgid "Clicked On"
msgstr "Klikkede på"

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_campaign_view_form
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_campaign_view_tree
msgid "Clicks"
msgstr "Klik"

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_campaign__color
msgid "Color Index"
msgstr "Farve index"

#. module: marketing_automation
#: model_terms:ir.actions.act_window,help:marketing_automation.marketing_participants_action_reporting
msgid ""
"Come back later once your campaigns are running to overview your "
"participants."
msgstr ""

#. module: marketing_automation
#. odoo-python
#: code:addons/marketing_automation/models/marketing_campaign.py:0
#: code:addons/marketing_automation/models/marketing_campaign.py:0
#, python-format
msgid "Commercial prospection"
msgstr "Kommerciel efterforskning"

#. module: marketing_automation
#: model:ir.model.fields.selection,name:marketing_automation.selection__marketing_participant__state__completed
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_participant_view_search
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_trace_view_search
msgid "Completed"
msgstr "Færdiggjort"

#. module: marketing_automation
#: model:ir.ui.menu,name:marketing_automation.marketing_automation_menu_configuration
msgid "Configuration"
msgstr "Konfiguration"

#. module: marketing_automation
#. odoo-python
#: code:addons/marketing_automation/models/marketing_campaign.py:0
#: code:addons/marketing_automation/models/marketing_campaign.py:0
#, python-format
msgid "Confirmation"
msgstr "Bekræftelse"

#. module: marketing_automation
#. odoo-python
#: code:addons/marketing_automation/models/marketing_campaign.py:0
#, python-format
msgid "Confirmed contacts"
msgstr ""

#. module: marketing_automation
#. odoo-javascript
#: code:addons/marketing_automation/static/src/xml/marketing_automation_templates.xml:0
#: model_terms:ir.actions.act_window,help:marketing_automation.marketing_campaign_action
#, python-format
msgid "Create a Campaign"
msgstr ""

#. module: marketing_automation
#. odoo-javascript
#: code:addons/marketing_automation/static/src/xml/marketing_automation_templates.xml:0
#, python-format
msgid "Create one or load a template prepared by our experts."
msgstr ""

#. module: marketing_automation
#: model_terms:ir.actions.act_window,help:marketing_automation.marketing_campaign_action
msgid ""
"Create one or load a template prepared by our experts.\n"
"                    Then sit down and let Odoo handle the rest."
msgstr ""

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_activity__create_uid
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_campaign__create_uid
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_campaign_test__create_uid
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_participant__create_uid
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_trace__create_uid
msgid "Created by"
msgstr "Oprettet af"

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_activity__create_date
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_campaign__create_date
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_campaign_test__create_date
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_participant__create_date
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_trace__create_date
msgid "Created on"
msgstr "Oprettet den"

#. module: marketing_automation
#: model:ir.model.fields,help:marketing_automation.field_marketing_campaign__ab_testing_schedule_datetime
msgid ""
"Date that will be used to know when to determine and send the winner mailing"
msgstr "Dato der vil blive anvendt til at fastslå og sende vinderforsendelsen"

#. module: marketing_automation
#: model:ir.model.fields.selection,name:marketing_automation.selection__marketing_activity__interval_type__days
#: model:ir.model.fields.selection,name:marketing_automation.selection__marketing_activity__validity_duration_type__days
msgid "Days"
msgstr "Dage"

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_activity__interval_type
msgid "Delay Type"
msgstr "Forsinkelses type"

#. module: marketing_automation
#. odoo-javascript
#: code:addons/marketing_automation/static/src/js/marketing_automation_one2many.js:0
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_campaign_view_form
#, python-format
msgid "Delete"
msgstr "Slet"

#. module: marketing_automation
#. odoo-javascript
#: code:addons/marketing_automation/static/src/js/marketing_automation_one2many.js:0
#, python-format
msgid ""
"Deleting this activity will delete ALL its children activities. Are you "
"sure?"
msgstr ""

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_trace__child_ids
msgid "Direct child traces"
msgstr "Diriger under sporinger"

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_activity__display_name
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_campaign__display_name
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_campaign_test__display_name
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_participant__display_name
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_trace__display_name
msgid "Display Name"
msgstr "Vis navn"

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_trace__res_id
msgid "Document ID"
msgstr "Dokument ID"

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_activity_view_form
msgid "Domain"
msgstr "Domæne"

#. module: marketing_automation
#: model:ir.model.fields,help:marketing_automation.field_marketing_activity__activity_domain
msgid "Domain that applies to this activity and its child activities"
msgstr "Domæne der anvendes på denne aktivitet og dens under aktiviteter"

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_campaign_view_form
msgid "Don't update"
msgstr "Opdater ikke"

#. module: marketing_automation
#. odoo-python
#: code:addons/marketing_automation/models/marketing_campaign.py:0
#: code:addons/marketing_automation/models/marketing_campaign.py:0
#, python-format
msgid "Double Opt-in"
msgstr ""

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_campaign_view_form
msgid "Edit"
msgstr "Rediger"

#. module: marketing_automation
#: model:ir.model.fields.selection,name:marketing_automation.selection__marketing_activity__activity_type__email
#: model:ir.model.fields.selection,name:marketing_automation.selection__marketing_activity__mass_mailing_id_mailing_type__mail
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_campaign_view_form
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_participant_view_form
msgid "Email"
msgstr "E-mail"

#. module: marketing_automation
#. odoo-python
#: code:addons/marketing_automation/models/marketing_activity.py:0
#, python-format
msgid "Email canceled"
msgstr ""

#. module: marketing_automation
#: model:ir.model,name:marketing_automation.model_mail_compose_message
msgid "Email composition wizard"
msgstr "Guide til at oprette e-mail"

#. module: marketing_automation
#. odoo-python
#: code:addons/marketing_automation/models/marketing_activity.py:0
#, python-format
msgid "Email failed"
msgstr "Mail slog fejl"

#. module: marketing_automation
#: model:ir.model.fields.selection,name:marketing_automation.selection__marketing_trace__state__error
msgid "Error"
msgstr "Fejl"

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_trace__state_msg
msgid "Error message"
msgstr "Fejl besked"

#. module: marketing_automation
#. odoo-python
#: code:addons/marketing_automation/models/marketing_activity.py:0
#, python-format
msgid "Error! You can't create recursive hierarchy of Activity."
msgstr "Fejl! Du kan ikke oprette et rekursivt hierarki af aktiviteten."

#. module: marketing_automation
#. odoo-python
#: code:addons/marketing_automation/models/marketing_activity.py:0
#, python-format
msgid "Exception in mass mailing: %s"
msgstr "Undtagelse i masseforsendelser: %s"

#. module: marketing_automation
#. odoo-python
#: code:addons/marketing_automation/models/marketing_activity.py:0
#, python-format
msgid "Exception in server action: %s"
msgstr "Undtagelse i server handling: %s"

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_participant_view_search
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_trace_view_search
msgid "Exclude Test"
msgstr "Udeluk test"

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_activity_view_form
msgid "Expiry Duration"
msgstr "Udløbs varighed"

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_campaign__mailing_filter_id
msgid "Favorite Filter"
msgstr ""

#. module: marketing_automation
#: model:ir.ui.menu,name:marketing_automation.mailing_filter_menu_action_marketing_automaion
msgid "Favorite Filters"
msgstr ""

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_campaign__mailing_filter_domain
msgid "Favorite filter domain"
msgstr ""

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_campaign__domain
msgid "Filter"
msgstr "Filter"

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_campaign_view_form
msgid "Generate participants"
msgstr "Generer deltagere"

#. module: marketing_automation
#. odoo-python
#: code:addons/marketing_automation/models/marketing_campaign.py:0
#, python-format
msgid "Get 10% OFF"
msgstr ""

#. module: marketing_automation
#: model_terms:ir.actions.act_window,help:marketing_automation.marketing_trace_action
msgid ""
"Here you will be able to check the results of your mailings from all "
"Marketing Automation Campaigns."
msgstr ""

#. module: marketing_automation
#. odoo-python
#: code:addons/marketing_automation/models/marketing_campaign.py:0
#, python-format
msgid "Hot"
msgstr ""

#. module: marketing_automation
#: model:ir.model.fields.selection,name:marketing_automation.selection__marketing_activity__interval_type__hours
#: model:ir.model.fields.selection,name:marketing_automation.selection__marketing_activity__validity_duration_type__hours
msgid "Hours"
msgstr "Timer"

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_activity__id
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_campaign__id
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_campaign_test__id
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_participant__id
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_trace__id
msgid "ID"
msgstr "ID"

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_campaign__is_mailing_campaign_activated
msgid "Is Mailing Campaign Activated"
msgstr ""

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_participant_view_form
msgid "It will be generated automatically once you save this record."
msgstr "Den vil blive genereret automatisk når du har gemt dette datasæt."

#. module: marketing_automation
#. odoo-python
#: code:addons/marketing_automation/models/marketing_campaign.py:0
#, python-format
msgid "Join partnership!"
msgstr "Deltag i partnerskab!"

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_activity__write_uid
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_campaign__write_uid
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_campaign_test__write_uid
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_participant__write_uid
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_trace__write_uid
msgid "Last Updated by"
msgstr "Sidst opdateret af"

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_activity__write_date
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_campaign__write_date
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_campaign_test__write_date
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_participant__write_date
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_trace__write_date
msgid "Last Updated on"
msgstr "Sidst opdateret den"

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_campaign__last_sync_date
msgid "Last activities synchronization"
msgstr "Seneste aktiviteter synkronisering"

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_campaign_test_view_form
msgid "Launch"
msgstr "Udgiv"

#. module: marketing_automation
#. odoo-python
#: code:addons/marketing_automation/wizard/marketing_campaign_test.py:0
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_campaign_view_form
#, python-format
msgid "Launch a Test"
msgstr "Påbegynd en test"

#. module: marketing_automation
#: model:ir.actions.act_window,name:marketing_automation.marketing_campaign_test_action
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_campaign_test_view_form
msgid "Launch a test"
msgstr "Påbegynd en test"

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_campaign__crm_lead_count
msgid "Leads/Opportunities count"
msgstr "Antal Kundeemner/Muligheder"

#. module: marketing_automation
#: model:ir.actions.act_window,name:marketing_automation.link_tracker_action_marketing_campaign
msgid "Link Statistics"
msgstr "Link statstikker"

#. module: marketing_automation
#: model:ir.ui.menu,name:marketing_automation.link_tracker_menu_reporting_marketing_automation
msgid "Link Tracker"
msgstr "Sporing af link"

#. module: marketing_automation
#: model_terms:ir.actions.act_window,help:marketing_automation.link_tracker_action_marketing_campaign
msgid ""
"Link Trackers are created when mailings with links are sent to track how "
"many clicks they get."
msgstr ""

#. module: marketing_automation
#: model:ir.model.fields.selection,name:marketing_automation.selection__marketing_activity__trigger_category__email
msgid "Mail"
msgstr "Mail"

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.mailing_mailing_view_form_marketing_activity
#: model_terms:ir.ui.view,arch_db:marketing_automation.mailing_mailing_view_form_marketing_automation
msgid "Mail Body"
msgstr "Mail krop"

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.mailing_mailing_view_form_marketing_activity
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_activity_view_form
msgid "Mail Template"
msgstr "Mailskabelon"

#. module: marketing_automation
#: model:ir.model.fields.selection,name:marketing_automation.selection__marketing_activity__trigger_type__mail_bounce
msgid "Mail: bounced"
msgstr "Mail: returneret"

#. module: marketing_automation
#: model:ir.model.fields.selection,name:marketing_automation.selection__marketing_activity__trigger_type__mail_click
msgid "Mail: clicked"
msgstr "Mail: Klikket"

#. module: marketing_automation
#: model:ir.model.fields.selection,name:marketing_automation.selection__marketing_activity__trigger_type__mail_not_click
msgid "Mail: not clicked"
msgstr "Mail: Ikke klikket"

#. module: marketing_automation
#: model:ir.model.fields.selection,name:marketing_automation.selection__marketing_activity__trigger_type__mail_not_open
msgid "Mail: not opened"
msgstr "Mail: ikke åbnet"

#. module: marketing_automation
#: model:ir.model.fields.selection,name:marketing_automation.selection__marketing_activity__trigger_type__mail_not_reply
msgid "Mail: not replied"
msgstr "Mail: ikke svaret"

#. module: marketing_automation
#: model:ir.model.fields.selection,name:marketing_automation.selection__marketing_activity__trigger_type__mail_open
msgid "Mail: opened"
msgstr "Mail: åbnet"

#. module: marketing_automation
#: model:ir.model.fields.selection,name:marketing_automation.selection__marketing_activity__trigger_type__mail_reply
msgid "Mail: replied"
msgstr "Mail: Besvaret"

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.mailing_mailing_view_form_marketing_activity
msgid "Mailing"
msgstr "Mailing"

#. module: marketing_automation
#: model:ir.model,name:marketing_automation.model_mailing_trace
msgid "Mailing Statistics"
msgstr "Mailing statistikker"

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_activity__mass_mailing_id_mailing_type
msgid "Mailing Type"
msgstr "Mailing type"

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_campaign_view_tree
msgid "Mailings"
msgstr "Udsendelser"

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_campaign_view_form
msgid "Mails sent and not bounced"
msgstr "Mails afsendt og ikke afvist"

#. module: marketing_automation
#. odoo-python
#: code:addons/marketing_automation/models/marketing_trace.py:0
#, python-format
msgid "Manually"
msgstr "Manuelt"

#. module: marketing_automation
#. odoo-python
#: code:addons/marketing_automation/models/marketing_participant.py:0
#, python-format
msgid "Marked as completed"
msgstr "Markeret som fuldført"

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_mailing_mailing__marketing_activity_ids
msgid "Marketing Activities"
msgstr "Marketing aktiviteter"

#. module: marketing_automation
#: model:ir.model,name:marketing_automation.model_marketing_activity
#: model:ir.model.fields,field_description:marketing_automation.field_mail_compose_message__marketing_activity_id
msgid "Marketing Activity"
msgstr "Marketing aktivitet"

#. module: marketing_automation
#: model:ir.module.category,name:marketing_automation.module_marketing_automation_category
#: model:ir.ui.menu,name:marketing_automation.marketing_automation_menu
msgid "Marketing Automation"
msgstr "Marketing automatisering"

#. module: marketing_automation
#: model:ir.actions.act_window,name:marketing_automation.mail_mass_mailing_action_marketing_automation
msgid "Marketing Automation Mailings"
msgstr "Marketing automatisering mailinger"

#. module: marketing_automation
#: model:ir.actions.server,name:marketing_automation.ir_cron_campaign_execute_activities_ir_actions_server
msgid "Marketing Automation: execute activities"
msgstr "Marketing automatisering: udfør aktiviteter"

#. module: marketing_automation
#: model:ir.actions.server,name:marketing_automation.ir_cron_campaign_sync_participants_ir_actions_server
msgid "Marketing Automation: sync participants"
msgstr "Marketing automatisering: synkroniser deltagere"

#. module: marketing_automation
#: model:ir.model,name:marketing_automation.model_marketing_campaign
msgid "Marketing Campaign"
msgstr "Marketingkampagne"

#. module: marketing_automation
#: model:ir.model,name:marketing_automation.model_marketing_campaign_test
msgid "Marketing Campaign: Launch a Test"
msgstr "Marketing kampagne: Påbegynd en test"

#. module: marketing_automation
#: model:ir.model,name:marketing_automation.model_marketing_participant
msgid "Marketing Participant"
msgstr "Marketing deltager"

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_activity__mass_mailing_id
msgid "Marketing Template"
msgstr "Marketing skabelon"

#. module: marketing_automation
#: model:ir.model,name:marketing_automation.model_marketing_trace
#: model:ir.model.fields,field_description:marketing_automation.field_mailing_trace__marketing_trace_id
msgid "Marketing Trace"
msgstr "Marketing spor"

#. module: marketing_automation
#: model:ir.model.fields,help:marketing_automation.field_mailing_mailing__use_in_marketing_automation
msgid ""
"Marketing campaigns use mass mailings with some specific behavior; this "
"field is used to indicate its statistics may be suspicious."
msgstr ""
"Marketing kampagner anvender masse mailinger med en specifik opførsel; dette"
" felt bruges til at indikere at dets statistikker kan være mistænkelige."

#. module: marketing_automation
#: model:ir.model,name:marketing_automation.model_mailing_mailing
msgid "Mass Mailing"
msgstr "Mailudsendelse"

#. module: marketing_automation
#: model:ir.model,name:marketing_automation.model_mailing_trace_report
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_trace_view_form
msgid "Mass Mailing Statistics"
msgstr "Statistikker for mailudsendelser"

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_campaign__mailing_mail_ids
msgid "Mass Mailings"
msgstr "Mailudsendelser"

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_trace__mailing_trace_ids
msgid "Mass mailing statistics"
msgstr "Masse mailing statistikker"

#. module: marketing_automation
#. odoo-python
#: code:addons/marketing_automation/models/marketing_campaign.py:0
#: code:addons/marketing_automation/models/marketing_campaign.py:0
#, python-format
msgid "Message for sales person"
msgstr "Besked til salgs personale"

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_activity__model_id
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_campaign__model_id
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_campaign_test__model_id
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_participant__model_id
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_participant_view_search
msgid "Model"
msgstr "Model"

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.ir_model_view_tree_marketing
msgid "Model Description"
msgstr "Model beskrivelse"

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_activity__model_name
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_campaign__model_name
msgid "Model Name"
msgstr "Modelnavn"

#. module: marketing_automation
#: model:ir.model.fields.selection,name:marketing_automation.selection__marketing_activity__interval_type__months
#: model:ir.model.fields.selection,name:marketing_automation.selection__marketing_activity__validity_duration_type__months
msgid "Months"
msgstr "Måneder"

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_activity__name
msgid "Name"
msgstr "Navn"

#. module: marketing_automation
#: model:ir.model.fields.selection,name:marketing_automation.selection__marketing_campaign__state__draft
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_campaign_view_search
msgid "New"
msgstr "Ny"

#. module: marketing_automation
#. odoo-python
#: code:addons/marketing_automation/models/marketing_campaign.py:0
#, python-format
msgid "Next activity: Check Email Address"
msgstr ""

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_participant_view_form
msgid "No activity"
msgstr "Ingen aktivitet"

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_campaign_view_form
msgid "No activity for this campaign."
msgstr ""

#. module: marketing_automation
#: model_terms:ir.actions.act_window,help:marketing_automation.link_tracker_action_marketing_campaign
#: model_terms:ir.actions.act_window,help:marketing_automation.marketing_participants_action_reporting
#: model_terms:ir.actions.act_window,help:marketing_automation.marketing_trace_action
msgid "No data yet!"
msgstr "Ingen data endnu!"

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_campaign_view_form
msgid "Not Clicked"
msgstr "Ikke klikket"

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_campaign_view_form
msgid "Not Opened"
msgstr "Ikke åbnet"

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_campaign_view_form
msgid "Not Replied"
msgstr "Ikke besvaret"

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_participant_view_form
msgid "Not bounced yet"
msgstr "Ikke afvist endnu"

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_participant_view_form
msgid "Not clicked yet"
msgstr "Ikke klikket endnu"

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_participant_view_form
msgid "Not opened yet"
msgstr "Ikke åbnet endnu"

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_participant_view_form
msgid "Not replied yet"
msgstr "Ikke besvaret endnu"

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_campaign__mailing_mail_count
msgid "Number of Mass Mailing"
msgstr "Antal af masse mailing"

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_campaign__click_count
msgid "Number of clicks generated by the campaign"
msgstr "Antal klik genereret af denne kampagne"

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_campaign__social_engagement
msgid ""
"Number of interactions (likes, shares, comments ...) with the social posts"
msgstr ""
"Antal interaktioner (synes om, delinger, kommentarer ...) med de sociale "
"indlæg"

#. module: marketing_automation
#. odoo-python
#: code:addons/marketing_automation/models/marketing_campaign.py:0
#, python-format
msgid "Offer free catalog"
msgstr "Tilbyd gratis katalog"

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_campaign_view_form
msgid "Opened"
msgstr "Åbnet"

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_campaign__opened_ratio
msgid "Opened Ratio"
msgstr "Åbnet forhold"

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.mailing_mailing_view_form_marketing_activity
msgid "Options"
msgstr "Valgmuligheder"

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_campaign_view_form
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_participant_view_form
msgid "Other activity"
msgstr "Anden aktivitet"

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_trace__parent_id
msgid "Parent"
msgstr "Overordnet"

#. module: marketing_automation
#. odoo-python
#: code:addons/marketing_automation/models/marketing_trace.py:0
#, python-format
msgid "Parent activity mail bounced"
msgstr "Oprindelig aktivitet mail afvist"

#. module: marketing_automation
#. odoo-python
#: code:addons/marketing_automation/models/marketing_trace.py:0
#, python-format
msgid "Parent activity mail clicked"
msgstr "Oprindelig aktivitet mail klikket"

#. module: marketing_automation
#. odoo-python
#: code:addons/marketing_automation/models/marketing_trace.py:0
#, python-format
msgid "Parent activity mail opened"
msgstr "Oprindelig aktivitet mail åbnet"

#. module: marketing_automation
#. odoo-python
#: code:addons/marketing_automation/models/marketing_trace.py:0
#, python-format
msgid "Parent activity mail replied"
msgstr "Oprindelig aktivitet mail besvaret"

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_trace__participant_id
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_participant_view_form
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_participant_view_tree
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_trace_view_form
msgid "Participant"
msgstr "Deltager"

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_participant_view_tree
msgid "Participant Name"
msgstr "Deltager navn"

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_participant_view_graph
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_participant_view_pivot
msgid "Participant summary"
msgstr "Deltager opsummering"

#. module: marketing_automation
#: model:ir.actions.act_window,name:marketing_automation.marketing_participant_action_campaign
#: model:ir.actions.act_window,name:marketing_automation.marketing_participant_action_campaign_test
#: model:ir.actions.act_window,name:marketing_automation.marketing_participants_action_mail
#: model:ir.actions.act_window,name:marketing_automation.marketing_participants_action_reporting
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_campaign__participant_ids
#: model:ir.ui.menu,name:marketing_automation.marketing_participants_menu
#: model_terms:ir.ui.view,arch_db:marketing_automation.mailing_mailing_view_form_marketing_activity
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_campaign_view_form
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_campaign_view_tree
msgid "Participants"
msgstr "Deltagere"

#. module: marketing_automation
#. odoo-python
#: code:addons/marketing_automation/models/marketing_activity.py:0
#, python-format
msgid "Participants of %s (%s)"
msgstr "Deltagere af  %s (%s)"

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_activity_view_form
msgid "Pick a Server Action"
msgstr ""

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_activity_view_form
msgid "Pick a Template"
msgstr ""

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_participant_view_form
msgid "Pick a record..."
msgstr ""

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_campaign_test_view_form
msgid "Pick or create a/an"
msgstr ""

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_activity__processed
#: model:ir.model.fields.selection,name:marketing_automation.selection__marketing_trace__state__processed
msgid "Processed"
msgstr "Behandlet"

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_campaign_view_form
msgid "REJECTED"
msgstr "AFVIST"

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_campaign__received_ratio
msgid "Received Ratio"
msgstr "Modtaget mængde"

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_campaign_test__resource_ref
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_participant__resource_ref
msgid "Record"
msgstr "Datasæt"

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_campaign_test__res_id
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_participant__res_id
msgid "Record ID"
msgstr "Datasæt ID"

#. module: marketing_automation
#. odoo-python
#: code:addons/marketing_automation/models/marketing_participant.py:0
#, python-format
msgid "Record deleted"
msgstr "Datasæt slettet"

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_campaign_test__model_name
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_participant__model_name
msgid "Record model"
msgstr "Datasæt model"

#. module: marketing_automation
#. odoo-python
#: code:addons/marketing_automation/models/marketing_activity.py:0
#: code:addons/marketing_automation/models/marketing_activity.py:0
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_activity__rejected
#: model:ir.model.fields.selection,name:marketing_automation.selection__marketing_trace__state__rejected
#, python-format
msgid "Rejected"
msgstr "Afvist"

#. module: marketing_automation
#. odoo-python
#: code:addons/marketing_automation/models/marketing_activity.py:0
#, python-format
msgid "Rejected by activity filter or record deleted / archived"
msgstr "Afvist af aktivitets filter eller datasæt slettet / arkiveret"

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_campaign_view_form
msgid "Reload a favorite filter"
msgstr ""

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.mailing_mailing_view_form_marketing_activity
msgid "Remove from Templates"
msgstr ""

#. module: marketing_automation
#: model:ir.model.fields.selection,name:marketing_automation.selection__marketing_participant__state__unlinked
msgid "Removed"
msgstr "Fjernet"

#. module: marketing_automation
#: model:ir.model.fields,help:marketing_automation.field_marketing_participant__state
msgid "Removed means the related record does not exist anymore."
msgstr "Fjernet betyder, at det relaterede datasæt ikke længere eksistere."

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_campaign_view_form
msgid "Replied"
msgstr "Besvaret"

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_campaign__replied_ratio
msgid "Replied Ratio"
msgstr "Besvaret forhold"

#. module: marketing_automation
#: model:ir.ui.menu,name:marketing_automation.marketing_automation_reporting_menu
msgid "Reporting"
msgstr "Rapportering"

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_activity__require_sync
msgid "Require trace sync"
msgstr "Kræver spor synkronisering"

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_participant_view_form
msgid "Resource ID"
msgstr "Ressource ID"

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_participant_view_search
msgid "Resource Name"
msgstr "Navn på ressourse"

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_campaign__user_id
msgid "Responsible"
msgstr "Ansvarlig"

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_campaign_view_form
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_participant_view_form
msgid "Run"
msgstr "Kør"

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_campaign_view_form
msgid "Run the next scheduled activity for each participant of this campaign?"
msgstr ""

#. module: marketing_automation
#: model:ir.model.fields.selection,name:marketing_automation.selection__marketing_campaign__state__running
#: model:ir.model.fields.selection,name:marketing_automation.selection__marketing_participant__state__running
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_campaign_view_search
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_participant_view_search
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_trace_view_search
msgid "Running"
msgstr "Kører"

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_campaign_view_form
msgid "SUCCESS"
msgstr "SUCCES"

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_trace__schedule_date
msgid "Schedule Date"
msgstr "Planlæg dato"

#. module: marketing_automation
#: model:ir.model.fields.selection,name:marketing_automation.selection__marketing_trace__state__scheduled
msgid "Scheduled"
msgstr "Planlagt"

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_campaign_view_search
msgid "Search Campaign"
msgstr "Søg kampagne"

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_participant_view_search
msgid "Search Participant"
msgstr "Søg deltager"

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_trace_view_search
msgid "Search Traces"
msgstr "Søg sporinger"

#. module: marketing_automation
#: model:ir.model.fields,help:marketing_automation.field_marketing_campaign__ab_testing_winner_selection
msgid "Selection to determine the winner mailing that will be sent."
msgstr "Udvalg til at afgøre vinderforsendelsen, der vil blive sendt."

#. module: marketing_automation
#. odoo-python
#: code:addons/marketing_automation/models/marketing_campaign.py:0
#, python-format
msgid "Send 10% Welcome Discount"
msgstr ""

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_campaign__ab_testing_schedule_datetime
msgid "Send Final On"
msgstr "Send sidste den"

#. module: marketing_automation
#. odoo-python
#: code:addons/marketing_automation/models/marketing_campaign.py:0
#, python-format
msgid "Send Welcome Email"
msgstr ""

#. module: marketing_automation
#. odoo-python
#: code:addons/marketing_automation/models/marketing_campaign.py:0
#, python-format
msgid "Send a free catalog and follow-up according to reactions."
msgstr ""

#. module: marketing_automation
#. odoo-python
#: code:addons/marketing_automation/models/marketing_campaign.py:0
#, python-format
msgid "Send a welcome email to contacts and tag them if they click in it."
msgstr ""

#. module: marketing_automation
#. odoo-python
#: code:addons/marketing_automation/models/marketing_campaign.py:0
#, python-format
msgid ""
"Send a welcome email to new subscribers, remove the address that bounced."
msgstr ""

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_activity__interval_number
msgid "Send after"
msgstr "Send efter"

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_activity__interval_standardized
msgid "Send after (in hours)"
msgstr "Send efter (i timer)"

#. module: marketing_automation
#. odoo-python
#: code:addons/marketing_automation/models/marketing_campaign.py:0
#, python-format
msgid "Send an email to new recipients to confirm their consent."
msgstr ""

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_campaign_view_form
msgid "Sent"
msgstr "Sendt"

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_activity__server_action_id
#: model:ir.model.fields.selection,name:marketing_automation.selection__marketing_activity__activity_type__action
msgid "Server Action"
msgstr "Server handling"

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_campaign__social_posts_count
msgid "Social Media Posts"
msgstr "Sociale medie posteringer"

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_campaign_view_form
msgid ""
"Some participants are already running on this campaign. Click on 'Update' to"
" apply the modifications you've just made."
msgstr ""
"Visse deltagere afvikler allerede denne kampagne. Klik på 'Opdater' for at "
"avende modificeringerne du lige har foretaget."

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_activity__source_id
msgid "Source"
msgstr "Kilde"

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_mailing_mailing__use_in_marketing_automation
msgid "Specific mailing used in marketing campaign"
msgstr "Specifik mailing brugt i marketing kampagne"

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_campaign__stage_id
msgid "Stage"
msgstr "Fase"

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_campaign_view_form
msgid "Start"
msgstr "Start"

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_campaign__state
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_participant__state
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_trace__state
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_participant_view_search
msgid "State"
msgstr "Stat"

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_participant_view_form
msgid "State: #{record.state.raw_value}"
msgstr "Status: #{record.state.raw_value}"

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_activity__statistics_graph_data
msgid "Statistics Graph Data"
msgstr "Statistikker graf data"

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_trace__mailing_trace_status
msgid "Status"
msgstr "Status"

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_campaign_view_form
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_participant_view_form
msgid "Stop"
msgstr "Stop"

#. module: marketing_automation
#: model:ir.model.fields.selection,name:marketing_automation.selection__marketing_campaign__state__stopped
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_campaign_view_search
msgid "Stopped"
msgstr "Stoppet"

#. module: marketing_automation
#: model:ir.model.fields,help:marketing_automation.field_marketing_trace__links_click_datetime
msgid "Stores last click datetime in case of multi clicks."
msgstr "Gemmer sidste klik datetime i tilfælde af flere klik."

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.mailing_mailing_view_form_marketing_activity
msgid "Subject"
msgstr "Emne"

#. module: marketing_automation
#. odoo-python
#: code:addons/marketing_automation/models/marketing_activity.py:0
#: code:addons/marketing_automation/models/marketing_activity.py:0
#, python-format
msgid "Success"
msgstr "Succes"

#. module: marketing_automation
#. odoo-python
#: code:addons/marketing_automation/models/marketing_campaign.py:0
#, python-format
msgid ""
"Switching Target Model invalidates the existing activities. Either update "
"your activity actions to match the new Target Model or delete them."
msgstr ""
"Skiftning af Mål-model invaliderer eksisterende. Enten opdater din "
"aktivitets handlinger til at stemme overens med den nye Målmodel, eller slet"
" dem."

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_campaign__require_sync
msgid "Sync of participants is required"
msgstr "Synkronisering af deltagere er påkrævet"

#. module: marketing_automation
#. odoo-python
#: code:addons/marketing_automation/models/marketing_campaign.py:0
#: code:addons/marketing_automation/models/marketing_campaign.py:0
#, python-format
msgid "Tag Hot Contacts"
msgstr ""

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_campaign__tag_ids
msgid "Tags"
msgstr "Tags"

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_campaign_view_form
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_campaign_view_search
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_campaign_view_tree
msgid "Target"
msgstr "Mål"

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_participant_view_tree
msgid "Target Model"
msgstr "Mål model"

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_campaign_view_form
msgid "Templates"
msgstr "Skabeloner"

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_participant_view_form
msgid "Test"
msgstr "Test"

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_participant__is_test
msgid "Test Record"
msgstr "Test datasæt"

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_trace__is_test
msgid "Test Trace"
msgstr "Test spor"

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_campaign_view_form
msgid "Tests"
msgstr "Test"

#. module: marketing_automation
#. odoo-python
#: code:addons/marketing_automation/models/marketing_campaign.py:0
#, python-format
msgid ""
"The saved filter targets different model and is incompatible with this "
"campaign."
msgstr ""

#. module: marketing_automation
#. odoo-javascript
#: code:addons/marketing_automation/static/src/xml/marketing_automation_templates.xml:0
#, python-format
msgid "Then sit down and let Odoo handle the rest!"
msgstr ""

#. module: marketing_automation
#. odoo-python
#: code:addons/marketing_automation/models/marketing_activity.py:0
#, python-format
msgid ""
"To use this feature you should be an administrator or belong to the "
"marketing automation group."
msgstr ""
"For at bruge denne funktion skal du være administrator, eller tilhøre "
"marketing automatiserings gruppen."

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_activity__total_bounce
msgid "Total Bounce"
msgstr "Samlet afvisninger"

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_activity__total_click
msgid "Total Click"
msgstr "Total antal klik"

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_activity__total_open
msgid "Total Open"
msgstr "Samlet åbnet"

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_activity__total_reply
msgid "Total Reply"
msgstr "Total antal svar"

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_activity__total_sent
msgid "Total Sent"
msgstr "Total sendt"

#. module: marketing_automation
#: model:ir.actions.act_window,name:marketing_automation.marketing_trace_action
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_activity__trace_ids
#: model:ir.ui.menu,name:marketing_automation.marketing_trace_menu
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_trace_view_tree
msgid "Traces"
msgstr "Sporinger"

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_activity_view_form
msgid "Trigger"
msgstr "Trigger"

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_activity__trigger_category
msgid "Trigger Category"
msgstr "Udløser Kategori"

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_activity__trigger_type
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_trace__trigger_type
msgid "Trigger Type"
msgstr "Udløsnings type"

#. module: marketing_automation
#: model:ir.model,name:marketing_automation.model_utm_campaign
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_activity__utm_campaign_id
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_campaign__utm_campaign_id
msgid "UTM Campaign"
msgstr "UTM Kampagne"

#. module: marketing_automation
#: model:ir.model,name:marketing_automation.model_utm_source
msgid "UTM Source"
msgstr "UTM kilde"

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_campaign_view_form
msgid "Unicity based on"
msgstr "Enhed baseret på"

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_campaign__unique_field_id
msgid "Unique Field"
msgstr "Unikt felt"

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_campaign_view_form
msgid "Update"
msgstr "Opdater"

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_campaign__use_leads
msgid "Use Leads"
msgstr "Brug Leads"

#. module: marketing_automation
#: model:ir.model.fields,help:marketing_automation.field_marketing_campaign__unique_field_id
msgid ""
"Used to avoid duplicates based on model field.\n"
"e.g.\n"
"                For model 'Customers', select email field here if you don't\n"
"                want to process records which have the same email address"
msgstr ""
"Anvendes til at undgå duplikeringer baseret på model felt.\n"
"f.eks.\n"
"                 For model 'Kunder', vælg mail felt her hvis du ikke\n"
"                 vil behandle datasæt, som har samme email adresse"

#. module: marketing_automation
#: model:res.groups,name:marketing_automation.group_marketing_automation_user
msgid "User"
msgstr "Bruger"

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_activity__validity_duration_number
msgid "Valid during"
msgstr "Gyldig under"

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_activity__validity_duration
msgid "Validity Duration"
msgstr "Gyldig i"

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_activity__validity_duration_type
msgid "Validity Duration Type"
msgstr "Gyldighed varighed type"

#. module: marketing_automation
#. odoo-python
#: code:addons/marketing_automation/models/marketing_campaign.py:0
#, python-format
msgid "Warning"
msgstr "Advarsel"

#. module: marketing_automation
#: model:ir.model.fields.selection,name:marketing_automation.selection__marketing_activity__interval_type__weeks
#: model:ir.model.fields.selection,name:marketing_automation.selection__marketing_activity__validity_duration_type__weeks
msgid "Weeks"
msgstr "Uger"

#. module: marketing_automation
#. odoo-python
#: code:addons/marketing_automation/models/marketing_campaign.py:0
#: code:addons/marketing_automation/models/marketing_campaign.py:0
#, python-format
msgid "Welcome Flow"
msgstr ""

#. module: marketing_automation
#. odoo-python
#: code:addons/marketing_automation/models/marketing_campaign.py:0
#: code:addons/marketing_automation/models/marketing_campaign.py:0
#, python-format
msgid "Welcome!"
msgstr "Velkommen!"

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_campaign__ab_testing_winner_selection
msgid "Winner Selection"
msgstr "Valg af vinder"

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_campaign_view_form
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_participant_view_form
msgid "Workflow"
msgstr "Arbejdsgang"

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_participant_view_form
msgid "Workflow Started On"
msgstr "Workflow startet den"

#. module: marketing_automation
#. odoo-python
#: code:addons/marketing_automation/models/marketing_activity.py:0
#, python-format
msgid ""
"You are trying to set the activity \"%s\" as \"%s\" while its child \"%s\" has the trigger type \"%s\"\n"
"Please modify one of those activities before saving."
msgstr ""
"Du prøver at angive aktiviteten \"%s\" som \"%s\", imens dens nedarver \"%s\" har udløsningstypen \"%s\"\n"
"Vær venlig at redigere en af disse aktiviteter før du gemmer."

#. module: marketing_automation
#. odoo-python
#: code:addons/marketing_automation/models/utm_campaign.py:0
#, python-format
msgid ""
"You cannot delete these UTM Campaigns as they are linked to the following marketing campaigns in Marketing Automation:\n"
"%(campaign_names)s"
msgstr ""

#. module: marketing_automation
#. odoo-python
#: code:addons/marketing_automation/models/utm_source.py:0
#, python-format
msgid ""
"You cannot delete these UTM Sources as they are linked to the following marketing activities in Marketing Automation:\n"
"%(activities_names)s"
msgstr ""

#. module: marketing_automation
#. odoo-python
#: code:addons/marketing_automation/models/marketing_campaign.py:0
#, python-format
msgid "You must set up at least one activity to start this campaign."
msgstr "Du skal oprette mindst én aktivitet for at påbegynde denne kampagne."

#. module: marketing_automation
#: model:ir.model.fields.selection,name:marketing_automation.selection__marketing_activity__trigger_type__activity
msgid "another activity"
msgstr "en anden aktivitet"

#. module: marketing_automation
#: model:ir.model.fields.selection,name:marketing_automation.selection__marketing_activity__trigger_type__begin
msgid "beginning of workflow"
msgstr ""

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_campaign_test_view_form
msgid "e.g. \"Brandon Freeman\""
msgstr ""

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_activity_view_form
msgid "e.g. eCommerce Offers"
msgstr "f.eks. eHandel tilbud"

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_campaign_view_form
msgid "e.g. eCommerce Offers Plan"
msgstr ""

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_campaign_test_view_form
msgid "to generate a Test Participant"
msgstr ""
