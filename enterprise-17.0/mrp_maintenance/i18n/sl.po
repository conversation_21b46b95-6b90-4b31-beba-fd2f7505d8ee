# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* mrp_maintenance
# 
# Translators:
# <PERSON><PERSON> <<EMAIL>>, 2023
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2023
# <PERSON><PERSON>, 2023
# <PERSON><PERSON> <<EMAIL>>, 2023
# <PERSON> <<EMAIL>>, 2023
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2023
# <PERSON>, 2023
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2023
# <AUTHOR> <EMAIL>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-10-26 22:21+0000\n"
"PO-Revision-Date: 2023-10-26 23:10+0000\n"
"Last-Translator: matjaz k <<EMAIL>>, 2023\n"
"Language-Team: Slovenian (https://app.transifex.com/odoo/teams/41243/sl/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: sl\n"
"Plural-Forms: nplurals=4; plural=(n%100==1 ? 0 : n%100==2 ? 1 : n%100==3 || n%100==4 ? 2 : 3);\n"

#. module: mrp_maintenance
#: model_terms:ir.ui.view,arch_db:mrp_maintenance.mrp_production_view_form_inherit_maintenance
msgid "<span class=\"o_stat_text\">Maintenance</span>"
msgstr ""

#. module: mrp_maintenance
#: model_terms:ir.ui.view,arch_db:mrp_maintenance.maintenance_equipment_view_form_inherit_mrp
msgid "<span class=\"o_stat_text\">Work Center</span>"
msgstr ""

#. module: mrp_maintenance
#: model:ir.model.fields,field_description:mrp_maintenance.field_mrp_workcenter__message_needaction
msgid "Action Needed"
msgstr "Potreben je ukrep"

#. module: mrp_maintenance
#: model:ir.model.fields,field_description:mrp_maintenance.field_mrp_workcenter__activity_ids
msgid "Activities"
msgstr "Aktivnosti"

#. module: mrp_maintenance
#: model:ir.model.fields,field_description:mrp_maintenance.field_mrp_workcenter__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "Izjema pri oznaki aktivnosti"

#. module: mrp_maintenance
#: model:ir.model.fields,field_description:mrp_maintenance.field_mrp_workcenter__activity_state
msgid "Activity State"
msgstr "Stanje aktivnosti"

#. module: mrp_maintenance
#: model:ir.model.fields,field_description:mrp_maintenance.field_mrp_workcenter__activity_type_icon
msgid "Activity Type Icon"
msgstr "Ikona vrste aktivnosti"

#. module: mrp_maintenance
#: model_terms:ir.actions.act_window,help:mrp_maintenance.mrp_workcenter_request_action_from_workcenter
msgid "Add a new maintenance request"
msgstr "Dodajte nov vzdrževalni zahtevek"

#. module: mrp_maintenance
#: model:ir.model.fields,field_description:mrp_maintenance.field_maintenance_request__recurring_leaves_count
msgid "Additional Leaves to Plan Ahead"
msgstr ""

#. module: mrp_maintenance
#: model:ir.model.fields,field_description:mrp_maintenance.field_mrp_workcenter__message_attachment_count
msgid "Attachment Count"
msgstr "Število prilog"

#. module: mrp_maintenance
#: model:ir.model.fields,field_description:mrp_maintenance.field_maintenance_request__block_workcenter
msgid "Block Workcenter"
msgstr ""

#. module: mrp_maintenance
#: model:ir.model.fields,help:mrp_maintenance.field_maintenance_request__recurring_leaves_count
msgid ""
"Block the workcenter for this many time slots in the future in advance."
msgstr ""

#. module: mrp_maintenance
#: model:ir.model.fields,field_description:mrp_maintenance.field_maintenance_request__company_id
#: model:ir.model.fields,field_description:mrp_maintenance.field_mrp_workcenter__company_id
msgid "Company"
msgstr "Podjetje"

#. module: mrp_maintenance
#: model:ir.model.fields,help:mrp_maintenance.field_mrp_workcenter__estimated_next_failure
msgid "Computed as Latest Failure Date + MTBF"
msgstr ""

#. module: mrp_maintenance
#: model_terms:ir.actions.act_window,help:mrp_maintenance.maintenance_workcenter_action
msgid "Create a new work center"
msgstr "Ustvari nov delovni center"

#. module: mrp_maintenance
#: model:maintenance.equipment,name:mrp_maintenance.equipment_furniture1
msgid "Crosscut Saw: 8 ppi."
msgstr ""

#. module: mrp_maintenance
#: model:ir.model.fields,field_description:mrp_maintenance.field_mrp_workcenter__maintenance_open_count
msgid "Current Maintenance"
msgstr "Tekoče vzdrževanje"

#. module: mrp_maintenance
#: model_terms:ir.ui.view,arch_db:mrp_maintenance.maintenance_request_view_form_inherit_mrp
msgid "Discard"
msgstr "Opusti"

#. module: mrp_maintenance
#: model:maintenance.equipment,name:mrp_maintenance.equipment_furniture3
msgid "Drill Machine"
msgstr ""

#. module: mrp_maintenance
#: model:ir.model.fields,field_description:mrp_maintenance.field_mrp_workcenter__effective_date
msgid "Effective Date"
msgstr "DUR"

#. module: mrp_maintenance
#: model:ir.model.fields,field_description:mrp_maintenance.field_maintenance_request__equipment_id
#: model:ir.model.fields.selection,name:mrp_maintenance.selection__maintenance_request__maintenance_for__equipment
#: model_terms:ir.ui.view,arch_db:mrp_maintenance.mrp_workcenter_view_form_inherit_maintenance
msgid "Equipment"
msgstr "Oprema"

#. module: mrp_maintenance
#: model_terms:ir.ui.view,arch_db:mrp_maintenance.mrp_workcenter_view_form_inherit_maintenance
msgid "Est. Next Failure"
msgstr ""

#. module: mrp_maintenance
#: model_terms:ir.ui.view,arch_db:mrp_maintenance.mrp_workcenter_view_form_inherit_maintenance
msgid "Estimated Next Failure"
msgstr ""

#. module: mrp_maintenance
#: model:ir.model.fields,field_description:mrp_maintenance.field_mrp_workcenter__estimated_next_failure
msgid "Estimated time before next failure (in days)"
msgstr ""

#. module: mrp_maintenance
#: model:ir.model.fields,field_description:mrp_maintenance.field_mrp_workcenter__expected_mtbf
msgid "Expected MTBF"
msgstr ""

#. module: mrp_maintenance
#: model:ir.model.fields,help:mrp_maintenance.field_mrp_workcenter__expected_mtbf
#: model_terms:ir.ui.view,arch_db:mrp_maintenance.mrp_workcenter_view_form_inherit_maintenance
msgid "Expected Mean Time Between Failure"
msgstr ""

#. module: mrp_maintenance
#: model_terms:ir.actions.act_window,help:mrp_maintenance.mrp_workcenter_request_action_from_workcenter
msgid ""
"Follow the process of the request and communicate with the collaborator."
msgstr "Sledite procesu zahtevka in komunicirajte s sodelavcem."

#. module: mrp_maintenance
#: model:ir.model.fields,field_description:mrp_maintenance.field_mrp_workcenter__message_follower_ids
msgid "Followers"
msgstr "Sledilci"

#. module: mrp_maintenance
#: model:ir.model.fields,field_description:mrp_maintenance.field_mrp_workcenter__message_partner_ids
msgid "Followers (Partners)"
msgstr "Sledilci (partnerji)"

#. module: mrp_maintenance
#: model:ir.model.fields,help:mrp_maintenance.field_mrp_workcenter__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "Font izjemna ikona npr. fa-tasks"

#. module: mrp_maintenance
#: model:ir.model.fields,field_description:mrp_maintenance.field_maintenance_request__maintenance_for
msgid "For"
msgstr ""

#. module: mrp_maintenance
#: model:maintenance.equipment.category,name:mrp_maintenance.equipment_furniture_tools
msgid "Furniture Tools"
msgstr ""

#. module: mrp_maintenance
#: model:ir.model.fields,field_description:mrp_maintenance.field_mrp_workcenter__has_message
msgid "Has Message"
msgstr "Ima sporočilo"

#. module: mrp_maintenance
#: model:ir.model.fields,field_description:mrp_maintenance.field_mrp_workcenter__activity_exception_icon
msgid "Icon"
msgstr "Ikona"

#. module: mrp_maintenance
#: model:ir.model.fields,help:mrp_maintenance.field_mrp_workcenter__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "Ikona za označevanje izjemne aktivnosti."

#. module: mrp_maintenance
#: model:ir.model.fields,help:mrp_maintenance.field_mrp_workcenter__message_needaction
msgid "If checked, new messages require your attention."
msgstr "Če je označeno, zahtevajo nova sporočila vašo pozornost."

#. module: mrp_maintenance
#: model:ir.model.fields,help:mrp_maintenance.field_mrp_workcenter__message_has_error
#: model:ir.model.fields,help:mrp_maintenance.field_mrp_workcenter__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "Če je označeno, nekatera sporočila vsebujejo napako pri dostavi."

#. module: mrp_maintenance
#: model:ir.model.fields,field_description:mrp_maintenance.field_mrp_workcenter__message_is_follower
msgid "Is Follower"
msgstr "Je sledilec"

#. module: mrp_maintenance
#: model:ir.model.fields,help:mrp_maintenance.field_maintenance_request__block_workcenter
msgid ""
"It won't be possible to plan work orders or other maintenances on this "
"workcenter during this time."
msgstr ""

#. module: mrp_maintenance
#: model_terms:ir.ui.view,arch_db:mrp_maintenance.mrp_workcenter_view_form_inherit_maintenance
msgid "Latest Failure"
msgstr ""

#. module: mrp_maintenance
#: model:ir.model.fields,field_description:mrp_maintenance.field_mrp_workcenter__latest_failure_date
msgid "Latest Failure Date"
msgstr ""

#. module: mrp_maintenance
#: model:ir.model.fields,field_description:mrp_maintenance.field_maintenance_request__leave_ids
msgid "Leaves"
msgstr ""

#. module: mrp_maintenance
#: model:ir.model.fields,field_description:mrp_maintenance.field_mrp_workcenter__mtbf
msgid "MTBF"
msgstr ""

#. module: mrp_maintenance
#: model:ir.model.fields,field_description:mrp_maintenance.field_mrp_workcenter__mttr
msgid "MTTR"
msgstr ""

#. module: mrp_maintenance
#: model:ir.ui.menu,name:mrp_maintenance.menu_equipment_dashboard
msgid "Machines & Tools"
msgstr ""

#. module: mrp_maintenance
#: model:ir.model.fields,field_description:mrp_maintenance.field_mrp_workcenter__maintenance_ids
#: model_terms:ir.ui.view,arch_db:mrp_maintenance.mrp_workcenter_view_form_inherit_maintenance
#: model_terms:ir.ui.view,arch_db:mrp_maintenance.mrp_workcenter_view_kanban_inherit_maintenance
msgid "Maintenance"
msgstr "Vzdrževanje"

#. module: mrp_maintenance
#: model:ir.model.fields,field_description:mrp_maintenance.field_mrp_workcenter__maintenance_count
msgid "Maintenance Count"
msgstr ""

#. module: mrp_maintenance
#: model:ir.model,name:mrp_maintenance.model_maintenance_equipment
#: model:ir.model.fields,field_description:mrp_maintenance.field_mrp_workcenter__equipment_ids
msgid "Maintenance Equipment"
msgstr ""

#. module: mrp_maintenance
#: model:ir.model,name:mrp_maintenance.model_maintenance_request
#: model_terms:ir.ui.view,arch_db:mrp_maintenance.mrp_production_view_form_inherit_maintenance
msgid "Maintenance Request"
msgstr "Vzdrževalni zahtevek"

#. module: mrp_maintenance
#. odoo-python
#: code:addons/mrp_maintenance/models/mrp_maintenance.py:0
#: model:ir.actions.act_window,name:mrp_maintenance.mrp_workcenter_request_action_from_workcenter
#, python-format
msgid "Maintenance Requests"
msgstr "Vzdrževalni zahtevki"

#. module: mrp_maintenance
#: model:ir.model,name:mrp_maintenance.model_maintenance_stage
msgid "Maintenance Stage"
msgstr "Stopnja vzdrževanja"

#. module: mrp_maintenance
#: model:ir.model.fields,field_description:mrp_maintenance.field_mrp_workcenter__maintenance_team_id
msgid "Maintenance Team"
msgstr "Vzdrževalna ekipa"

#. module: mrp_maintenance
#: model:ir.model.fields,field_description:mrp_maintenance.field_maintenance_request__production_id
msgid "Manufacturing Order"
msgstr "Proizvodni nalog"

#. module: mrp_maintenance
#: model_terms:ir.actions.act_window,help:mrp_maintenance.maintenance_workcenter_action
msgid ""
"Manufacturing operations are processed at Work Centers. A Work Center can be composed of\n"
"            workers and/or machines, they are used for costing, scheduling, capacity planning, etc."
msgstr ""

#. module: mrp_maintenance
#: model_terms:ir.ui.view,arch_db:mrp_maintenance.mrp_workcenter_view_form_inherit_maintenance
msgid "Mean Time Between Failure"
msgstr ""

#. module: mrp_maintenance
#: model:ir.model.fields,help:mrp_maintenance.field_mrp_workcenter__mtbf
msgid ""
"Mean Time Between Failure, computed based on done corrective maintenances."
msgstr ""

#. module: mrp_maintenance
#: model:ir.model.fields,help:mrp_maintenance.field_mrp_workcenter__mttr
#: model_terms:ir.ui.view,arch_db:mrp_maintenance.mrp_workcenter_view_form_inherit_maintenance
msgid "Mean Time To Repair"
msgstr ""

#. module: mrp_maintenance
#: model:ir.model.fields,field_description:mrp_maintenance.field_mrp_workcenter__message_has_error
msgid "Message Delivery error"
msgstr "Napaka pri dostavi sporočila"

#. module: mrp_maintenance
#: model:ir.model.fields,field_description:mrp_maintenance.field_mrp_workcenter__message_ids
msgid "Messages"
msgstr "Sporočila"

#. module: mrp_maintenance
#: model:ir.model.fields,field_description:mrp_maintenance.field_mrp_workcenter__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "Rok dejavnosti"

#. module: mrp_maintenance
#. odoo-python
#: code:addons/mrp_maintenance/models/mrp_maintenance.py:0
#: code:addons/mrp_maintenance/models/mrp_maintenance.py:0
#, python-format
msgid "New Maintenance Request"
msgstr ""

#. module: mrp_maintenance
#: model:ir.model.fields,field_description:mrp_maintenance.field_mrp_workcenter__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr "Naslednji dogodek v koledarju aktivnosti"

#. module: mrp_maintenance
#: model:ir.model.fields,field_description:mrp_maintenance.field_mrp_workcenter__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "Rok naslednje aktivnosti"

#. module: mrp_maintenance
#: model:ir.model.fields,field_description:mrp_maintenance.field_mrp_workcenter__activity_summary
msgid "Next Activity Summary"
msgstr "Povzetek naslednje aktivnosti"

#. module: mrp_maintenance
#: model:ir.model.fields,field_description:mrp_maintenance.field_mrp_workcenter__activity_type_id
msgid "Next Activity Type"
msgstr "Tip naslednje aktivnosti"

#. module: mrp_maintenance
#: model:ir.model.fields,field_description:mrp_maintenance.field_mrp_workcenter__message_needaction_counter
msgid "Number of Actions"
msgstr "Število aktivnosti"

#. module: mrp_maintenance
#: model:ir.model.fields,field_description:mrp_maintenance.field_mrp_workcenter__message_has_error_counter
msgid "Number of errors"
msgstr "Število napak"

#. module: mrp_maintenance
#: model:ir.model.fields,field_description:mrp_maintenance.field_mrp_production__maintenance_count
msgid "Number of maintenance requests"
msgstr ""

#. module: mrp_maintenance
#: model:ir.model.fields,help:mrp_maintenance.field_mrp_workcenter__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "Število sporočil, ki zahtevajo ukrepanje"

#. module: mrp_maintenance
#: model:ir.model.fields,help:mrp_maintenance.field_mrp_workcenter__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Število sporočil, ki niso bila pravilno dostavljena."

#. module: mrp_maintenance
#: model_terms:ir.ui.view,arch_db:mrp_maintenance.maintenance_request_view_search_inherit_mrp
msgid "Operation"
msgstr "Postopek"

#. module: mrp_maintenance
#: model:ir.model.fields,field_description:mrp_maintenance.field_maintenance_request__production_company_id
msgid "Production Company"
msgstr ""

#. module: mrp_maintenance
#: model:ir.model,name:mrp_maintenance.model_mrp_production
msgid "Production Order"
msgstr "Proizvodni nalog"

#. module: mrp_maintenance
#: model:ir.model.fields,field_description:mrp_maintenance.field_mrp_workcenter__rating_ids
msgid "Ratings"
msgstr "Ocene"

#. module: mrp_maintenance
#: model:ir.model.fields,field_description:mrp_maintenance.field_mrp_production__request_ids
#: model_terms:ir.ui.view,arch_db:mrp_maintenance.maintenance_workcenter_view_kanban_inherit_mrp
msgid "Request"
msgstr "Zahteva"

#. module: mrp_maintenance
#: model:ir.model.fields,field_description:mrp_maintenance.field_maintenance_stage__create_leaves
msgid "Request Confirmed"
msgstr ""

#. module: mrp_maintenance
#. odoo-javascript
#: code:addons/mrp_maintenance/static/src/components/menuPopup.xml:0
#, python-format
msgid "Request Maintenance"
msgstr ""

#. module: mrp_maintenance
#. odoo-python
#: code:addons/mrp_maintenance/models/mrp_maintenance.py:0
#, python-format
msgid "Request planned for %s"
msgstr ""

#. module: mrp_maintenance
#: model:ir.model.fields,field_description:mrp_maintenance.field_mrp_workcenter__activity_user_id
msgid "Responsible User"
msgstr "Odgovorni uporabnik"

#. module: mrp_maintenance
#: model:ir.model.fields,field_description:mrp_maintenance.field_mrp_workcenter__message_has_sms_error
msgid "SMS Delivery error"
msgstr "Napaka pri dostavi SMS "

#. module: mrp_maintenance
#: model_terms:ir.ui.view,arch_db:mrp_maintenance.maintenance_request_view_form_inherit_mrp
msgid "Save"
msgstr "Shrani"

#. module: mrp_maintenance
#: model:maintenance.equipment,name:mrp_maintenance.equipment_furniture2
msgid "Scrub Plane"
msgstr ""

#. module: mrp_maintenance
#: model:ir.model.fields,help:mrp_maintenance.field_mrp_workcenter__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"Status na osnovi aktivnosti\n"
"Zapadel: Rok je že prekoračen\n"
"Danes: Datum aktivnosti je danes\n"
"Planirano: Bodoče aktivnosti."

#. module: mrp_maintenance
#: model:ir.model.fields,field_description:mrp_maintenance.field_mrp_workcenter__technician_user_id
msgid "Technician"
msgstr "Tehnik"

#. module: mrp_maintenance
#. odoo-javascript
#: code:addons/mrp_maintenance/static/src/components/maintenance_request_form_view.js:0
#, python-format
msgid "The maintenance request has successfully been created."
msgstr ""

#. module: mrp_maintenance
#. odoo-python
#: code:addons/mrp_maintenance/models/mrp_maintenance.py:0
#, python-format
msgid ""
"The schedule has changed from %(desired_date)s to %(effective_date)s due to "
"planned manufacturing orders."
msgstr ""

#. module: mrp_maintenance
#. odoo-python
#: code:addons/mrp_maintenance/models/mrp_maintenance.py:0
#, python-format
msgid "The workcenter is missing for %s."
msgstr ""

#. module: mrp_maintenance
#: model:ir.model.fields,help:mrp_maintenance.field_mrp_workcenter__effective_date
msgid "This date will be used to compute the Mean Time Between Failure."
msgstr ""

#. module: mrp_maintenance
#: model:ir.model.fields,help:mrp_maintenance.field_mrp_workcenter__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "Vrsta dejavnosti izjeme na zapisu. "

#. module: mrp_maintenance
#: model:ir.model.fields,field_description:mrp_maintenance.field_mrp_workcenter__website_message_ids
msgid "Website Messages"
msgstr "Sporočila iz spletne strani"

#. module: mrp_maintenance
#: model:ir.model.fields,help:mrp_maintenance.field_mrp_workcenter__website_message_ids
msgid "Website communication history"
msgstr "Kronologija komunikacij spletne strani"

#. module: mrp_maintenance
#: model:ir.model.fields,help:mrp_maintenance.field_maintenance_stage__create_leaves
msgid ""
"When this box is unticked, and the maintenance is of the type 'Work Center', no leave is created on the respective work center when a maintenance request is created.\n"
"If the box is ticked, the work center is automatically blocked for the listed duration, either at the specified date, or as soon as possible, if the work center is unavailable then."
msgstr ""

#. module: mrp_maintenance
#: model:ir.model,name:mrp_maintenance.model_mrp_workcenter
#: model:ir.model.fields,field_description:mrp_maintenance.field_maintenance_equipment__workcenter_id
#: model:ir.model.fields,field_description:mrp_maintenance.field_maintenance_request__workcenter_id
#: model:ir.model.fields.selection,name:mrp_maintenance.selection__maintenance_request__maintenance_for__workcenter
msgid "Work Center"
msgstr "Delovni center"

#. module: mrp_maintenance
#: model:ir.actions.act_window,name:mrp_maintenance.maintenance_workcenter_action
#: model:ir.ui.menu,name:mrp_maintenance.menu_workcenter_tree
msgid "Work Centers"
msgstr "Delovni centri"

#. module: mrp_maintenance
#: model:ir.model,name:mrp_maintenance.model_mrp_workorder
#: model:ir.model.fields,field_description:mrp_maintenance.field_maintenance_request__workorder_id
msgid "Work Order"
msgstr "Delovni nalog"

#. module: mrp_maintenance
#: model_terms:ir.ui.view,arch_db:mrp_maintenance.maintenance_request_view_search_inherit_mrp
msgid "Workcenter"
msgstr "Delovni center"

#. module: mrp_maintenance
#: model_terms:ir.ui.view,arch_db:mrp_maintenance.mrp_workcenter_view_form_inherit_maintenance
msgid "days"
msgstr "dni"

#. module: mrp_maintenance
#. odoo-python
#: code:addons/mrp_maintenance/models/mrp_maintenance.py:0
#, python-format
msgid "work centers"
msgstr ""
