# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* worksheet
# 
# Translators:
# Wil Odoo, 2023
# <PERSON><PERSON><PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-10-26 22:22+0000\n"
"PO-Revision-Date: 2023-10-26 23:10+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON>, 2024\n"
"Language-Team: Portuguese (Brazil) (https://app.transifex.com/odoo/teams/41243/pt_BR/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: pt_BR\n"
"Plural-Forms: nplurals=3; plural=(n == 0 || n == 1) ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: worksheet
#. odoo-python
#: code:addons/worksheet/models/worksheet_template.py:0
#, python-format
msgid "%s (copy)"
msgstr "%s (cópia)"

#. module: worksheet
#: model_terms:ir.ui.view,arch_db:worksheet.worksheet_template_view_form
msgid "<span class=\"o_stat_text\">Worksheets</span>"
msgstr "<span class=\"o_stat_text\">Planilhas de trabalho</span>"

#. module: worksheet
#: model:ir.model.fields,field_description:worksheet.field_worksheet_template__action_id
msgid "Action"
msgstr "Ação"

#. module: worksheet
#: model:ir.model.fields,field_description:worksheet.field_worksheet_template__active
msgid "Active"
msgstr "Ativo"

#. module: worksheet
#. odoo-python
#: code:addons/worksheet/models/worksheet_template.py:0
#: model_terms:ir.ui.view,arch_db:worksheet.worksheet_template_view_form
#, python-format
msgid "Analysis"
msgstr "Análise"

#. module: worksheet
#: model_terms:ir.ui.view,arch_db:worksheet.worksheet_template_view_form
#: model_terms:ir.ui.view,arch_db:worksheet.worksheet_template_view_search
msgid "Archived"
msgstr "Arquivado"

#. module: worksheet
#: model:ir.model.fields,field_description:worksheet.field_worksheet_template__color
msgid "Color"
msgstr "Cor"

#. module: worksheet
#: model:ir.model.fields,field_description:worksheet.field_worksheet_template__company_ids
msgid "Companies"
msgstr "Empresas"

#. module: worksheet
#: model:ir.model.fields,field_description:worksheet.field_worksheet_template__create_uid
msgid "Created by"
msgstr "Criado por"

#. module: worksheet
#: model:ir.model.fields,field_description:worksheet.field_worksheet_template__create_date
msgid "Created on"
msgstr "Criado em"

#. module: worksheet
#. odoo-javascript
#: code:addons/worksheet/static/src/open_studio_button_widget/open_studio_button_widget.xml:0
#, python-format
msgid "Design Template"
msgstr "Modelo de design"

#. module: worksheet
#: model:ir.model.fields,field_description:worksheet.field_worksheet_template__display_name
msgid "Display Name"
msgstr "Nome exibido"

#. module: worksheet
#: model:ir.model.fields,field_description:worksheet.field_worksheet_template__res_model
msgid "Host Model"
msgstr "Modelo de host"

#. module: worksheet
#: model:ir.model.fields,field_description:worksheet.field_worksheet_template__id
msgid "ID"
msgstr "ID"

#. module: worksheet
#: model:ir.model.fields,field_description:worksheet.field_worksheet_template__write_uid
msgid "Last Updated by"
msgstr "Última atualização por"

#. module: worksheet
#: model:ir.model.fields,field_description:worksheet.field_worksheet_template__write_date
msgid "Last Updated on"
msgstr "Última atualização em"

#. module: worksheet
#: model:ir.model.fields,field_description:worksheet.field_worksheet_template__model_id
msgid "Model"
msgstr "Modelo"

#. module: worksheet
#: model:ir.model.fields,field_description:worksheet.field_worksheet_template__name
msgid "Name"
msgstr "Nome"

#. module: worksheet
#: model:ir.model.fields,field_description:worksheet.field_worksheet_template__report_view_id
msgid "Report View"
msgstr "Visualização de relatório"

#. module: worksheet
#: model:ir.model.fields,field_description:worksheet.field_worksheet_template__sequence
msgid "Sequence"
msgstr "Sequência"

#. module: worksheet
#. odoo-python
#: code:addons/worksheet/models/worksheet_template.py:0
#, python-format
msgid "The host model name should be an existing model."
msgstr "O nome do modelo de host deve ser um modelo existente."

#. module: worksheet
#: model:ir.model.fields,help:worksheet.field_worksheet_template__res_model
msgid "The model that is using this template"
msgstr "O modelo que está usando esse modelo"

#. module: worksheet
#. odoo-python
#: code:addons/worksheet/models/worksheet_template.py:0
#, python-format
msgid ""
"The template to print this worksheet template should be a QWeb template."
msgstr ""
"O modelo para imprimir esse modelo de planilha de trabalho deve ser um "
"modelo QWeb."

#. module: worksheet
#. odoo-python
#: code:addons/worksheet/models/worksheet_template.py:0
#, python-format
msgid ""
"Unfortunately, you cannot unlink this worksheet template from %s because the"
" template is still connected to tasks within the company."
msgstr ""
"Infelizmente, você não pode desvincular este modelo de planilha de trabalho "
"de %s porque o modelo ainda está conectado a tarefas na empresa."

#. module: worksheet
#: model:ir.model.fields,field_description:worksheet.field_worksheet_template__worksheet_count
msgid "Worksheet Count"
msgstr "Total de planilhas"

#. module: worksheet
#: model:ir.model,name:worksheet.model_worksheet_template
msgid "Worksheet Template"
msgstr "Modelo de planilha de trabalho"

#. module: worksheet
#: model_terms:ir.ui.view,arch_db:worksheet.worksheet_template_view_search
msgid "Worksheet Template Search"
msgstr "Busca de modelos de planilha de trabalho"

#. module: worksheet
#: model_terms:ir.ui.view,arch_db:worksheet.worksheet_template_view_list
msgid "Worksheets"
msgstr "Planilhas de trabalho"

#. module: worksheet
#. odoo-python
#: code:addons/worksheet/models/worksheet_template.py:0
#, python-format
msgid ""
"You can't restrict this worksheet template to '%s' because it's still "
"connected to tasks in '%s' (and potentially other companies). Please either "
"unlink those tasks from this worksheet template, move them to a project for "
"the right company, or keep this worksheet template open to all companies."
msgstr ""
"Você não pode restringir esse modelo de planilha a '%s' porque isso ainda "
"está conectado a tarefas em '%s' (e potencialmente em outras empresas). "
"Desvincule essas tarefas do modelo de planilha, mova-as para um projeto da "
"empresa certa ou mantenha o modelo de planilha aberto para todas as "
"empresas."

#. module: worksheet
#: model_terms:ir.ui.view,arch_db:worksheet.worksheet_template_view_form
msgid "e.g Device Installation"
msgstr "Ex.: instalação de dispositivo"
