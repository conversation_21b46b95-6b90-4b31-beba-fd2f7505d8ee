<?xml version="1.0" encoding="UTF-8"?>

<templates xml:space="preserve">
    <t t-name="frontdesk.Navbar">
        <nav class="navbar align-items-start p-0">
            <div class="w-25">
                <a t-if="props.currentComponent === 'HostPage' or !props.isMobile" t-att-class="props.theme === 'light' ? 'bg-200' : 'bg-700'" class="btn btn-sm d-inline-flex align-items-center order-0" href="#" t-on-click="() => props.currentComponent === 'VisitorForm' ? props.showScreen('WelcomePage') : props.showScreen('VisitorForm')">
                    <i class="oi oi-arrow-left me-2"/>Back
                </a>
            </div>
            <div class="me-auto me-sm-0 w-50 px-3 text-center">
                <img t-attf-src="/web/image/res.company/{{ props.companyInfo.id }}/logo" alt="Company Logo" class="img-fluid o_company_logo"/>
            </div>
            <div class="align-self-end mt-3 mt-sm-0 w-100 w-sm-25">
                <select class="form-select form-select-lg ms-auto w-100" t-att-class="props.theme == 'dark' ? 'border-0 bg-800 text-white' : ' bg-white'" t-on-change="(ev) => props.onChangeLang(ev)" t-if="props.langs.length > 1 and props.isMobile and props.currentComponent === 'VisitorForm'">
                    <t t-foreach="props.langs" t-as="lang" t-key="lang.code">
                        <option t-att-value="lang.code" t-att-selected="lang.code === props.currentLang ? true : undefined"><t t-out="lang.name"/></option>
                    </t>
                </select>
            </div>
            <h1 class="flex-fill mb-0 mt-4 fw-light text-center">
                <span t-if="props.currentComponent === 'VisitorForm'">Who are you?</span>
                <span t-if="props.currentComponent === 'HostPage'">Who are you visiting?</span>
            </h1>
        </nav>
    </t>
</templates>
