# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* timer
# 
# Translators:
# <PERSON><PERSON>, 2023
# <PERSON><PERSON> <y.shad<PERSON><PERSON>@gmail.com>, 2023
# <PERSON>, 2023
# <PERSON><PERSON> <ifara<PERSON><EMAIL>>, 2023
# <PERSON>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-10-26 22:20+0000\n"
"PO-Revision-Date: 2023-10-26 23:10+0000\n"
"Last-Translator: <PERSON>, 2023\n"
"Language-Team: Persian (https://app.transifex.com/odoo/teams/41243/fa/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: fa\n"
"Plural-Forms: nplurals=2; plural=(n > 1);\n"

#. module: timer
#: model:ir.model.fields,field_description:timer.field_timer_timer__create_uid
msgid "Created by"
msgstr "ایجاد شده توسط"

#. module: timer
#: model:ir.model.fields,field_description:timer.field_timer_timer__create_date
msgid "Created on"
msgstr "ایجادشده در"

#. module: timer
#: model:ir.model.fields,field_description:timer.field_timer_timer__display_name
msgid "Display Name"
msgstr "نام نمایش داده شده"

#. module: timer
#: model:ir.model.fields,field_description:timer.field_timer_mixin__display_timer_pause
msgid "Display Timer Pause"
msgstr ""

#. module: timer
#: model:ir.model.fields,field_description:timer.field_timer_mixin__display_timer_resume
msgid "Display Timer Resume"
msgstr ""

#. module: timer
#: model:ir.model.fields,field_description:timer.field_timer_mixin__display_timer_start_primary
msgid "Display Timer Start Primary"
msgstr ""

#. module: timer
#: model:ir.model.fields,field_description:timer.field_timer_mixin__display_timer_stop
msgid "Display Timer Stop"
msgstr ""

#. module: timer
#: model:ir.model.fields,field_description:timer.field_timer_timer__id
msgid "ID"
msgstr "شناسه"

#. module: timer
#: model:ir.model.fields,field_description:timer.field_timer_mixin__is_timer_running
#: model:ir.model.fields,field_description:timer.field_timer_timer__is_timer_running
msgid "Is Timer Running"
msgstr ""

#. module: timer
#: model:ir.model.fields,field_description:timer.field_timer_timer__write_uid
msgid "Last Updated by"
msgstr "آخرین بروز رسانی توسط"

#. module: timer
#: model:ir.model.fields,field_description:timer.field_timer_timer__write_date
msgid "Last Updated on"
msgstr "آخرین بروز رسانی در"

#. module: timer
#: model:ir.model.constraint,message:timer.constraint_timer_timer_unique_timer
msgid "Only one timer occurrence by model, record and user"
msgstr ""

#. module: timer
#. odoo-python
#: code:addons/timer/models/timer_mixin.py:0
#, python-format
msgid "Operation not supported"
msgstr "عملیات پشتیبانی نمی شود"

#. module: timer
#: model:ir.model.fields,field_description:timer.field_timer_timer__res_id
msgid "Res"
msgstr ""

#. module: timer
#: model:ir.model.fields,field_description:timer.field_timer_timer__res_model
msgid "Res Model"
msgstr ""

#. module: timer
#. odoo-javascript
#: code:addons/timer/static/src/component/timer_toggle_button/timer_toggle_button.js:0
#, python-format
msgid "Start"
msgstr "شروع"

#. module: timer
#. odoo-javascript
#: code:addons/timer/static/src/component/timer_toggle_button/timer_toggle_button.js:0
#, python-format
msgid "Stop"
msgstr "توقف"

#. module: timer
#: model:ir.model.fields,field_description:timer.field_timer_mixin__timer_pause
#: model:ir.model.fields,field_description:timer.field_timer_timer__timer_pause
msgid "Timer Last Pause"
msgstr ""

#. module: timer
#: model:ir.model,name:timer.model_timer_mixin
msgid "Timer Mixin"
msgstr ""

#. module: timer
#: model:ir.model,name:timer.model_timer_timer
msgid "Timer Module"
msgstr ""

#. module: timer
#: model:ir.model.fields,field_description:timer.field_timer_mixin__timer_start
#: model:ir.model.fields,field_description:timer.field_timer_timer__timer_start
msgid "Timer Start"
msgstr ""

#. module: timer
#: model:ir.model.fields,field_description:timer.field_timer_timer__user_id
msgid "User"
msgstr "کاربر"

#. module: timer
#: model:ir.model.fields,field_description:timer.field_timer_mixin__user_timer_id
msgid "User Timer"
msgstr ""
