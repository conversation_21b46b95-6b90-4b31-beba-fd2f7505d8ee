# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* website_helpdesk_livechat
# 
# Translators:
# <AUTHOR> <EMAIL>, 2023
# <PERSON><PERSON>, 2023
# <PERSON>, 2023
# <PERSON><PERSON> <<EMAIL>>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-10-26 22:20+0000\n"
"PO-Revision-Date: 2023-10-26 23:10+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>, 2024\n"
"Language-Team: Danish (https://app.transifex.com/odoo/teams/41243/da/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: da\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: website_helpdesk_livechat
#. odoo-python
#: code:addons/website_helpdesk_livechat/models/helpdesk.py:0
#, python-format
msgid ""
"\n"
"                    Create a new helpdesk ticket by typing <b>/ticket <i>ticket title</i></b><br>\n"
"                    "
msgstr ""
"\n"
"                    Opret en ny helpdesk opgave ved at skrive <b>/ticket <i>ticket title</i></b><br>\n"
"                    "

#. module: website_helpdesk_livechat
#. odoo-python
#: code:addons/website_helpdesk_livechat/models/chatbot_script_step.py:0
#, python-format
msgid "%(name)s's Ticket"
msgstr "%(name)s's opgave"

#. module: website_helpdesk_livechat
#: model:chatbot.script.step,message:website_helpdesk_livechat.chatbot_script_helpdesk_step_done
msgid "Alright, we should have everything we need"
msgstr "Okay, vi burde have alt, hvad vi har brug for"

#. module: website_helpdesk_livechat
#: model:chatbot.script.step,message:website_helpdesk_livechat.chatbot_script_helpdesk_step_technical_serial_misc
msgid "Anything else to add?"
msgstr "Noget andet at tilføje?"

#. module: website_helpdesk_livechat
#: model:ir.ui.menu,name:website_helpdesk_livechat.helpdesk_team_canned_response_menu
msgid "Canned Responses"
msgstr "Standardsvar"

#. module: website_helpdesk_livechat
#: model:ir.model,name:website_helpdesk_livechat.model_chatbot_script
msgid "Chatbot Script"
msgstr "Chatbot-skript"

#. module: website_helpdesk_livechat
#: model:ir.model,name:website_helpdesk_livechat.model_chatbot_script_step
msgid "Chatbot Script Step"
msgstr "Chatbot Script Step"

#. module: website_helpdesk_livechat
#: model_terms:ir.ui.view,arch_db:website_helpdesk_livechat.helpdesk_team_view_form_inherit_website_helpdesk_livechat
msgid "Configure Canned Responses"
msgstr "Konfigurer automatiske svar"

#. module: website_helpdesk_livechat
#: model_terms:ir.ui.view,arch_db:website_helpdesk_livechat.helpdesk_team_view_form_inherit_website_helpdesk_livechat
msgid "Configure Live Chat Channel"
msgstr "Konfigurer Live Chat Kanal"

#. module: website_helpdesk_livechat
#: model:ir.model.fields.selection,name:website_helpdesk_livechat.selection__chatbot_script_step__step_type__create_ticket
msgid "Create Ticket"
msgstr "Opret sag"

#. module: website_helpdesk_livechat
#. odoo-javascript
#: code:addons/website_helpdesk_livechat/static/src/messaging_service_patch.js:0
#, python-format
msgid "Create a new helpdesk ticket (/ticket ticket title)"
msgstr "Opret en ny helpdesk opgave (/ticket ticket title)"

#. module: website_helpdesk_livechat
#. odoo-python
#: code:addons/website_helpdesk_livechat/models/helpdesk.py:0
#, python-format
msgid "Created a new ticket: %s"
msgstr "Oprettet en ny opgave: %s"

#. module: website_helpdesk_livechat
#: model:ir.model,name:website_helpdesk_livechat.model_discuss_channel
msgid "Discussion Channel"
msgstr "Diskussionskanal"

#. module: website_helpdesk_livechat
#: model:chatbot.script.step,message:website_helpdesk_livechat.chatbot_script_helpdesk_step_dispatch
msgid "First, what is the nature of your issue?"
msgstr ""

#. module: website_helpdesk_livechat
#: model:ir.model.fields,field_description:website_helpdesk_livechat.field_chatbot_script__ticket_count
msgid "Generated Ticket Count"
msgstr "Genereret opgave antal"

#. module: website_helpdesk_livechat
#: model:chatbot.script.step,message:website_helpdesk_livechat.chatbot_script_helpdesk_step_administrative_customer_ref_yes
msgid "Great, that will make our lives easier."
msgstr "Fantastisk, det vil gøre vores liv lettere."

#. module: website_helpdesk_livechat
#: model:chatbot.script,title:website_helpdesk_livechat.chatbot_script_helpdesk_bot
msgid "Helpdesk Bot"
msgstr "Helpdesk Bot"

#. module: website_helpdesk_livechat
#: model:ir.model,name:website_helpdesk_livechat.model_helpdesk_team
#: model:ir.model.fields,field_description:website_helpdesk_livechat.field_chatbot_script_step__helpdesk_team_id
msgid "Helpdesk Team"
msgstr "Helpdesk team"

#. module: website_helpdesk_livechat
#: model:chatbot.script.step,message:website_helpdesk_livechat.chatbot_script_helpdesk_step_welcome
msgid "Here we go, help is on the way!"
msgstr "Så er vi klar, hjælpen er på vej!"

#. module: website_helpdesk_livechat
#: model:chatbot.script.answer,name:website_helpdesk_livechat.chatbot_script_helpdesk_step_dispatch_answer_technical
msgid "I have a technical issue"
msgstr "Jeg har en teknisk udfordring"

#. module: website_helpdesk_livechat
#: model:chatbot.script.answer,name:website_helpdesk_livechat.chatbot_script_helpdesk_step_dispatch_answer_administrative
msgid "I have an administrative question"
msgstr "Jeg har et administrativt spørgsmål"

#. module: website_helpdesk_livechat
#: model:chatbot.script.step,message:website_helpdesk_livechat.chatbot_script_helpdesk_step_administrative_customer_ref_no
msgid "It's OK, we can also find your contract by other means."
msgstr "Det er i orden, vi kan også finde din kontrakt på andre måder."

#. module: website_helpdesk_livechat
#: model:chatbot.script.step,message:website_helpdesk_livechat.chatbot_script_helpdesk_step_email
msgid "Just a last thing, can we please have your email address?"
msgstr "Bare en sidste ting, kan vi venligst få din email adresse?"

#. module: website_helpdesk_livechat
#: model:ir.model.fields,field_description:website_helpdesk_livechat.field_helpdesk_team__use_website_helpdesk_livechat
msgid "Live Chat"
msgstr "Live Chat"

#. module: website_helpdesk_livechat
#. odoo-python
#: code:addons/website_helpdesk_livechat/models/helpdesk.py:0
#, python-format
msgid "Make sure you are using the right format:"
msgstr "Sørg for, at du bruger det rigtige format:"

#. module: website_helpdesk_livechat
#: model:chatbot.script.answer,name:website_helpdesk_livechat.chatbot_script_helpdesk_step_administrative_customer_ref_answer_no
msgid "No"
msgstr "Nej"

#. module: website_helpdesk_livechat
#. odoo-python
#: code:addons/website_helpdesk_livechat/models/helpdesk.py:0
#, python-format
msgid "No tickets found for %s."
msgstr "Ingen opgaver fundet til %s."

#. module: website_helpdesk_livechat
#. odoo-python
#: code:addons/website_helpdesk_livechat/models/helpdesk.py:0
#, python-format
msgid ""
"No tickets found for <b>%s</b>. <br> Make sure you are using the right "
"format:<br> <b>/search_tickets <i>keyword</i></b>"
msgstr ""
"Ingen opgaver fundet til <b>%s</b>. <br> Sørg for, at du bruger det rigtige "
"format:<br> <b>/search_tickets <i>keyword</i></b>"

#. module: website_helpdesk_livechat
#: model:chatbot.script.step,message:website_helpdesk_livechat.chatbot_script_helpdesk_step_ticket
msgid ""
"OK, I just created a ticket for you. You should receive an email "
"confirmation very soon."
msgstr ""
"OK, jeg har lige oprettet en opgave til dig. Du burde snart modtage en "
"emailbekræftelse."

#. module: website_helpdesk_livechat
#: model:chatbot.script.step,message:website_helpdesk_livechat.chatbot_script_helpdesk_step_technical_serial
msgid "Please write below the serial number of your equipment."
msgstr "Skriv venligst serienummeret på dit udstyr nedenfor."

#. module: website_helpdesk_livechat
#: model:chatbot.script.step,message:website_helpdesk_livechat.chatbot_script_helpdesk_step_administrative_customer_ref_input
msgid "Please write below your customer reference."
msgstr "Skriv venligst din kunde reference nedenfor."

#. module: website_helpdesk_livechat
#. odoo-javascript
#: code:addons/website_helpdesk_livechat/static/src/messaging_service_patch.js:0
#, python-format
msgid "Search helpdesk tickets (/search_tickets keyword)"
msgstr "Søg helpdesk opgaver (/search_tickets keyword)"

#. module: website_helpdesk_livechat
#. odoo-python
#: code:addons/website_helpdesk_livechat/models/helpdesk.py:0
#, python-format
msgid ""
"Search helpdesk tickets by typing <b>/search_tickets <i>keyword</i></b>"
msgstr ""
"Søg helpdesk opgave ved at skrive <b>/search_tickets <i>keyword</i></b>"

#. module: website_helpdesk_livechat
#. odoo-python
#: code:addons/website_helpdesk_livechat/models/helpdesk.py:0
#: code:addons/website_helpdesk_livechat/models/helpdesk.py:0
#, python-format
msgid "Something is missing or wrong in command"
msgstr "Noget mangler eller er forkert i kommando"

#. module: website_helpdesk_livechat
#: model:ir.model.fields,field_description:website_helpdesk_livechat.field_chatbot_script_step__step_type
msgid "Step Type"
msgstr "Trin type"

#. module: website_helpdesk_livechat
#: model:chatbot.script.step,message:website_helpdesk_livechat.chatbot_script_helpdesk_step_technical_serial_thanks
msgid "Thank you, that will help our engineers see what went wrong."
msgstr "Tak, det vil hjælpe vores ingeniører med at se, hvad der gik galt."

#. module: website_helpdesk_livechat
#: model_terms:ir.ui.view,arch_db:website_helpdesk_livechat.chatbot_script_view_form
msgid "Tickets"
msgstr "Supportsager"

#. module: website_helpdesk_livechat
#. odoo-python
#: code:addons/website_helpdesk_livechat/models/helpdesk.py:0
#, python-format
msgid "Tickets search results for %s: "
msgstr "Søgeresultater for opgaver %s:"

#. module: website_helpdesk_livechat
#: model:chatbot.script.step,message:website_helpdesk_livechat.chatbot_script_helpdesk_step_administrative_customer_ref
msgid ""
"To start with, do you have a customer reference?\n"
"They are written on each invoice you received, next to your name."
msgstr ""
"For at begynde, har du en kundereference? \n"
"Den står skrevet på hver faktura, du har modtaget, ved siden af dit navn."

#. module: website_helpdesk_livechat
#: model:ir.model,name:website_helpdesk_livechat.model_res_users
msgid "User"
msgstr "Bruger"

#. module: website_helpdesk_livechat
#: model:chatbot.script.step,message:website_helpdesk_livechat.chatbot_script_helpdesk_step_administrative_issue
msgid "We're all set. Now, what is your issue?"
msgstr "Så er vi helt klar. Hvad er dit problem?"

#. module: website_helpdesk_livechat
#: model:chatbot.script.answer,name:website_helpdesk_livechat.chatbot_script_helpdesk_step_administrative_customer_ref_answer_yes
msgid "Yes"
msgstr "Ja"

#. module: website_helpdesk_livechat
#. odoo-python
#: code:addons/website_helpdesk_livechat/models/helpdesk.py:0
#, python-format
msgid "keyword"
msgstr ""
