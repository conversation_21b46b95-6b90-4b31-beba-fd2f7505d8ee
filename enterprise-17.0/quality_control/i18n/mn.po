# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* quality_control
#
# Translators:
# hish, 2022
# <AUTHOR> <EMAIL>, 2022
# <AUTHOR> <EMAIL>, 2022
# t<PERSON><PERSON><PERSON><PERSON> tsogtoo <<EMAIL>>, 2022
# Bayarkhuu Bataa, 2022
# Batmunk<PERSON>anbat <<EMAIL>>, 2022
# <AUTHOR> <EMAIL>, 2022
# <PERSON>, 2022
# <PERSON>sk<PERSON><PERSON> <<EMAIL>>, 2022
# <AUTHOR> <EMAIL>, 2022
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-05-16 16:01+0000\n"
"PO-Revision-Date: 2022-09-22 05:48+0000\n"
"Last-Translator: Батмөнх Ганбат <<EMAIL>>, 2022\n"
"Language-Team: Mongolian (https://app.transifex.com/odoo/teams/41243/mn/)\n"
"Language: mn\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.quality_point_view_form_inherit_quality_control
msgid "% of Operations"
msgstr ""

#. module: quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.quality_point_view_form_inherit_quality_control
msgid "% of Transfers"
msgstr ""

#. module: quality_control
#: model:ir.actions.report,print_report_name:quality_control.quality_check_report
#: model:ir.actions.report,print_report_name:quality_control.quality_check_report_internal
msgid "'Worksheet_%s' % object.name"
msgstr ""

#. module: quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.quality_alert_team_dashboard_view_kanban
msgid "<i class=\"fa fa-envelope-o\" role=\"img\" aria-label=\"Domain alias\" title=\"Domain alias\"/>&amp;nbsp;"
msgstr ""

#. module: quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.quality_point_view_form_inherit_quality_control
msgid "<span attrs=\"{'invisible': [('measure_frequency_type', '=', 'all')]}\">Every </span>"
msgstr "<span attrs=\"{'invisible': [('measure_frequency_type', '=', 'all')]}\">Бүх </span>"

#. module: quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.quality_point_view_form_inherit_quality_control
msgid "<span class=\"fa fa-2x\" data-icon=\"∑\" style=\"padding-left: 10px;\" role=\"img\" aria-label=\"Statistics\" title=\"Statistics\"/>"
msgstr ""

#. module: quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.stock_picking_view_form_inherit_quality
msgid "<span class=\"o_stat_text text-danger\">Quality Checks</span>"
msgstr ""

#. module: quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.stock_picking_view_form_inherit_quality
msgid "<span class=\"o_stat_text text-success\">Quality Checks</span>"
msgstr ""

#. module: quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.quality_point_view_form_inherit_quality_control
msgid ""
"<span class=\"o_stat_text\">AVG:</span>\n"
"                        <span class=\"o_stat_text\">STD:</span>"
msgstr ""

#. module: quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.product_product_form_view_quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.product_template_form_view_quality_control
msgid ""
"<span class=\"o_stat_text\">Pass:</span>\n"
"                        <span class=\"o_stat_text\">Fail:</span>"
msgstr ""

#. module: quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.stock_picking_view_form_inherit_quality
msgid "<span class=\"o_stat_text\">Quality Alert</span>"
msgstr "<span class=\"o_stat_text\">Чанарын анхааруулга</span>"

#. module: quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.quality_alert_view_form
msgid "<span class=\"o_stat_text\">Quality Check</span>"
msgstr "<span class=\"o_stat_text\">Чанарын шалгалт</span>"

#. module: quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.stock_picking_view_form_inherit_quality
msgid "<span class=\"o_stat_text\">Quality Checks</span>"
msgstr ""

#. module: quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.quality_point_view_form_inherit_quality_control
msgid "<span>from </span>"
msgstr "<span>хэнээс </span>"

#. module: quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.quality_point_view_form_inherit_quality_control
msgid "<span>to </span>"
msgstr "<span>хэн рүү </span>"

#. module: quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.worksheet_page
msgid "<strong>Lot/Serial Number: </strong>"
msgstr ""

#. module: quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.worksheet_page
msgid "<strong>Measure: </strong>"
msgstr ""

#. module: quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.worksheet_page
msgid "<strong>Notes: </strong>"
msgstr ""

#. module: quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.view_quality_point_kanban
msgid "<strong>Operations:</strong>"
msgstr ""

#. module: quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.worksheet_page
msgid "<strong>Product: </strong>"
msgstr ""

#. module: quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.view_quality_point_kanban
msgid "<strong>Products:</strong>"
msgstr ""

#. module: quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.worksheet_page
msgid "<strong>Test Type: </strong>"
msgstr ""

#. module: quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.worksheet_page
msgid "<strong>Tested by: </strong>"
msgstr ""

#. module: quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.worksheet_page
msgid "<strong>Tested on: </strong>"
msgstr ""

#. module: quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.worksheet_page
msgid "<strong>Transfer: </strong>"
msgstr ""

#. module: quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.worksheet_page
msgid "<strong>Warning: </strong>"
msgstr ""

#. module: quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.quality_alert_team_view_form
msgid "Accept Emails From"
msgstr "Имэйл хүлээн авах хаягууд"

#. module: quality_control
#: model_terms:ir.actions.act_window,help:quality_control.quality_tag_action
msgid "Add a new tag"
msgstr "Шинэ пайз нэмэх"

#. module: quality_control
#: model:ir.model.fields,field_description:quality_control.field_quality_check_wizard__additional_note
msgid "Additional Note"
msgstr "Нэмэлт тэмдэглэл"

#. module: quality_control
#: model:ir.model.fields,help:quality_control.field_quality_check_wizard__additional_note
msgid "Additional remarks concerning this check."
msgstr ""

#. module: quality_control
#: model:ir.model.fields,field_description:quality_control.field_stock_picking__quality_alert_ids
#: model_terms:ir.ui.view,arch_db:quality_control.quality_check_view_form
msgid "Alerts"
msgstr "Анхааруулгууд"

#. module: quality_control
#: model:ir.model.fields.selection,name:quality_control.selection__quality_point__measure_frequency_type__all
msgid "All"
msgstr "Бүх"

#. module: quality_control
#: model:ir.model.fields.selection,name:quality_control.selection__stock_move_line__check_state__pass
msgid "All checks passed"
msgstr ""

#. module: quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.quality_point_view_search
msgid "Archived"
msgstr "Архивласан"

#. module: quality_control
#: model:ir.model.fields,field_description:quality_control.field_quality_point__average
msgid "Average"
msgstr "Дундаж"

#. module: quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.view_quality_check_wizard
msgid "Cancel"
msgstr "Цуцлах"

#. module: quality_control
#: model:ir.model.fields,field_description:quality_control.field_quality_check_wizard__check_ids
msgid "Check"
msgstr "Шалгах"

#. module: quality_control
#: model:ir.model.fields,field_description:quality_control.field_stock_move_line__check_state
msgid "Check State"
msgstr ""

#. module: quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.quality_check_view_tree
msgid "Checked By"
msgstr "Шалгасан"

#. module: quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.quality_check_view_search
#: model_terms:ir.ui.view,arch_db:quality_control.quality_check_view_tree
msgid "Checked Date"
msgstr "Шалгасан огноо"

#. module: quality_control
#: model:ir.model.fields,field_description:quality_control.field_stock_move_line__check_ids
#: model:ir.model.fields,field_description:quality_control.field_stock_picking__check_ids
msgid "Checks"
msgstr "Чекүүд"

#. module: quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.quality_alert_team_dashboard_view_kanban
msgid "Checks In Progress"
msgstr "Боловсруулагдаж буй Шалгалтууд"

#. module: quality_control
#: model:ir.ui.menu,name:quality_control.menu_quality_configuration
#: model_terms:ir.ui.view,arch_db:quality_control.quality_alert_team_dashboard_view_kanban
msgid "Configuration"
msgstr "Тохиргоо"

#. module: quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.quality_check_wizard_form_failure
msgid "Confirm Measure"
msgstr ""

#. module: quality_control
#: model:ir.model.fields,field_description:quality_control.field_quality_point__measure_frequency_type
#: model_terms:ir.ui.view,arch_db:quality_control.quality_point_view_form_inherit_quality_control
msgid "Control Frequency"
msgstr "Хяналтын давтамж"

#. module: quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.quality_check_view_form
msgid "Control Person"
msgstr "Хянагч"

#. module: quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.quality_check_view_search
#: model_terms:ir.ui.view,arch_db:quality_control.quality_point_view_search
msgid "Control Point"
msgstr "Хяналтын оноо"

#. module: quality_control
#: model:ir.ui.menu,name:quality_control.menu_quality_control_points
msgid "Control Points"
msgstr "Хяналтын оноонууд"

#. module: quality_control
#: model:ir.model.fields,field_description:quality_control.field_quality_check__measure_on
#: model:ir.model.fields,field_description:quality_control.field_quality_check_wizard__measure_on
#: model:ir.model.fields,field_description:quality_control.field_quality_point__measure_on
msgid "Control per"
msgstr ""

#. module: quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.quality_check_wizard_form_failure
msgid "Correct Measure"
msgstr ""

#. module: quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.quality_alert_view_form
msgid "Corrective Actions"
msgstr "Залруулах үйлдлүүд"

#. module: quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.quality_check_view_form
msgid "Create Alert"
msgstr "Анхааруулга үүсгэх"

#. module: quality_control
#: model_terms:ir.actions.act_window,help:quality_control.quality_alert_action_check
msgid "Create a new quality alert"
msgstr ""

#. module: quality_control
#: model_terms:ir.actions.act_window,help:quality_control.quality_alert_stage_action
msgid "Create a new quality alert stage"
msgstr ""

#. module: quality_control
#: model:ir.model.fields,field_description:quality_control.field_quality_check_wizard__create_uid
msgid "Created by"
msgstr "Үүсгэсэн этгээд"

#. module: quality_control
#: model:ir.model.fields,field_description:quality_control.field_quality_check_wizard__create_date
msgid "Created on"
msgstr "Үүсгэсэн огноо"

#. module: quality_control
#: model:ir.model.fields,field_description:quality_control.field_quality_check_wizard__current_check_id
msgid "Current Check"
msgstr ""

#. module: quality_control
#: model:ir.model.fields.selection,name:quality_control.selection__quality_point__measure_frequency_unit__day
msgid "Days"
msgstr "Өдөр"

#. module: quality_control
#: model:ir.model.fields,help:quality_control.field_quality_check__uom_id
#: model:ir.model.fields,help:quality_control.field_quality_check_wizard__uom_id
msgid "Default unit of measure used for all stock operations."
msgstr "Агуулахын үйл ажиллагаанд ашиглагдах үндсэн хэмжих нэгж"

#. module: quality_control
#: model_terms:ir.actions.act_window,help:quality_control.quality_check_action_main
#: model_terms:ir.actions.act_window,help:quality_control.quality_check_action_team
msgid ""
"Define Quality Control Points in order to automatically generate\n"
"              quality checks at the right logistic operation: transfers, manufacturing orders."
msgstr ""

#. module: quality_control
#: model:ir.model.fields,help:quality_control.field_quality_check__testing_percentage_within_lot
#: model:ir.model.fields,help:quality_control.field_quality_check_wizard__testing_percentage_within_lot
#: model:ir.model.fields,help:quality_control.field_quality_point__testing_percentage_within_lot
msgid "Defines the percentage within a lot that should be tested"
msgstr ""

#. module: quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.quality_alert_view_form
msgid "Describe the corrective actions you did..."
msgstr "Өөрийн хийсэн залруулах үйлдлүүдийг тайлбарлана уу..."

#. module: quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.quality_alert_view_form
msgid "Describe the preventive actions you did..."
msgstr "Өөрийн хийсэн сэргийлэх үйлдлүүдийг тайлбарлана уу..."

#. module: quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.quality_alert_view_form
msgid "Description"
msgstr "Тайлбар"

#. module: quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.quality_alert_view_form
msgid "Description of the issue..."
msgstr "Асуудлын тодорхойлолт..."

#. module: quality_control
#: model:ir.model.fields,help:quality_control.field_quality_check__is_lot_tested_fractionally
#: model:ir.model.fields,help:quality_control.field_quality_check_wizard__is_lot_tested_fractionally
#: model:ir.model.fields,help:quality_control.field_quality_point__is_lot_tested_fractionally
msgid "Determines if only a fraction of the lot should be tested"
msgstr ""

#. module: quality_control
#: model:ir.model.fields,field_description:quality_control.field_quality_check_wizard__display_name
msgid "Display Name"
msgstr "Дэлгэрэнгүй нэр"

#. module: quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.quality_alert_team_view_form
msgid "Email Alias"
msgstr "Ерөнхий имэйл"

#. module: quality_control
#: model:ir.model.fields,help:quality_control.field_quality_check__product_tracking
#: model:ir.model.fields,help:quality_control.field_quality_check_wizard__product_tracking
msgid "Ensure the traceability of a storable product in your warehouse."
msgstr "Агуулахын нөөцөнд байгаа барааг мөшгин шалгах боломж"

#. module: quality_control
#: model:ir.model.fields.selection,name:quality_control.selection__quality_check__measure_success__fail
#: model_terms:ir.ui.view,arch_db:quality_control.quality_check_view_form
#: model_terms:ir.ui.view,arch_db:quality_control.view_quality_check_wizard
msgid "Fail"
msgstr "Амжилтгүй"

#. module: quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.quality_check_view_search
msgid "Failed"
msgstr "Амжилтгүй"

#. module: quality_control
#: model:ir.model.fields,field_description:quality_control.field_quality_check__failure_message
#: model:ir.model.fields,field_description:quality_control.field_quality_check_wizard__failure_message
#: model:ir.model.fields,field_description:quality_control.field_quality_point__failure_message
msgid "Failure Message"
msgstr ""

#. module: quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.quality_point_view_form_inherit_quality_control
msgid "Frequency"
msgstr "Давтамж"

#. module: quality_control
#: model:ir.model.fields,field_description:quality_control.field_quality_point__measure_frequency_unit_value
msgid "Frequency Unit Value"
msgstr ""

#. module: quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.quality_check_view_search
#: model_terms:ir.ui.view,arch_db:quality_control.quality_point_view_search
msgid "Group By"
msgstr "Бүлэглэлт"

#. module: quality_control
#: model:ir.model.fields,field_description:quality_control.field_quality_check_wizard__id
msgid "ID"
msgstr "ID"

#. module: quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.quality_alert_team_dashboard_view_kanban
msgid "In #{kanban_getcolorname(record.color.raw_value)}"
msgstr "#{kanban_getcolorname(record.color.raw_value)} дотор"

#. module: quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.quality_check_view_search
msgid "In Progress"
msgstr "Явагдаж буй"

#. module: quality_control
#: model:ir.model.fields,help:quality_control.field_quality_check__move_line_id
msgid "In case of Quality Check by Quantity, Move Line on which the Quality Check applies"
msgstr ""

#. module: quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.quality_check_view_form
msgid "Instructions"
msgstr ""

#. module: quality_control
#: model:ir.model.fields,field_description:quality_control.field_quality_check_wizard__is_last_check
msgid "Is Last Check"
msgstr ""

#. module: quality_control
#: model:ir.model.fields,field_description:quality_control.field_quality_check_wizard__write_uid
msgid "Last Updated by"
msgstr "Сүүлд зассан этгээд"

#. module: quality_control
#: model:ir.model.fields,field_description:quality_control.field_quality_check_wizard__write_date
msgid "Last Updated on"
msgstr "Сүүлд зассан огноо"

#. module: quality_control
#: model:ir.model.fields,field_description:quality_control.field_quality_check__lot_line_id
#: model:ir.model.fields,field_description:quality_control.field_quality_check_wizard__lot_line_id
msgid "Lot Line"
msgstr ""

#. module: quality_control
#: model:ir.model.fields,field_description:quality_control.field_quality_check__is_lot_tested_fractionally
#: model:ir.model.fields,field_description:quality_control.field_quality_check_wizard__is_lot_tested_fractionally
#: model:ir.model.fields,field_description:quality_control.field_quality_point__is_lot_tested_fractionally
msgid "Lot Tested Fractionally"
msgstr ""

#. module: quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.view_quality_check_wizard
msgid "Lot/SN"
msgstr ""

#. module: quality_control
#: model:ir.model,name:quality_control.model_stock_lot
msgid "Lot/Serial"
msgstr "Серийн дугаар"

#. module: quality_control
#: model:ir.model.fields,field_description:quality_control.field_quality_check__lot_name
#: model:ir.model.fields,field_description:quality_control.field_quality_check_wizard__lot_name
msgid "Lot/Serial Number Name"
msgstr "Серийн дугаарын нэр"

#. module: quality_control
#: model:ir.ui.menu,name:quality_control.quality_control_menu_traceability
msgid "Lots/Serial Numbers"
msgstr "Серийн дугаарууд"

#. module: quality_control
#: model:ir.model.fields,field_description:quality_control.field_quality_check__tolerance_max
#: model:ir.model.fields,field_description:quality_control.field_quality_point__tolerance_max
msgid "Max Tolerance"
msgstr "Тэсвэрийн дээд хэмжээ"

#. module: quality_control
#: model:ir.model.fields,field_description:quality_control.field_quality_check__measure
#: model:ir.model.fields,field_description:quality_control.field_quality_check_wizard__measure
#: model_terms:ir.ui.view,arch_db:quality_control.quality_check_view_form
msgid "Measure"
msgstr "Хэмжих"

#. module: quality_control
#: model:ir.model.fields,field_description:quality_control.field_quality_point__measure_frequency_unit
msgid "Measure Frequency Unit"
msgstr ""

#. module: quality_control
#: model:ir.model.fields,field_description:quality_control.field_quality_check__measure_success
msgid "Measure Success"
msgstr "Хэмжилт амжилттай"

#. module: quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.quality_point_view_form_inherit_quality_control
msgid "Message If Failure"
msgstr ""

#. module: quality_control
#: model:ir.model.fields,field_description:quality_control.field_quality_check__tolerance_min
#: model:ir.model.fields,field_description:quality_control.field_quality_point__tolerance_min
msgid "Min Tolerance"
msgstr "Тэсвэрийн доод хэмжээ"

#. module: quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.quality_alert_view_form
msgid "Miscellaneous"
msgstr "Бусад"

#. module: quality_control
#: model:ir.model.fields.selection,name:quality_control.selection__quality_point__measure_frequency_unit__month
msgid "Months"
msgstr "Сар"

#. module: quality_control
#: model:ir.model.fields,field_description:quality_control.field_quality_check_wizard__nb_checks
msgid "Nb Checks"
msgstr ""

#. module: quality_control
#. odoo-python
#: code:addons/quality_control/models/quality.py:0
#, python-format
msgid "New"
msgstr "Шинэ"

#. module: quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.view_quality_check_wizard
msgid "Next"
msgstr "Дараах"

#. module: quality_control
#: model:ir.model.fields.selection,name:quality_control.selection__stock_move_line__check_state__no_checks
msgid "No checks"
msgstr ""

#. module: quality_control
#: model_terms:ir.actions.act_window,help:quality_control.quality_check_action_spc
msgid "No data yet!"
msgstr "Одоогоор мэдээлэл алга!"

#. module: quality_control
#: model:ir.model.fields.selection,name:quality_control.selection__quality_check__measure_success__none
msgid "No measure"
msgstr "Хэмжилт алга"

#. module: quality_control
#: model_terms:ir.actions.act_window,help:quality_control.quality_alert_action_report
msgid "No quality alert"
msgstr ""

#. module: quality_control
#: model_terms:ir.actions.act_window,help:quality_control.quality_check_action_main
#: model_terms:ir.actions.act_window,help:quality_control.quality_check_action_team
msgid "No quality check found"
msgstr ""

#. module: quality_control
#: model_terms:ir.actions.act_window,help:quality_control.quality_check_action_report
msgid "No quality checks"
msgstr ""

#. module: quality_control
#: model_terms:ir.actions.act_window,help:quality_control.quality_point_action
msgid "No quality control point found"
msgstr ""

#. module: quality_control
#: model:ir.model.fields,field_description:quality_control.field_quality_point__norm
msgid "Norm"
msgstr "Норм"

#. module: quality_control
#: model:ir.model.fields,field_description:quality_control.field_quality_check__norm_unit
#: model:ir.model.fields,field_description:quality_control.field_quality_check_wizard__norm_unit
#: model:ir.model.fields,field_description:quality_control.field_quality_point__norm_unit
msgid "Norm Unit"
msgstr ""

#. module: quality_control
#: model:ir.model.fields,field_description:quality_control.field_quality_check_wizard__note
msgid "Note"
msgstr "Тэмдэглэл"

#. module: quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.quality_check_view_form
msgid "Notes"
msgstr "Тэмдэглэлүүд"

#. module: quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.quality_check_wizard_form_failure
msgid "OK"
msgstr "OK"

#. module: quality_control
#: model:ir.model.fields.selection,name:quality_control.selection__quality_check__measure_on__operation
#: model:ir.model.fields.selection,name:quality_control.selection__quality_point__measure_on__operation
msgid "Operation"
msgstr "Ажилбар"

#. module: quality_control
#: model:ir.model.fields,help:quality_control.field_quality_check__measure_on
#: model:ir.model.fields,help:quality_control.field_quality_check_wizard__measure_on
#: model:ir.model.fields,help:quality_control.field_quality_point__measure_on
msgid ""
"Operation = One quality check is requested at the operation level.\n"
"                  Product = A quality check is requested per product.\n"
"                 Quantity = A quality check is requested for each new product quantity registered, with partial quantity checks also possible."
msgstr ""

#. module: quality_control
#: model:ir.ui.menu,name:quality_control.menu_quality_dashboard
msgid "Overview"
msgstr "Тойм"

#. module: quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.quality_point_view_form_inherit_quality_control
msgid "Partial Transfer Test"
msgstr ""

#. module: quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.quality_check_view_form
msgid "Partner"
msgstr "Харилцагч"

#. module: quality_control
#: model:ir.model.fields.selection,name:quality_control.selection__quality_check__measure_success__pass
#: model_terms:ir.ui.view,arch_db:quality_control.quality_check_view_form
#: model_terms:ir.ui.view,arch_db:quality_control.view_quality_check_wizard
msgid "Pass"
msgstr "Тэнцэх"

#. module: quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.quality_check_view_search
msgid "Passed"
msgstr "Тэнцсэн"

#. module: quality_control
#: model:ir.model.fields,field_description:quality_control.field_stock_picking__quality_check_todo
msgid "Pending checks"
msgstr "Хүлээгдэж буй шалгалтууд"

#. module: quality_control
#: model:ir.model.fields,field_description:quality_control.field_quality_point__measure_frequency_value
#: model_terms:ir.ui.view,arch_db:quality_control.quality_point_view_form_inherit_quality_control
msgid "Percentage"
msgstr "Хувь"

#. module: quality_control
#: model:ir.model.fields.selection,name:quality_control.selection__quality_point__measure_frequency_type__periodical
msgid "Periodically"
msgstr "Мөчлөгөөр"

#. module: quality_control
#: model:ir.model.fields,field_description:quality_control.field_quality_check_wizard__picture
msgid "Picture"
msgstr "Зураг"

#. module: quality_control
#. odoo-python
#: code:addons/quality_control/models/quality.py:0
#, python-format
msgid "Picture Uploaded"
msgstr ""

#. module: quality_control
#: model:ir.model.fields,field_description:quality_control.field_quality_check_wizard__position_current_check
msgid "Position Current Check"
msgstr ""

#. module: quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.quality_alert_view_form
msgid "Preventive Actions"
msgstr "Сэргийлэх үйлдлүүд"

#. module: quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.view_quality_check_wizard
msgid "Previous"
msgstr "Өмнөх"

#. module: quality_control
#: model:ir.model,name:quality_control.model_product_template
#: model:ir.model.fields,field_description:quality_control.field_quality_check_wizard__product_id
#: model:ir.model.fields.selection,name:quality_control.selection__quality_check__measure_on__product
#: model:ir.model.fields.selection,name:quality_control.selection__quality_point__measure_on__product
#: model_terms:ir.ui.view,arch_db:quality_control.quality_check_view_search
msgid "Product"
msgstr "Бараа"

#. module: quality_control
#: model:ir.model,name:quality_control.model_stock_move_line
msgid "Product Moves (Stock Move Line)"
msgstr "Барааны хөдөлгөөн (Дэлгэрэнгүй)"

#. module: quality_control
#: model:ir.model.fields,field_description:quality_control.field_quality_check__uom_id
#: model:ir.model.fields,field_description:quality_control.field_quality_check_wizard__uom_id
msgid "Product Unit of Measure"
msgstr "Барааны хэмжих нэгж"

#. module: quality_control
#: model:ir.model,name:quality_control.model_product_product
msgid "Product Variant"
msgstr "Барааны хувилбар"

#. module: quality_control
#: model:ir.ui.menu,name:quality_control.quality_control_menu_product_variant
msgid "Product Variants"
msgstr "Барааны хувилбар"

#. module: quality_control
#: model:ir.ui.menu,name:quality_control.quality_control_menu_product
#: model:ir.ui.menu,name:quality_control.quality_product_menu_product_template
msgid "Products"
msgstr "Бараа"

#. module: quality_control
#: model:ir.ui.menu,name:quality_control.menu_quality_root
msgid "Quality"
msgstr "Чанар"

#. module: quality_control
#. odoo-python
#: code:addons/quality_control/models/quality.py:0
#: model:ir.model,name:quality_control.model_quality_alert
#: model_terms:ir.ui.view,arch_db:quality_control.quality_alert_view_calendar
#: model_terms:ir.ui.view,arch_db:quality_control.quality_alert_view_search_inherit_quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.stock_picking_view_form_inherit_quality
#, python-format
msgid "Quality Alert"
msgstr "Чанарын анхааруулга"

#. module: quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.quality_alert_view_graph
#: model_terms:ir.ui.view,arch_db:quality_control.quality_alert_view_pivot
msgid "Quality Alert Analysis"
msgstr "Чанарын анхааруулгын шинжилгээ"

#. module: quality_control
#: model:ir.model.fields,field_description:quality_control.field_stock_picking__quality_alert_count
msgid "Quality Alert Count"
msgstr "Чанарын анхааруулгын тоо"

#. module: quality_control
#: model:ir.actions.act_window,name:quality_control.quality_alert_stage_action
#: model:ir.ui.menu,name:quality_control.menu_quality_config_alert_stage
msgid "Quality Alert Stages"
msgstr "Чанарын анхааруулгын шатууд"

#. module: quality_control
#: model_terms:ir.actions.act_window,help:quality_control.quality_alert_stage_action
msgid "Quality Alert stages define the different steps a quality alert should go through."
msgstr "Чанарын анхааруулгын шатууд нь чанарын анхааруулгын туулах шатуудыг тодорхойлдог."

#. module: quality_control
#: model:ir.actions.act_window,name:quality_control.quality_alert_action_check
#: model:ir.actions.act_window,name:quality_control.quality_alert_action_report
#: model:ir.actions.act_window,name:quality_control.quality_alert_action_team
#: model:ir.ui.menu,name:quality_control.menu_quality_alert
#: model:ir.ui.menu,name:quality_control.menu_quality_alert_report
#: model_terms:ir.ui.view,arch_db:quality_control.quality_alert_team_dashboard_view_kanban
msgid "Quality Alerts"
msgstr "Чанарын анхааруулгууд"

#. module: quality_control
#. odoo-python
#: code:addons/quality_control/models/quality.py:0
#: model:ir.actions.act_window,name:quality_control.action_quality_check_wizard
#: model:ir.model,name:quality_control.model_quality_check
#: model_terms:ir.ui.view,arch_db:quality_control.view_stock_move_line_detailed_operation_tree_inherit_quality
#, python-format
msgid "Quality Check"
msgstr "Чанарын Шалгалт"

#. module: quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.quality_check_view_graph
#: model_terms:ir.ui.view,arch_db:quality_control.quality_check_view_pivot
msgid "Quality Check Analysis"
msgstr "Чанарын Шалгалтын шинжилгээ"

#. module: quality_control
#: model:ir.model.fields,field_description:quality_control.field_stock_picking__quality_check_fail
msgid "Quality Check Fail"
msgstr "Чанарын шалгалтын алдаа"

#. module: quality_control
#. odoo-python
#: code:addons/quality_control/wizard/quality_check_wizard.py:0
#: model_terms:ir.ui.view,arch_db:quality_control.quality_check_wizard_form_failure
#, python-format
msgid "Quality Check Failed"
msgstr ""

#. module: quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.worksheet_page
msgid "Quality Check Picture"
msgstr ""

#. module: quality_control
#: model:ir.model.fields,field_description:quality_control.field_stock_lot__quality_check_qty
msgid "Quality Check Qty"
msgstr ""

#. module: quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.worksheet_page
msgid "Quality Check:"
msgstr ""

#. module: quality_control
#: model:ir.actions.act_window,name:quality_control.quality_check_action_main
#: model:ir.actions.act_window,name:quality_control.quality_check_action_picking
#: model:ir.actions.act_window,name:quality_control.quality_check_action_production_lot
#: model:ir.actions.act_window,name:quality_control.quality_check_action_report
#: model:ir.actions.act_window,name:quality_control.quality_check_action_team
#: model:ir.ui.menu,name:quality_control.menu_quality_check_report
#: model:ir.ui.menu,name:quality_control.menu_quality_checks
#: model_terms:ir.ui.view,arch_db:quality_control.quality_point_view_form_inherit_quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.stock_picking_view_form_inherit_quality
#: model_terms:ir.ui.view,arch_db:quality_control.stock_production_lot_form_quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.view_quality_check_wizard
msgid "Quality Checks"
msgstr "Чанарын шалгалтууд"

#. module: quality_control
#: model:ir.actions.act_window,name:quality_control.quality_check_action_spc
msgid "Quality Checks SPC"
msgstr "Чанарын шалгалтууд SPC"

#. module: quality_control
#: model:ir.ui.menu,name:quality_control.menu_quality_control
msgid "Quality Control"
msgstr "Чанарын хяналт"

#. module: quality_control
#: model:ir.model,name:quality_control.model_quality_point
msgid "Quality Control Point"
msgstr "Чанарын хяналтын цэг"

#. module: quality_control
#: model:ir.model.fields,field_description:quality_control.field_product_product__quality_control_point_qty
#: model:ir.model.fields,field_description:quality_control.field_product_template__quality_control_point_qty
msgid "Quality Control Point Qty"
msgstr ""

#. module: quality_control
#: model:ir.actions.act_window,name:quality_control.quality_point_action
msgid "Quality Control Points"
msgstr "Чанарын хяналтын цэгүүд"

#. module: quality_control
#: model:ir.model.fields,field_description:quality_control.field_product_product__quality_fail_qty
#: model:ir.model.fields,field_description:quality_control.field_product_template__quality_fail_qty
msgid "Quality Fail Qty"
msgstr ""

#. module: quality_control
#: model:ir.actions.act_window,name:quality_control.quality_alert_team_action
msgid "Quality Overview"
msgstr ""

#. module: quality_control
#: model:ir.model.fields,field_description:quality_control.field_product_product__quality_pass_qty
#: model:ir.model.fields,field_description:quality_control.field_product_template__quality_pass_qty
msgid "Quality Pass Qty"
msgstr ""

#. module: quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.product_product_form_view_quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.product_template_form_view_quality_control
msgid "Quality Points"
msgstr ""

#. module: quality_control
#: model:ir.actions.act_window,name:quality_control.quality_tag_action
#: model:ir.ui.menu,name:quality_control.menu_config_quality_tags
msgid "Quality Tags"
msgstr ""

#. module: quality_control
#: model:ir.actions.act_window,name:quality_control.quality_alert_team_action_config
#: model:ir.ui.menu,name:quality_control.menu_quality_config_alert_team
msgid "Quality Teams"
msgstr "Чанарын багууд"

#. module: quality_control
#: model_terms:ir.actions.act_window,help:quality_control.quality_alert_team_action
msgid ""
"Quality Teams group the different quality alerts/checks\n"
"              according to the roles (teams) that need them."
msgstr ""

#. module: quality_control
#: model:ir.model,name:quality_control.model_report_quality_control_quality_worksheet_internal
msgid "Quality Worksheet Internal Report"
msgstr ""

#. module: quality_control
#: model:ir.model,name:quality_control.model_report_quality_control_quality_worksheet
msgid "Quality Worksheet Report"
msgstr ""

#. module: quality_control
#: model_terms:ir.actions.act_window,help:quality_control.quality_point_action
msgid ""
"Quality control points define the quality checks which should be\n"
"              performed at each operation, for your different products."
msgstr ""

#. module: quality_control
#: model:ir.model.fields,field_description:quality_control.field_quality_check__qty_line
#: model:ir.model.fields,field_description:quality_control.field_quality_check_wizard__qty_line
#: model:ir.model.fields.selection,name:quality_control.selection__quality_check__measure_on__move_line
#: model:ir.model.fields.selection,name:quality_control.selection__quality_point__measure_on__move_line
msgid "Quantity"
msgstr "Тоо хэмжээ"

#. module: quality_control
#: model:ir.model.fields,field_description:quality_control.field_quality_check__qty_tested
#: model:ir.model.fields,field_description:quality_control.field_quality_check_wizard__qty_tested
msgid "Quantity Tested"
msgstr ""

#. module: quality_control
#: model:ir.model.fields,help:quality_control.field_quality_check__qty_tested
#: model:ir.model.fields,help:quality_control.field_quality_check_wizard__qty_tested
msgid "Quantity of product tested within the lot"
msgstr ""

#. module: quality_control
#: model:ir.model.fields,help:quality_control.field_quality_check__qty_to_test
#: model:ir.model.fields,help:quality_control.field_quality_check_wizard__qty_to_test
msgid "Quantity of product to test within the lot"
msgstr ""

#. module: quality_control
#: model:ir.model.fields,field_description:quality_control.field_quality_check__qty_to_test
#: model:ir.model.fields,field_description:quality_control.field_quality_check_wizard__qty_to_test
msgid "Quantity to Test"
msgstr ""

#. module: quality_control
#: model:ir.model.fields.selection,name:quality_control.selection__quality_point__measure_frequency_type__random
msgid "Randomly"
msgstr "Санамсаргүйгээр"

#. module: quality_control
#: model:ir.model.fields,field_description:quality_control.field_quality_check_wizard__name
msgid "Reference"
msgstr "Холбогдол"

#. module: quality_control
#: model:ir.ui.menu,name:quality_control.menu_quality_reporting
msgid "Reporting"
msgstr "Тайлан"

#. module: quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.quality_point_view_search
msgid "Responsible"
msgstr "Хариуцагч"

#. module: quality_control
#: model:ir.model.fields,field_description:quality_control.field_quality_check__show_lot_text
#: model:ir.model.fields,field_description:quality_control.field_quality_check_wizard__show_lot_text
msgid "Show Lot Text"
msgstr ""

#. module: quality_control
#: model:ir.model.fields.selection,name:quality_control.selection__stock_move_line__check_state__fail
msgid "Some checks failed"
msgstr ""

#. module: quality_control
#: model:ir.model.fields.selection,name:quality_control.selection__stock_move_line__check_state__in_progress
msgid "Some checks to be done"
msgstr ""

#. module: quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.quality_alert_stage_view_tree
msgid "Stage Name"
msgstr "Үе шатын нэр"

#. module: quality_control
#: model:ir.model.fields,field_description:quality_control.field_quality_point__standard_deviation
msgid "Standard Deviation"
msgstr ""

#. module: quality_control
#: model:ir.model.fields,field_description:quality_control.field_quality_check_wizard__quality_state
#: model_terms:ir.ui.view,arch_db:quality_control.quality_check_view_search
msgid "Status"
msgstr "Төлөв"

#. module: quality_control
#: model:ir.model,name:quality_control.model_stock_move
msgid "Stock Move"
msgstr "Барааны хөдөлгөөн"

#. module: quality_control
#: model:ir.model.fields,field_description:quality_control.field_quality_check__move_line_id
msgid "Stock Move Line"
msgstr ""

#. module: quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.quality_tag_view_search
#: model_terms:ir.ui.view,arch_db:quality_control.quality_tag_view_tree
msgid "Tags"
msgstr "Пайз"

#. module: quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.quality_check_view_search
#: model_terms:ir.ui.view,arch_db:quality_control.quality_point_view_search
msgid "Team"
msgstr "Баг"

#. module: quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.quality_alert_team_view_form
msgid "Team Name"
msgstr "Багийн нэр"

#. module: quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.quality_alert_team_view_tree
msgid "Teams"
msgstr "Багууд"

#. module: quality_control
#: model:ir.model.fields,field_description:quality_control.field_quality_check_wizard__test_type
msgid "Technical name"
msgstr "Техникийн нэр"

#. module: quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.quality_point_view_search
msgid "Test Type"
msgstr "Тестийн төрөл"

#. module: quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.quality_point_view_tree
msgid "Testing % Within Lot"
msgstr ""

#. module: quality_control
#: model:ir.model.fields,field_description:quality_control.field_quality_check__testing_percentage_within_lot
#: model:ir.model.fields,field_description:quality_control.field_quality_check_wizard__testing_percentage_within_lot
#: model:ir.model.fields,field_description:quality_control.field_quality_point__testing_percentage_within_lot
msgid "Testing Percentage Within Lot"
msgstr ""

#. module: quality_control
#: model:ir.model.fields,field_description:quality_control.field_quality_alert__title
#: model:ir.model.fields,field_description:quality_control.field_quality_check_wizard__title
msgid "Title"
msgstr "Гарчиг"

#. module: quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.quality_point_view_form_inherit_quality_control
msgid "Tolerance"
msgstr "Тэсвэр"

#. module: quality_control
#: model:ir.model.fields,field_description:quality_control.field_quality_check__product_tracking
#: model:ir.model.fields,field_description:quality_control.field_quality_check_wizard__product_tracking
msgid "Tracking"
msgstr "Хөтлөлт"

#. module: quality_control
#: model:ir.model,name:quality_control.model_stock_picking
#: model_terms:ir.ui.view,arch_db:quality_control.quality_check_view_tree
msgid "Transfer"
msgstr "Шилжүүлэг"

#. module: quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.quality_check_view_form
msgid "Type"
msgstr "Төрөл"

#. module: quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.quality_point_view_form_inherit_quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.view_quality_check_wizard
msgid "Unit of Measure"
msgstr "Хэмжих нэгж"

#. module: quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.view_quality_check_wizard
msgid "Validate"
msgstr "Зөвшөөрөх"

#. module: quality_control
#: model:ir.model.fields,field_description:quality_control.field_quality_check__warning_message
#: model:ir.model.fields,field_description:quality_control.field_quality_check_wizard__warning_message
msgid "Warning Message"
msgstr "Анхааруулга зурвас"

#. module: quality_control
#: model:ir.model.fields.selection,name:quality_control.selection__quality_point__measure_frequency_unit__week
msgid "Weeks"
msgstr "7 хоног"

#. module: quality_control
#: model:ir.model,name:quality_control.model_quality_check_wizard
msgid "Wizard for Quality Check Pop Up"
msgstr ""

#. module: quality_control
#: model:ir.actions.report,name:quality_control.quality_check_report
msgid "Worksheet Report - External (PDF)"
msgstr ""

#. module: quality_control
#: model:ir.actions.report,name:quality_control.quality_check_report_internal
msgid "Worksheet Report - Internal (PDF)"
msgstr ""

#. module: quality_control
#. odoo-python
#: code:addons/quality_control/models/quality.py:0
#, python-format
msgid "You measured %.2f %s and it should be between %.2f and %.2f %s."
msgstr ""

#. module: quality_control
#. odoo-python
#: code:addons/quality_control/wizard/quality_check_wizard.py:0
#, python-format
msgid "You must provide a picture before validating"
msgstr ""

#. module: quality_control
#. odoo-python
#: code:addons/quality_control/models/stock_picking.py:0
#, python-format
msgid "You still need to do the quality checks!"
msgstr "Та чанарын шалгалтыг хийх шаардагатай хэвээр байна!"

#. module: quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.quality_alert_team_view_form
msgid "e.g. The QA Masters"
msgstr ""
