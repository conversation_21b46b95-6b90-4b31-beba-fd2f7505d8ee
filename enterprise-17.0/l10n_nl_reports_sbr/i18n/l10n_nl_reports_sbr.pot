# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* l10n_nl_reports_sbr
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-04-15 15:39+0000\n"
"PO-Revision-Date: 2024-04-15 15:39+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: l10n_nl_reports_sbr
#: model_terms:ir.ui.view,arch_db:l10n_nl_reports_sbr.res_config_settings_view_form
msgid ""
"<span class=\"o_form_label\">SBR services</span>\n"
"                        <span class=\"fa fa-lg fa-building-o\" title=\"Values set here are company-specific.\" groups=\"base.group_multi_company\"/>"
msgstr ""

#. module: l10n_nl_reports_sbr
#. odoo-python
#: code:addons/l10n_nl_reports_sbr/models/tax_report.py:0
#, python-format
msgid ""
"A new module (l10n_nl_reports_sbr_statusinformatieservice) needs to be "
"installed for the service to track your submission status correctly."
msgstr ""

#. module: l10n_nl_reports_sbr
#: model:ir.model.fields,help:l10n_nl_reports_sbr.field_res_company__l10n_nl_reports_sbr_key
#: model:ir.model.fields,help:l10n_nl_reports_sbr.field_res_config_settings__l10n_nl_reports_sbr_key
msgid ""
"A private key is required in order for the Digipoort services to identify "
"you. No need to upload a key if it is already included in the certificate "
"file."
msgstr ""

#. module: l10n_nl_reports_sbr
#. odoo-python
#: code:addons/l10n_nl_reports_sbr/wizard/l10n_nl_reports_sbr_tax_report_wizard.py:0
#, python-format
msgid ""
"An error occured while using your certificate. Please verify the certificate"
" you uploaded and try again."
msgstr ""

#. module: l10n_nl_reports_sbr
#. odoo-python
#: code:addons/l10n_nl_reports_sbr/models/res_company.py:0
#, python-format
msgid ""
"An error occurred while decrypting your certificate or private key. Please "
"verify your password."
msgstr ""

#. module: l10n_nl_reports_sbr
#. odoo-python
#: code:addons/l10n_nl_reports_sbr/models/res_config_settings.py:0
#, python-format
msgid ""
"An error occurred while loading the certificate. Please check the uploaded "
"file and the related password."
msgstr ""

#. module: l10n_nl_reports_sbr
#: model:ir.model.fields,help:l10n_nl_reports_sbr.field_l10n_nl_reports_sbr_tax_report_wizard__contact_type
msgid ""
"BPL: if the taxpayer files a turnover tax return as an individual "
"entrepreneur.INT: if the turnover tax return is made by an intermediary."
msgstr ""

#. module: l10n_nl_reports_sbr
#: model:ir.model.fields,field_description:l10n_nl_reports_sbr.field_l10n_nl_reports_sbr_tax_report_wizard__can_report_be_sent
msgid "Can Report Be Sent"
msgstr ""

#. module: l10n_nl_reports_sbr
#: model:ir.model.fields,field_description:l10n_nl_reports_sbr.field_res_company__l10n_nl_reports_sbr_cert_filename
#: model:ir.model.fields,field_description:l10n_nl_reports_sbr.field_res_config_settings__l10n_nl_reports_sbr_cert_filename
msgid "Certificate File Name"
msgstr ""

#. module: l10n_nl_reports_sbr
#: model:ir.model.fields,field_description:l10n_nl_reports_sbr.field_l10n_nl_reports_sbr_tax_report_wizard__password
msgid "Certificate or private key password"
msgstr ""

#. module: l10n_nl_reports_sbr
#: model:ir.model.fields,field_description:l10n_nl_reports_sbr.field_res_config_settings__l10n_nl_reports_sbr_password
msgid "Certificate/key password"
msgstr ""

#. module: l10n_nl_reports_sbr
#: model:ir.model.fields,help:l10n_nl_reports_sbr.field_l10n_nl_reports_sbr_tax_report_wizard__is_test
msgid ""
"Check this if you want the system to use the pre-production environment with"
" test certificates."
msgstr ""

#. module: l10n_nl_reports_sbr
#: model_terms:ir.ui.view,arch_db:l10n_nl_reports_sbr.l10n_nl_reports_sbr_tax_report_wizard_form
msgid "Close"
msgstr ""

#. module: l10n_nl_reports_sbr
#. odoo-python
#: code:addons/l10n_nl_reports_sbr/wizard/l10n_nl_reports_sbr_tax_report_wizard.py:0
#, python-format
msgid "Closing Entry"
msgstr ""

#. module: l10n_nl_reports_sbr
#: model:ir.model,name:l10n_nl_reports_sbr.model_res_company
msgid "Companies"
msgstr ""

#. module: l10n_nl_reports_sbr
#. odoo-python
#: code:addons/l10n_nl_reports_sbr/wizard/l10n_nl_reports_sbr_tax_report_wizard.py:0
#: code:addons/l10n_nl_reports_sbr/wizard/l10n_nl_reports_sbr_tax_report_wizard.py:0
#, python-format
msgid "Company settings"
msgstr ""

#. module: l10n_nl_reports_sbr
#: model:ir.model,name:l10n_nl_reports_sbr.model_res_config_settings
msgid "Config Settings"
msgstr ""

#. module: l10n_nl_reports_sbr
#: model:ir.model.fields,field_description:l10n_nl_reports_sbr.field_l10n_nl_reports_sbr_tax_report_wizard__contact_initials
msgid "Contact Initials"
msgstr ""

#. module: l10n_nl_reports_sbr
#: model:ir.model.fields,field_description:l10n_nl_reports_sbr.field_l10n_nl_reports_sbr_tax_report_wizard__contact_surname
msgid "Contact Last Name"
msgstr ""

#. module: l10n_nl_reports_sbr
#: model:ir.model.fields,field_description:l10n_nl_reports_sbr.field_l10n_nl_reports_sbr_tax_report_wizard__contact_prefix
msgid "Contact Name Infix"
msgstr ""

#. module: l10n_nl_reports_sbr
#: model:ir.model.fields,field_description:l10n_nl_reports_sbr.field_l10n_nl_reports_sbr_tax_report_wizard__contact_phone
msgid "Contact Phone"
msgstr ""

#. module: l10n_nl_reports_sbr
#: model:ir.model.fields,field_description:l10n_nl_reports_sbr.field_l10n_nl_reports_sbr_tax_report_wizard__contact_type
msgid "Contact Type"
msgstr ""

#. module: l10n_nl_reports_sbr
#. odoo-python
#: code:addons/l10n_nl_reports_sbr/wizard/l10n_nl_reports_sbr_tax_report_wizard.py:0
#: model:ir.actions.server,name:l10n_nl_reports_sbr.action_open_closing_entry
#, python-format
msgid "Create Closing Entry"
msgstr ""

#. module: l10n_nl_reports_sbr
#: model_terms:ir.ui.view,arch_db:l10n_nl_reports_sbr.l10n_nl_reports_sbr_tax_report_wizard_form
msgid "Create Tax Report XBRL for SBR"
msgstr ""

#. module: l10n_nl_reports_sbr
#: model:ir.model.fields,field_description:l10n_nl_reports_sbr.field_l10n_nl_reports_sbr_tax_report_wizard__create_uid
msgid "Created by"
msgstr ""

#. module: l10n_nl_reports_sbr
#: model:ir.model.fields,field_description:l10n_nl_reports_sbr.field_l10n_nl_reports_sbr_tax_report_wizard__create_date
msgid "Created on"
msgstr ""

#. module: l10n_nl_reports_sbr
#: model:ir.model.fields,field_description:l10n_nl_reports_sbr.field_l10n_nl_reports_sbr_tax_report_wizard__display_name
msgid "Display Name"
msgstr ""

#. module: l10n_nl_reports_sbr
#: model_terms:ir.ui.view,arch_db:l10n_nl_reports_sbr.l10n_nl_reports_sbr_tax_report_wizard_form
msgid "Download"
msgstr ""

#. module: l10n_nl_reports_sbr
#: model:ir.model,name:l10n_nl_reports_sbr.model_l10n_nl_tax_report_handler
msgid "Dutch Report Custom Handler"
msgstr ""

#. module: l10n_nl_reports_sbr
#. odoo-python
#: code:addons/l10n_nl_reports_sbr/models/tax_report.py:0
#: code:addons/l10n_nl_reports_sbr/wizard/l10n_nl_reports_sbr_tax_report_wizard.py:0
#, python-format
msgid "Go to Apps"
msgstr ""

#. module: l10n_nl_reports_sbr
#. odoo-python
#: code:addons/l10n_nl_reports_sbr/models/res_company.py:0
#, python-format
msgid "Go to the Accounting Settings"
msgstr ""

#. module: l10n_nl_reports_sbr
#: model:ir.model.fields,field_description:l10n_nl_reports_sbr.field_l10n_nl_reports_sbr_tax_report_wizard__id
msgid "ID"
msgstr ""

#. module: l10n_nl_reports_sbr
#: model:ir.model.fields.selection,name:l10n_nl_reports_sbr.selection__l10n_nl_reports_sbr_icp_icp_wizard__contact_type__int
#: model:ir.model.fields.selection,name:l10n_nl_reports_sbr.selection__l10n_nl_reports_sbr_tax_report_wizard__contact_type__int
msgid "Intermediary (INT)"
msgstr ""

#. module: l10n_nl_reports_sbr
#: model:ir.model.fields,field_description:l10n_nl_reports_sbr.field_l10n_nl_reports_sbr_tax_report_wizard__is_test
msgid "Is Test"
msgstr ""

#. module: l10n_nl_reports_sbr
#: model:ir.model,name:l10n_nl_reports_sbr.model_l10n_nl_reports_sbr_tax_report_wizard
msgid "L10n NL Tax Report for SBR Wizard"
msgstr ""

#. module: l10n_nl_reports_sbr
#: model:ir.model.fields,field_description:l10n_nl_reports_sbr.field_res_company__l10n_nl_reports_sbr_last_sent_date_to
msgid "Last Date Sent"
msgstr ""

#. module: l10n_nl_reports_sbr
#: model:ir.model.fields,field_description:l10n_nl_reports_sbr.field_l10n_nl_reports_sbr_tax_report_wizard____last_update
msgid "Last Modified on"
msgstr ""

#. module: l10n_nl_reports_sbr
#: model:ir.model.fields,field_description:l10n_nl_reports_sbr.field_l10n_nl_reports_sbr_tax_report_wizard__write_uid
msgid "Last Updated by"
msgstr ""

#. module: l10n_nl_reports_sbr
#: model:ir.model.fields,field_description:l10n_nl_reports_sbr.field_l10n_nl_reports_sbr_tax_report_wizard__write_date
msgid "Last Updated on"
msgstr ""

#. module: l10n_nl_reports_sbr
#: model_terms:ir.ui.view,arch_db:l10n_nl_reports_sbr.l10n_nl_reports_sbr_tax_report_wizard_form
msgid "New SBR File"
msgstr ""

#. module: l10n_nl_reports_sbr
#. odoo-python
#: code:addons/l10n_nl_reports_sbr/wizard/l10n_nl_reports_sbr_tax_report_wizard.py:0
#, python-format
msgid ""
"No Closing Entry was found for the selected period. Please create one and "
"post it before sending your report."
msgstr ""

#. module: l10n_nl_reports_sbr
#: model:ir.model.fields,field_description:l10n_nl_reports_sbr.field_res_company__l10n_nl_reports_sbr_cert
#: model:ir.model.fields,field_description:l10n_nl_reports_sbr.field_res_config_settings__l10n_nl_reports_sbr_cert
msgid "PKI Certificate"
msgstr ""

#. module: l10n_nl_reports_sbr
#: model:ir.model.fields,field_description:l10n_nl_reports_sbr.field_res_company__l10n_nl_reports_sbr_key
#: model:ir.model.fields,field_description:l10n_nl_reports_sbr.field_res_config_settings__l10n_nl_reports_sbr_key
msgid "PKI Private Key"
msgstr ""

#. module: l10n_nl_reports_sbr
#: model:ir.model.fields,field_description:l10n_nl_reports_sbr.field_l10n_nl_reports_sbr_tax_report_wizard__date_to
msgid "Period Ending Date"
msgstr ""

#. module: l10n_nl_reports_sbr
#: model:ir.model.fields,field_description:l10n_nl_reports_sbr.field_l10n_nl_reports_sbr_tax_report_wizard__date_from
msgid "Period Starting Date"
msgstr ""

#. module: l10n_nl_reports_sbr
#. odoo-python
#: code:addons/l10n_nl_reports_sbr/models/tax_report.py:0
#, python-format
msgid ""
"Please select only one company to send the report. If you wish to aggregate "
"multiple companies, please create a tax unit."
msgstr ""

#. module: l10n_nl_reports_sbr
#: model:ir.model.fields,field_description:l10n_nl_reports_sbr.field_res_company__l10n_nl_reports_sbr_key_filename
#: model:ir.model.fields,field_description:l10n_nl_reports_sbr.field_res_config_settings__l10n_nl_reports_sbr_key_filename
msgid "Private Key File Name"
msgstr ""

#. module: l10n_nl_reports_sbr
#: model:ir.model.fields,field_description:l10n_nl_reports_sbr.field_res_company__l10n_nl_reports_sbr_server_root_cert
msgid "SBR Root Certificate"
msgstr ""

#. module: l10n_nl_reports_sbr
#: model_terms:ir.ui.view,arch_db:l10n_nl_reports_sbr.l10n_nl_reports_sbr_tax_report_wizard_form
msgid "Send"
msgstr ""

#. module: l10n_nl_reports_sbr
#. odoo-python
#: code:addons/l10n_nl_reports_sbr/wizard/l10n_nl_reports_sbr_tax_report_wizard.py:0
#, python-format
msgid "Sending your report"
msgstr ""

#. module: l10n_nl_reports_sbr
#: model:ir.model.fields,help:l10n_nl_reports_sbr.field_res_company__l10n_nl_reports_sbr_last_sent_date_to
msgid ""
"Stores the date of the end of the last period submitted to the Tax Services"
msgstr ""

#. module: l10n_nl_reports_sbr
#: model:ir.model.fields,field_description:l10n_nl_reports_sbr.field_l10n_nl_reports_sbr_tax_report_wizard__tax_consultant_number
msgid "Tax Consultant Number"
msgstr ""

#. module: l10n_nl_reports_sbr
#. odoo-python
#: code:addons/l10n_nl_reports_sbr/models/tax_report.py:0
#, python-format
msgid "Tax Report SBR"
msgstr ""

#. module: l10n_nl_reports_sbr
#. odoo-python
#: code:addons/l10n_nl_reports_sbr/wizard/l10n_nl_reports_sbr_tax_report_wizard.py:0
#, python-format
msgid "Tax report sent"
msgstr ""

#. module: l10n_nl_reports_sbr
#: model:ir.model.fields.selection,name:l10n_nl_reports_sbr.selection__l10n_nl_reports_sbr_icp_icp_wizard__contact_type__bpl
#: model:ir.model.fields.selection,name:l10n_nl_reports_sbr.selection__l10n_nl_reports_sbr_tax_report_wizard__contact_type__bpl
msgid "Taxpayer (BPL)"
msgstr ""

#. module: l10n_nl_reports_sbr
#. odoo-python
#: code:addons/l10n_nl_reports_sbr/wizard/l10n_nl_reports_sbr_tax_report_wizard.py:0
#, python-format
msgid ""
"The Closing Entry for the selected period is still in draft. Please post it "
"before sending your report."
msgstr ""

#. module: l10n_nl_reports_sbr
#: model:ir.model.fields,help:l10n_nl_reports_sbr.field_res_company__l10n_nl_reports_sbr_server_root_cert
msgid ""
"The SBR Tax Service Server Root Certificate is used to verifiy the "
"connection with the Tax services server of the SBR.It is used in order to "
"make the connection library trust the server."
msgstr ""

#. module: l10n_nl_reports_sbr
#. odoo-python
#: code:addons/l10n_nl_reports_sbr/wizard/l10n_nl_reports_sbr_tax_report_wizard.py:0
#, python-format
msgid ""
"The Tax Services returned the error hereunder. Please upgrade your module "
"and try again before submitting a ticket."
msgstr ""

#. module: l10n_nl_reports_sbr
#. odoo-python
#: code:addons/l10n_nl_reports_sbr/models/res_config_settings.py:0
#, python-format
msgid "The certificate or private key for SBR services is missing."
msgstr ""

#. module: l10n_nl_reports_sbr
#. odoo-python
#: code:addons/l10n_nl_reports_sbr/models/res_company.py:0
#, python-format
msgid ""
"The certificate or private key you uploaded is encrypted. Please specify "
"your password."
msgstr ""

#. module: l10n_nl_reports_sbr
#. odoo-python
#: code:addons/l10n_nl_reports_sbr/models/res_company.py:0
#, python-format
msgid ""
"The certificate or the private key is missing. Please upload it in the "
"Accounting Settings first."
msgstr ""

#. module: l10n_nl_reports_sbr
#: model:ir.model.fields,help:l10n_nl_reports_sbr.field_l10n_nl_reports_sbr_tax_report_wizard__password
msgid "The password is not needed for just printing the XBRL file."
msgstr ""

#. module: l10n_nl_reports_sbr
#. odoo-python
#: code:addons/l10n_nl_reports_sbr/models/res_config_settings.py:0
#, python-format
msgid "The provided key could not be successfully loaded."
msgstr ""

#. module: l10n_nl_reports_sbr
#. odoo-python
#: code:addons/l10n_nl_reports_sbr/models/res_config_settings.py:0
#: code:addons/l10n_nl_reports_sbr/models/res_config_settings.py:0
#, python-format
msgid "The provided password for the key is not correct."
msgstr ""

#. module: l10n_nl_reports_sbr
#. odoo-python
#: code:addons/l10n_nl_reports_sbr/models/res_company.py:0
#, python-format
msgid ""
"The server root certificate is not accessible at the moment. Please try "
"again later."
msgstr ""

#. module: l10n_nl_reports_sbr
#: model:ir.model.fields,help:l10n_nl_reports_sbr.field_l10n_nl_reports_sbr_tax_report_wizard__tax_consultant_number
msgid ""
"The tax consultant number of the office aware of the content of this report."
msgstr ""

#. module: l10n_nl_reports_sbr
#. odoo-python
#: code:addons/l10n_nl_reports_sbr/wizard/l10n_nl_reports_sbr_tax_report_wizard.py:0
#, python-format
msgid ""
"The tax report from %s to %s was sent to Digipoort.<br/>We will post its "
"processing status in this chatter once received.<br/>Discussion id: %s"
msgstr ""

#. module: l10n_nl_reports_sbr
#: model_terms:ir.ui.view,arch_db:l10n_nl_reports_sbr.res_config_settings_view_form
msgid ""
"Upload here the PKI-certificate that identifies you to the Digipoort "
"services. You can buy one from KPN or Quovadis."
msgstr ""

#. module: l10n_nl_reports_sbr
#: model:ir.model.fields,help:l10n_nl_reports_sbr.field_res_company__l10n_nl_reports_sbr_cert
#: model:ir.model.fields,help:l10n_nl_reports_sbr.field_res_config_settings__l10n_nl_reports_sbr_cert
msgid ""
"Upload here the certificate file that will be used to connect to the "
"Digipoort infrastructure. The private key from this file will be used, if "
"there is one included."
msgstr ""

#. module: l10n_nl_reports_sbr
#: model_terms:ir.ui.view,arch_db:l10n_nl_reports_sbr.res_config_settings_view_form
msgid ""
"Upload here your Private Key. If your certificate file ends with .p12 or "
".pfx, the Private Key is already included."
msgstr ""

#. module: l10n_nl_reports_sbr
#. odoo-python
#: code:addons/l10n_nl_reports_sbr/models/tax_report.py:0
#: code:addons/l10n_nl_reports_sbr/models/tax_report.py:0
#, python-format
msgid "XBRL"
msgstr ""

#. module: l10n_nl_reports_sbr
#. odoo-python
#: code:addons/l10n_nl_reports_sbr/wizard/l10n_nl_reports_sbr_tax_report_wizard.py:0
#, python-format
msgid ""
"Your Accounting Firm does not have a VAT set. Please set it up before trying"
" to send the report."
msgstr ""

#. module: l10n_nl_reports_sbr
#. odoo-python
#: code:addons/l10n_nl_reports_sbr/wizard/l10n_nl_reports_sbr_tax_report_wizard.py:0
#, python-format
msgid ""
"Your company does not have a VAT set. Please set it up before trying to send"
" the report."
msgstr ""

#. module: l10n_nl_reports_sbr
#. odoo-python
#: code:addons/l10n_nl_reports_sbr/wizard/l10n_nl_reports_sbr_tax_report_wizard.py:0
#, python-format
msgid ""
"Your tax report is being sent to Digipoort. Check its status in the closing "
"entry's chatter."
msgstr ""

#. module: l10n_nl_reports_sbr
#: model_terms:ir.ui.view,arch_db:l10n_nl_reports_sbr.tax_report_sbr
msgid "iso4217:EUR"
msgstr ""
