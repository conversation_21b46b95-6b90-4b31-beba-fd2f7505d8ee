# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* l10n_mx_edi_pos
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-02-28 09:03+0000\n"
"PO-Revision-Date: 2023-08-02 11:55+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"Language: es_419\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: l10n_mx_edi_pos
#: model:ir.model.fields.selection,name:l10n_mx_edi_pos.selection__pos_order__l10n_mx_edi_usage__g01
msgid "Acquisition of merchandise"
msgstr "Adquisición de mercancías"

#. module: l10n_mx_edi_pos
#. odoo-javascript
#: code:addons/l10n_mx_edi_pos/static/src/app/add_info_popup/add_info_popup.xml:0
#, python-format
msgid "Additional Invoicing Information"
msgstr "Información adicional sobre la facturación"

#. module: l10n_mx_edi_pos
#. odoo-javascript
#: code:addons/l10n_mx_edi_pos/static/src/overrides/components/partner_details_edit/partner_details_edit.xml:0
#, python-format
msgid "Additional invoicing information"
msgstr "Información adicional sobre la facturación"

#. module: l10n_mx_edi_pos
#: model:ir.model.fields,field_description:l10n_mx_edi_pos.field_pos_order__l10n_mx_edi_cfdi_attachment_id
#: model_terms:ir.ui.view,arch_db:l10n_mx_edi_pos.pos_order_form_inherit_l10n_mx_edi
msgid "CFDI"
msgstr "CFDI"

#. module: l10n_mx_edi_pos
#: model:ir.model.fields,field_description:l10n_mx_edi_pos.field_pos_order__l10n_mx_edi_cfdi_state
msgid "CFDI status"
msgstr "Estado CFDI"

#. module: l10n_mx_edi_pos
#: model:ir.model.fields,field_description:l10n_mx_edi_pos.field_pos_order__l10n_mx_edi_cfdi_to_public
msgid "CFDI to public"
msgstr "CFDI para público en general"

#. module: l10n_mx_edi_pos
#: model_terms:ir.ui.view,arch_db:l10n_mx_edi_pos.pos_order_form_inherit_l10n_mx_edi
msgid "Cancel"
msgstr "Cancelar"

#. module: l10n_mx_edi_pos
#: model:ir.model.fields.selection,name:l10n_mx_edi_pos.selection__pos_order__l10n_mx_edi_cfdi_sat_state__cancelled
msgid "Cancelled"
msgstr "Cancelado"

#. module: l10n_mx_edi_pos
#: model:ir.model.fields.selection,name:l10n_mx_edi_pos.selection__pos_order__l10n_mx_edi_cfdi_state__global_cancel
msgid "Cancelled Global"
msgstr "Global cancelado"

#. module: l10n_mx_edi_pos
#: model:ir.model.fields.selection,name:l10n_mx_edi_pos.selection__pos_order__l10n_mx_edi_usage__i04
msgid "Computer equipment and accessories"
msgstr "Equipo de cómputo y accesorios"

#. module: l10n_mx_edi_pos
#. odoo-javascript
#: code:addons/l10n_mx_edi_pos/static/src/app/add_info_popup/add_info_popup.xml:0
#, python-format
msgid "Confirm"
msgstr "Confirmar"

#. module: l10n_mx_edi_pos
#: model:ir.model.fields.selection,name:l10n_mx_edi_pos.selection__pos_order__l10n_mx_edi_usage__i01
msgid "Constructions"
msgstr "Construcciones"

#. module: l10n_mx_edi_pos
#: model:ir.model.fields,field_description:l10n_mx_edi_pos.field_pos_payment_method__country_code
msgid "Country Code"
msgstr "Código de país"

#. module: l10n_mx_edi_pos
#. odoo-python
#: code:addons/l10n_mx_edi_pos/models/pos_order.py:0
#: model:ir.actions.server,name:l10n_mx_edi_pos.action_account_move_create_global_invoice
#, python-format
msgid "Create Global Invoice"
msgstr "Crear factura global"

#. module: l10n_mx_edi_pos
#: model:ir.model,name:l10n_mx_edi_pos.model_l10n_mx_edi_global_invoice_create
msgid "Create a global invoice"
msgstr "Crear una factura global"

#. module: l10n_mx_edi_pos
#: model:ir.model.fields.selection,name:l10n_mx_edi_pos.selection__pos_order__l10n_mx_edi_usage__d09
msgid "Deposits in savings accounts, premiums based on pension plans."
msgstr ""
"Depósitos en cuentas para el ahorro, primas que tengan como base planes de "
"pensiones."

#. module: l10n_mx_edi_pos
#: model:ir.model.fields.selection,name:l10n_mx_edi_pos.selection__pos_order__l10n_mx_edi_usage__i05
msgid "Dices, dies, molds, matrices and tooling"
msgstr "Dados, troqueles, moldes, matrices y herramental"

#. module: l10n_mx_edi_pos
#. odoo-javascript
#: code:addons/l10n_mx_edi_pos/static/src/app/add_info_popup/add_info_popup.xml:0
#, python-format
msgid "Discard"
msgstr "Descartar"

#. module: l10n_mx_edi_pos
#: model:ir.model.fields.selection,name:l10n_mx_edi_pos.selection__pos_order__l10n_mx_edi_usage__d04
msgid "Donations"
msgstr "Donativos"

#. module: l10n_mx_edi_pos
#: model_terms:ir.ui.view,arch_db:l10n_mx_edi_pos.pos_order_form_inherit_l10n_mx_edi
msgid "Download"
msgstr "Descargar"

#. module: l10n_mx_edi_pos
#. odoo-javascript
#: code:addons/l10n_mx_edi_pos/static/src/overrides/models/order.js:0
#: model:ir.model.fields.selection,name:l10n_mx_edi_pos.selection__pos_order__l10n_mx_edi_cfdi_sat_state__error
#, python-format
msgid "Error"
msgstr "Error"

#. module: l10n_mx_edi_pos
#. odoo-python
#: code:addons/l10n_mx_edi_pos/models/pos_order.py:0
#, python-format
msgid "Failed to distribute some negative lines"
msgstr "No fue posible distribuir algunas líneas negativas."

#. module: l10n_mx_edi_pos
#: model:ir.model.fields,field_description:l10n_mx_edi_pos.field_pos_order__l10n_mx_edi_cfdi_uuid
msgid "Fiscal Folio"
msgstr "Folio fiscal"

#. module: l10n_mx_edi_pos
#. odoo-javascript
#: code:addons/l10n_mx_edi_pos/static/src/overrides/components/partner_details_edit/partner_details_edit.xml:0
#, python-format
msgid "Fiscal Regime"
msgstr "Régimen fiscal"

#. module: l10n_mx_edi_pos
#: model:ir.model.fields,help:l10n_mx_edi_pos.field_pos_order__l10n_mx_edi_cfdi_uuid
msgid "Folio in electronic invoice, is returned by SAT when send to stamp."
msgstr ""
"Folio en la factura electrónica, el SAT lo devuelve al enviar para su "
"timbrado."

#. module: l10n_mx_edi_pos
#: model:ir.model.fields.selection,name:l10n_mx_edi_pos.selection__pos_order__l10n_mx_edi_usage__d03
msgid "Funeral expenses"
msgstr "Gastos funerarios"

#. module: l10n_mx_edi_pos
#: model:ir.model.fields.selection,name:l10n_mx_edi_pos.selection__pos_order__l10n_mx_edi_usage__g03
msgid "General expenses"
msgstr "Gastos generales"

#. module: l10n_mx_edi_pos
#. odoo-python
#: code:addons/l10n_mx_edi_pos/models/l10n_mx_edi_document.py:0
#, python-format
msgid "Global Invoice"
msgstr "Factura global"

#. module: l10n_mx_edi_pos
#: model:ir.model.fields,help:l10n_mx_edi_pos.field_pos_payment_method__l10n_mx_edi_payment_method_id
msgid ""
"Indicates the way the invoice was/will be paid, where the options could be: "
"Cash, Nominal Check, Credit Card, etc. Leave empty if unknown and the XML "
"will show 'Unidentified'."
msgstr ""
"Indica la forma en que se pagó o se pagará la factura. Las opciones pueden "
"ser: efectivo, cheque nominal, tarjeta de crédito, etc. Deje en blanco si no "
"conoce el método de pago y el XML mostrará 'No identificado'."

#. module: l10n_mx_edi_pos
#. odoo-javascript
#: code:addons/l10n_mx_edi_pos/static/src/app/add_info_popup/add_info_popup.xml:0
#: code:addons/l10n_mx_edi_pos/static/src/overrides/components/payment_screen/payment_screen.xml:0
#, python-format
msgid "Invoice to public:"
msgstr "Factura a público en general:"

#. module: l10n_mx_edi_pos
#: model:ir.model.fields,field_description:l10n_mx_edi_pos.field_pos_order__l10n_mx_edi_document_ids
msgid "L10N Mx Edi Document"
msgstr "Documento"

#. module: l10n_mx_edi_pos
#: model:ir.model.fields,field_description:l10n_mx_edi_pos.field_pos_order__l10n_mx_edi_is_cfdi_needed
msgid "L10N Mx Edi Is Cfdi Needed"
msgstr "Requiere el CFDI"

#. module: l10n_mx_edi_pos
#: model:ir.model.fields,field_description:l10n_mx_edi_pos.field_pos_order__l10n_mx_edi_update_sat_needed
msgid "L10N Mx Edi Update Sat Needed"
msgstr "Necesita una actualización en el SAT"

#. module: l10n_mx_edi_pos
#: model:ir.model.fields.selection,name:l10n_mx_edi_pos.selection__pos_order__l10n_mx_edi_usage__d08
msgid "Mandatory School Transportation Expenses"
msgstr "Gastos de transportación escolar obligatoria"

#. module: l10n_mx_edi_pos
#: model:ir.model.fields.selection,name:l10n_mx_edi_pos.selection__pos_order__l10n_mx_edi_usage__d02
msgid "Medical expenses for disability"
msgstr "Gastos médicos por incapacidad o discapacidad"

#. module: l10n_mx_edi_pos
#: model:ir.model.fields.selection,name:l10n_mx_edi_pos.selection__pos_order__l10n_mx_edi_usage__d07
msgid "Medical insurance premiums"
msgstr "Primas por seguros de gastos médicos"

#. module: l10n_mx_edi_pos
#: model:ir.model.fields.selection,name:l10n_mx_edi_pos.selection__pos_order__l10n_mx_edi_usage__d01
msgid "Medical, dental and hospital expenses."
msgstr "Honorarios médicos, dentales y gastos hospitalarios."

#. module: l10n_mx_edi_pos
#: model:ir.model,name:l10n_mx_edi_pos.model_l10n_mx_edi_document
msgid "Mexican documents that needs to transit outside of Odoo"
msgstr "Documentos mexicanos que se deben procesar fuera de Odoo"

#. module: l10n_mx_edi_pos
#. odoo-javascript
#: code:addons/l10n_mx_edi_pos/static/src/app/add_info_popup/add_info_popup.xml:0
#, python-format
msgid "No"
msgstr "No"

#. module: l10n_mx_edi_pos
#. odoo-javascript
#: code:addons/l10n_mx_edi_pos/static/src/overrides/components/partner_details_edit/partner_details_edit.xml:0
#, python-format
msgid "No Tax Breakdown"
msgstr "No hay desglose de impuestos"

#. module: l10n_mx_edi_pos
#: model:ir.model.fields.selection,name:l10n_mx_edi_pos.selection__pos_order__l10n_mx_edi_cfdi_sat_state__not_defined
msgid "Not Defined"
msgstr "Sin definir"

#. module: l10n_mx_edi_pos
#: model:ir.model.fields.selection,name:l10n_mx_edi_pos.selection__pos_order__l10n_mx_edi_cfdi_sat_state__not_found
msgid "Not Found"
msgstr "No encontrado"

#. module: l10n_mx_edi_pos
#: model:ir.model.fields.selection,name:l10n_mx_edi_pos.selection__pos_order__l10n_mx_edi_usage__i02
msgid "Office furniture and equipment investment"
msgstr "Inversión en mobiliario y equipo de oficina"

#. module: l10n_mx_edi_pos
#. odoo-python
#: code:addons/l10n_mx_edi_pos/models/pos_order.py:0
#, python-format
msgid "On %s: %s"
msgstr "En %s: %s"

#. module: l10n_mx_edi_pos
#. odoo-python
#: code:addons/l10n_mx_edi_pos/models/pos_order.py:0
#, python-format
msgid ""
"Order lines having a negative amount are not allowed to generate the CFDI."
msgstr ""
"Las líneas de la orden que tengan un importe negativo no tienen autorización "
"para generar el CFDI."

#. module: l10n_mx_edi_pos
#. odoo-python
#: code:addons/l10n_mx_edi_pos/models/pos_order.py:0
#, python-format
msgid ""
"Order lines having a negative amount without a tax set is not allowed to "
"generate the CFDI."
msgstr ""
"Las líneas de la orden que tengan un importe negativo sin un impuesto "
"establecido no tienen autorización para generar el CFDI."

#. module: l10n_mx_edi_pos
#. odoo-python
#: code:addons/l10n_mx_edi_pos/models/pos_order.py:0
#, python-format
msgid "Orders %s are already sent or not eligible for CFDI."
msgstr "Ya se enviaron las órdenes %s o no son elegibles para CFDI."

#. module: l10n_mx_edi_pos
#: model:ir.model.fields.selection,name:l10n_mx_edi_pos.selection__pos_order__l10n_mx_edi_usage__i08
msgid "Other machinery and equipment"
msgstr "Otra maquinaria y equipo"

#. module: l10n_mx_edi_pos
#: model:ir.model.fields,field_description:l10n_mx_edi_pos.field_pos_order__l10n_mx_edi_payment_method_id
#: model:ir.model.fields,field_description:l10n_mx_edi_pos.field_pos_payment_method__l10n_mx_edi_payment_method_id
msgid "Payment Way"
msgstr "Forma de pago"

#. module: l10n_mx_edi_pos
#: model:ir.model.fields.selection,name:l10n_mx_edi_pos.selection__pos_order__l10n_mx_edi_usage__d10
msgid "Payments for educational services (Colegiatura)"
msgstr "Pagos por servicios educativos (colegiaturas)"

#. module: l10n_mx_edi_pos
#: model:ir.model,name:l10n_mx_edi_pos.model_pos_order_line
msgid "Point of Sale Order Lines"
msgstr "Líneas de orden del Punto de venta"

#. module: l10n_mx_edi_pos
#: model:ir.model,name:l10n_mx_edi_pos.model_pos_order
msgid "Point of Sale Orders"
msgstr "Órdenes del punto de venta"

#. module: l10n_mx_edi_pos
#: model:ir.model,name:l10n_mx_edi_pos.model_pos_payment_method
msgid "Point of Sale Payment Methods"
msgstr "Métodos de pago del punto de venta"

#. module: l10n_mx_edi_pos
#: model:ir.model,name:l10n_mx_edi_pos.model_pos_session
msgid "Point of Sale Session"
msgstr "Sesión del punto de venta"

#. module: l10n_mx_edi_pos
#: model:ir.model.fields,field_description:l10n_mx_edi_pos.field_l10n_mx_edi_document__pos_order_ids
#: model:ir.model.fields,field_description:l10n_mx_edi_pos.field_l10n_mx_edi_global_invoice_create__pos_order_ids
msgid "Pos Order"
msgstr "Orden del punto de venta"

#. module: l10n_mx_edi_pos
#: model:ir.model.fields.selection,name:l10n_mx_edi_pos.selection__pos_order__l10n_mx_edi_usage__d05
msgid "Real interest effectively paid for mortgage loans (room house)"
msgstr ""
"Intereses reales efectivamente pagados por créditos hipotecarios (casa "
"habitación)"

#. module: l10n_mx_edi_pos
#: model_terms:ir.ui.view,arch_db:l10n_mx_edi_pos.pos_order_form_inherit_l10n_mx_edi
msgid "Retry"
msgstr "Reintentar"

#. module: l10n_mx_edi_pos
#: model:ir.model.fields.selection,name:l10n_mx_edi_pos.selection__pos_order__l10n_mx_edi_usage__g02
msgid "Returns, discounts or bonuses"
msgstr "Devoluciones, descuentos o bonificaciones"

#. module: l10n_mx_edi_pos
#: model:ir.model.fields,field_description:l10n_mx_edi_pos.field_pos_order__l10n_mx_edi_cfdi_sat_state
msgid "SAT status"
msgstr "Estado SAT"

#. module: l10n_mx_edi_pos
#: model:ir.model.fields.selection,name:l10n_mx_edi_pos.selection__pos_order__l10n_mx_edi_usage__i07
msgid "Satellite communications"
msgstr "Comunicaciones satelitales"

#. module: l10n_mx_edi_pos
#: model:ir.model.fields,help:l10n_mx_edi_pos.field_pos_order__l10n_mx_edi_cfdi_to_public
msgid "Send the CFDI with recipient 'publico en general'"
msgstr "Enviar el CFDI con destinatario 'público en general'"

#. module: l10n_mx_edi_pos
#: model_terms:ir.ui.view,arch_db:l10n_mx_edi_pos.pos_order_form_inherit_l10n_mx_edi
msgid "Show"
msgstr "Mostrar"

#. module: l10n_mx_edi_pos
#: model:ir.model.fields.selection,name:l10n_mx_edi_pos.selection__pos_order__l10n_mx_edi_cfdi_state__sent
msgid "Signed"
msgstr "Firmado"

#. module: l10n_mx_edi_pos
#: model:ir.model.fields.selection,name:l10n_mx_edi_pos.selection__pos_order__l10n_mx_edi_cfdi_state__global_sent
msgid "Signed Global"
msgstr "Global firmado"

#. module: l10n_mx_edi_pos
#: model:ir.model.fields.selection,name:l10n_mx_edi_pos.selection__pos_order__l10n_mx_edi_usage__i06
msgid "Telephone communications"
msgstr "Comunicaciones telefónicas"

#. module: l10n_mx_edi_pos
#: model:ir.model.fields,help:l10n_mx_edi_pos.field_pos_payment_method__country_code
msgid ""
"The ISO country code in two chars. \n"
"You can use this field for quick search."
msgstr ""
"El código ISO del país en dos caracteres.\n"
"Puede utilizar este campo para realizar una búsqueda rápida."

#. module: l10n_mx_edi_pos
#. odoo-python
#: code:addons/l10n_mx_edi_pos/models/pos_order.py:0
#, python-format
msgid ""
"The SAT does not provide information for the currency %s.\n"
"You must get manually a key from the PAC to confirm the currency rate is "
"accurate enough."
msgstr ""
"El SAT no proporciona información sobre la divisa %s.\n"
"Debe obtener una clave del PAC de forma manual para confirmar que la tasa de "
"cambio es precisa."

#. module: l10n_mx_edi_pos
#. odoo-javascript
#. odoo-python
#: code:addons/l10n_mx_edi_pos/models/pos_order.py:0
#: code:addons/l10n_mx_edi_pos/static/src/overrides/models/order.js:0
#, python-format
msgid ""
"The amount of the order must be positive for a sale and negative for a "
"refund."
msgstr ""
"El importe de la orden debe ser positivo para una venta y negativo para un "
"reembolso."

#. module: l10n_mx_edi_pos
#: model:ir.model.fields,help:l10n_mx_edi_pos.field_pos_order__l10n_mx_edi_usage
msgid ""
"The code that corresponds to the use that will be made of the receipt by the "
"recipient."
msgstr ""
"El código que corresponde al uso que el destinatario le dará a la factura."

#. module: l10n_mx_edi_pos
#: model:ir.model.fields.selection,name:l10n_mx_edi_pos.selection__pos_order__l10n_mx_edi_usage__i03
msgid "Transportation equipment"
msgstr "Equipo de transporte"

#. module: l10n_mx_edi_pos
#: model_terms:ir.ui.view,arch_db:l10n_mx_edi_pos.pos_order_form_inherit_l10n_mx_edi
msgid "Update SAT"
msgstr "Actualizar en SAT"

#. module: l10n_mx_edi_pos
#: model:ir.model.fields,field_description:l10n_mx_edi_pos.field_pos_order__l10n_mx_edi_usage
msgid "Usage"
msgstr "Uso"

#. module: l10n_mx_edi_pos
#. odoo-javascript
#: code:addons/l10n_mx_edi_pos/static/src/app/add_info_popup/add_info_popup.xml:0
#: code:addons/l10n_mx_edi_pos/static/src/overrides/components/payment_screen/payment_screen.xml:0
#, python-format
msgid "Usage:"
msgstr "Uso:"

#. module: l10n_mx_edi_pos
#: model:ir.model.fields.selection,name:l10n_mx_edi_pos.selection__pos_order__l10n_mx_edi_cfdi_sat_state__valid
msgid "Validated"
msgstr "Validado"

#. module: l10n_mx_edi_pos
#: model:ir.model.fields.selection,name:l10n_mx_edi_pos.selection__pos_order__l10n_mx_edi_usage__d06
msgid "Voluntary contributions to SAR"
msgstr "Aportaciones voluntarias al SAR"

#. module: l10n_mx_edi_pos
#: model:ir.model.fields.selection,name:l10n_mx_edi_pos.selection__pos_order__l10n_mx_edi_usage__s01
msgid "Without fiscal effects"
msgstr "Sin efectos fiscales"

#. module: l10n_mx_edi_pos
#. odoo-javascript
#: code:addons/l10n_mx_edi_pos/static/src/app/add_info_popup/add_info_popup.xml:0
#, python-format
msgid "Yes"
msgstr "Sí"

#. module: l10n_mx_edi_pos
#. odoo-python
#: code:addons/l10n_mx_edi_pos/models/pos_order.py:0
#, python-format
msgid "You can only process orders sharing the same company."
msgstr "Solo puede procesar órdenes que comparten la misma empresa."

#. module: l10n_mx_edi_pos
#. odoo-python
#: code:addons/l10n_mx_edi_pos/models/pos_order.py:0
#, python-format
msgid ""
"You cannot invoice this refund since the related orders are not invoiced yet."
msgstr ""
"No puede facturar este reembolso. Las órdenes relacionadas aún no se "
"facturan."
