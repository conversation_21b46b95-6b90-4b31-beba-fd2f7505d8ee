# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* data_merge
# 
# Translators:
# Wil Odoo, 2023
# <PERSON><PERSON>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-10-26 22:20+0000\n"
"PO-Revision-Date: 2023-10-26 23:10+0000\n"
"Last-Translator: Jun<PERSON>, 2023\n"
"Language-Team: Japanese (https://app.transifex.com/odoo/teams/41243/ja/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ja\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: data_merge
#. odoo-python
#: code:addons/data_merge/models/data_merge_model.py:0
#, python-format
msgid "%s (copy)"
msgstr "%s (コピー)"

#. module: data_merge
#. odoo-python
#: code:addons/data_merge/models/data_merge_group.py:0
#, python-format
msgid "%s - Similarity: %s%%"
msgstr "%s - 類似性: %s%%"

#. module: data_merge
#. odoo-javascript
#: code:addons/data_merge/static/src/views/data_merge_list_view.js:0
#, python-format
msgid "%s records have been merged"
msgstr "%sレコードがマージされました "

#. module: data_merge
#: model_terms:ir.ui.view,arch_db:data_merge.data_merge_duplicate
msgid ""
"' deduplication rule.<br/>\n"
"You can merge them"
msgstr ""
"'重複規則。<br/>\n"
"それらをマージできます。"

#. module: data_merge
#: model_terms:ir.ui.view,arch_db:data_merge.view_data_merge_model_form
msgid "<span class=\"me-1\">Every</span>"
msgstr "<span class=\"me-1\">毎</span>"

#. module: data_merge
#: model_terms:ir.ui.view,arch_db:data_merge.view_data_merge_model_form
msgid "<span class=\"oe_inline\">%</span>"
msgstr "<span class=\"oe_inline\">%</span>"

#. module: data_merge
#: model_terms:ir.ui.view,arch_db:data_merge.view_data_merge_model_form
msgid "<span invisible=\"not custom_merge_method\">Model specific</span>"
msgstr "<span invisible=\"not custom_merge_method\">モデル固有</span>"

#. module: data_merge
#: model:ir.model.constraint,message:data_merge.constraint_data_merge_rule_uniq_model_id_field_id
msgid "A field can only appear once!"
msgstr "フィールドは1度しか表示されません！"

#. module: data_merge
#: model:ir.model.fields,field_description:data_merge.field_data_merge_group__active
#: model:ir.model.fields,field_description:data_merge.field_data_merge_model__active
#: model:ir.model.fields,field_description:data_merge.field_data_merge_record__active
msgid "Active"
msgstr "有効化"

#. module: data_merge
#: model:ir.model.fields.selection,name:data_merge.selection__data_merge_model__removal_mode__archive
msgid "Archive"
msgstr "アーカイブ"

#. module: data_merge
#: model_terms:ir.ui.view,arch_db:data_merge.data_merge_model_view_search
#: model_terms:ir.ui.view,arch_db:data_merge.view_data_merge_record_search
msgid "Archived"
msgstr "アーカイブ済"

#. module: data_merge
#. odoo-javascript
#: code:addons/data_merge/static/src/views/data_merge_list_view.js:0
#, python-format
msgid ""
"Are you sure that you want to merge the selected records in their respective"
" group?"
msgstr "選択したレコードをそれぞれのグループにマージするで、間違いありませんか？"

#. module: data_merge
#. odoo-javascript
#: code:addons/data_merge/static/src/views/data_merge_list_view.js:0
#, python-format
msgid "Are you sure that you want to merge these records?"
msgstr "本当にこれらのレコードをマージしますか？"

#. module: data_merge
#: model:ir.model.fields.selection,name:data_merge.selection__data_merge_model__merge_mode__automatic
msgid "Automatic"
msgstr "自動"

#. module: data_merge
#: model:ir.model.fields,field_description:data_merge.field_ir_model__is_merge_enabled
#: model_terms:ir.ui.view,arch_db:data_merge.ir_model_view_search
msgid "Can Be Merged"
msgstr "マージ可"

#. module: data_merge
#. odoo-python
#: code:addons/data_merge/models/data_merge_rule.py:0
#, python-format
msgid "Case/Accent Insensitive Match"
msgstr "大文字/小文字を区別しない一致"

#. module: data_merge
#: model:ir.model.fields,field_description:data_merge.field_data_merge_record__company_id
msgid "Company"
msgstr "会社"

#. module: data_merge
#: model_terms:ir.actions.act_window,help:data_merge.action_data_merge_record
#: model_terms:ir.actions.act_window,help:data_merge.action_data_merge_record_notification
msgid "Configure rules to identify duplicate records"
msgstr "重複レコードを識別するためのルールを設定してください"

#. module: data_merge
#: model:ir.model,name:data_merge.model_res_partner
msgid "Contact"
msgstr "連絡先"

#. module: data_merge
#: model:ir.model.fields,help:data_merge.field_ir_model__ref_merge_ir_act_server_id
msgid ""
"Contextual menu action that redirects to the deduplicate view of data_merge."
msgstr "data_merge の重複排除ビューにリダイレクトするコンテキストメニューアクション。"

#. module: data_merge
#: model:ir.model.fields,field_description:data_merge.field_data_merge_record__record_create_uid
msgid "Created By"
msgstr "作成者"

#. module: data_merge
#: model:ir.model.fields,field_description:data_merge.field_data_merge_record__record_create_date
msgid "Created On"
msgstr "作成日"

#. module: data_merge
#: model:ir.model.fields,field_description:data_merge.field_data_merge_group__create_uid
#: model:ir.model.fields,field_description:data_merge.field_data_merge_model__create_uid
#: model:ir.model.fields,field_description:data_merge.field_data_merge_record__create_uid
#: model:ir.model.fields,field_description:data_merge.field_data_merge_rule__create_uid
msgid "Created by"
msgstr "作成者"

#. module: data_merge
#: model:ir.model.fields,field_description:data_merge.field_data_merge_group__create_date
#: model:ir.model.fields,field_description:data_merge.field_data_merge_model__create_date
#: model:ir.model.fields,field_description:data_merge.field_data_merge_record__create_date
#: model:ir.model.fields,field_description:data_merge.field_data_merge_rule__create_date
msgid "Created on"
msgstr "作成日"

#. module: data_merge
#: model:ir.model.fields,field_description:data_merge.field_data_merge_model__mix_by_company
msgid "Cross-Company"
msgstr "会社間"

#. module: data_merge
#: model:ir.model.fields,field_description:data_merge.field_data_merge_model__custom_merge_method
msgid "Custom Merge Method"
msgstr "マージ方法をカスタム"

#. module: data_merge
#: model:ir.actions.server,name:data_merge.ir_cron_cleanup_ir_actions_server
msgid "Data Merge: Cleanup Records"
msgstr "データマージ: レコードをクリーンナップ"

#. module: data_merge
#: model:ir.actions.server,name:data_merge.ir_cron_find_duplicates_ir_actions_server
msgid "Data Merge: Find Duplicate Records"
msgstr "データマージ: 重複レコードを見つける"

#. module: data_merge
#: model:ir.model.fields.selection,name:data_merge.selection__data_merge_model__notify_frequency_period__days
msgid "Days"
msgstr "日"

#. module: data_merge
#. odoo-python
#: code:addons/data_merge/models/data_merge_record.py:0
#: model_terms:ir.ui.view,arch_db:data_merge.view_data_merge_model_form
#, python-format
msgid "Deduplicate"
msgstr "重複"

#. module: data_merge
#: model:ir.ui.menu,name:data_merge.menu_data_merge_config_rules_deduplication
#: model:ir.ui.menu,name:data_merge.menu_data_merge_group
msgid "Deduplication"
msgstr "重複"

#. module: data_merge
#: model:ir.model,name:data_merge.model_data_merge_group
msgid "Deduplication Group"
msgstr "重複グループ"

#. module: data_merge
#: model:ir.model,name:data_merge.model_data_merge_model
#: model:ir.model.fields,field_description:data_merge.field_data_merge_group__model_id
#: model:ir.model.fields,field_description:data_merge.field_data_merge_record__model_id
#: model:ir.model.fields,field_description:data_merge.field_data_merge_rule__model_id
msgid "Deduplication Model"
msgstr "重複モデル"

#. module: data_merge
#: model:ir.model,name:data_merge.model_data_merge_record
msgid "Deduplication Record"
msgstr "重複レコード"

#. module: data_merge
#: model:ir.model,name:data_merge.model_data_merge_rule
msgid "Deduplication Rule"
msgstr "重複ルール"

#. module: data_merge
#: model:ir.actions.act_window,name:data_merge.action_data_merge_config
#: model:ir.model.fields,field_description:data_merge.field_data_merge_model__rule_ids
#: model_terms:ir.ui.view,arch_db:data_merge.view_data_merge_model_form
msgid "Deduplication Rules"
msgstr "重複ルール"

#. module: data_merge
#. odoo-python
#: code:addons/data_merge/models/data_merge_model.py:0
#, python-format
msgid "Deduplication is forbidden on the model: %s"
msgstr "モデルでは重複は禁止されています: %s"

#. module: data_merge
#: model:ir.model.fields.selection,name:data_merge.selection__data_merge_model__removal_mode__delete
msgid "Delete"
msgstr "削除"

#. module: data_merge
#: model:ir.model.fields,field_description:data_merge.field_data_merge_record__differences
msgid "Differences"
msgstr "差異"

#. module: data_merge
#: model:ir.model.fields,help:data_merge.field_data_merge_record__differences
msgid "Differences with the master record"
msgstr "マスタレコードとの相違"

#. module: data_merge
#: model_terms:ir.ui.view,arch_db:data_merge.ir_model_view_form
msgid "Disable Merge"
msgstr "マージ無効化"

#. module: data_merge
#: model_terms:ir.ui.view,arch_db:data_merge.view_data_merge_record_list
msgid "Discard"
msgstr "破棄"

#. module: data_merge
#: model_terms:ir.ui.view,arch_db:data_merge.data_merge_record_view_search_merge_action
#: model_terms:ir.ui.view,arch_db:data_merge.view_data_merge_record_search
msgid "Discarded"
msgstr "破棄済"

#. module: data_merge
#: model:ir.model.fields,field_description:data_merge.field_data_merge_group__display_name
#: model:ir.model.fields,field_description:data_merge.field_data_merge_model__display_name
#: model:ir.model.fields,field_description:data_merge.field_data_merge_record__display_name
#: model:ir.model.fields,field_description:data_merge.field_data_merge_rule__display_name
msgid "Display Name"
msgstr "表示名"

#. module: data_merge
#: model:ir.model.fields,field_description:data_merge.field_data_merge_group__divergent_fields
msgid "Divergent Fields"
msgstr "不一致のフィールド"

#. module: data_merge
#: model:ir.model.fields,field_description:data_merge.field_data_merge_model__domain
msgid "Domain"
msgstr "ドメイン"

#. module: data_merge
#: model:ir.model.fields,field_description:data_merge.field_data_merge_model__removal_mode
msgid "Duplicate Removal"
msgstr "重複削除"

#. module: data_merge
#: model:ir.actions.act_window,name:data_merge.action_data_merge_record
#: model:ir.actions.act_window,name:data_merge.action_data_merge_record_notification
#: model_terms:ir.ui.view,arch_db:data_merge.view_data_merge_model_form
msgid "Duplicates"
msgstr "重複"

#. module: data_merge
#. odoo-python
#: code:addons/data_merge/models/data_merge_model.py:0
#, python-format
msgid "Duplicates to Merge"
msgstr "マージする重複"

#. module: data_merge
#: model:ir.model.fields,help:data_merge.field_data_merge_model__create_threshold
msgid ""
"Duplicates with a similarity below this threshold will not be suggested"
msgstr "このしきい値を下回る類似度の重複は提案されません。"

#. module: data_merge
#: model_terms:ir.ui.view,arch_db:data_merge.ir_model_view_form
msgid "Enable Merge"
msgstr "マージ有効化"

#. module: data_merge
#. odoo-python
#: code:addons/data_merge/models/data_merge_rule.py:0
#, python-format
msgid "Exact Match"
msgstr "完全一致"

#. module: data_merge
#: model:ir.model.fields,field_description:data_merge.field_data_merge_record__field_values
msgid "Field Values"
msgstr "項目値"

#. module: data_merge
#: model:ir.model.fields,field_description:data_merge.field_ir_model__hide_merge_action
msgid "Hide merge action button"
msgstr "マージアクションボタンを隠す"

#. module: data_merge
#: model_terms:ir.ui.view,arch_db:data_merge.data_merge_duplicate
msgid "I've identified"
msgstr "以下を識別しました:"

#. module: data_merge
#: model:ir.model.fields,field_description:data_merge.field_data_merge_group__id
#: model:ir.model.fields,field_description:data_merge.field_data_merge_model__id
#: model:ir.model.fields,field_description:data_merge.field_data_merge_record__id
#: model:ir.model.fields,field_description:data_merge.field_data_merge_rule__id
#: model_terms:ir.ui.view,arch_db:data_merge.view_data_merge_record_list
msgid "ID"
msgstr "ID"

#. module: data_merge
#: model:ir.model.fields,help:data_merge.field_ir_model__is_merge_enabled
msgid ""
"If True, the generic data merge tool is available in the contextual menu of "
"this model."
msgstr "TRUEの場合、このモデルのコンテクストメニューで汎用データマージツールが利用できます。"

#. module: data_merge
#: model:ir.model.fields,help:data_merge.field_data_merge_model__is_contextual_merge_action
msgid ""
"If True, this record is used for contextual menu action \"Merge\" on the "
"target model."
msgstr "TRUEの場合、このレコードはターゲットモデルのコンテキストメニューのアクション\"マージ\"に使用されます。"

#. module: data_merge
#: model:ir.model.fields,help:data_merge.field_ir_model__hide_merge_action
msgid ""
"If the model already has a custom merge method, the class attribute `_merge_disabled` is set to true on\n"
"             that model and the generic data merge action should not be available on that model."
msgstr ""
"もしモデルが既にカスタムマージ方法を持っている場合、クラス属性 `_merge_disabled` はそのモデルで\n"
"　　　TRUEに設定され、汎用データマージアクションはそのモデルでは利用できません。"

#. module: data_merge
#: model:ir.model.fields,field_description:data_merge.field_data_merge_record__is_deleted
msgid "Is Deleted"
msgstr "削除済"

#. module: data_merge
#: model:ir.model.fields,field_description:data_merge.field_data_merge_record__is_discarded
msgid "Is Discarded"
msgstr "破棄済"

#. module: data_merge
#: model:ir.model.fields,field_description:data_merge.field_data_merge_record__is_master
msgid "Is Master"
msgstr "マスターレコード"

#. module: data_merge
#: model:ir.model.fields,field_description:data_merge.field_data_merge_model__last_notification
msgid "Last Notification"
msgstr "最終通知"

#. module: data_merge
#: model:ir.model.fields,field_description:data_merge.field_data_merge_group__write_uid
#: model:ir.model.fields,field_description:data_merge.field_data_merge_model__write_uid
#: model:ir.model.fields,field_description:data_merge.field_data_merge_record__write_uid
#: model:ir.model.fields,field_description:data_merge.field_data_merge_rule__write_uid
msgid "Last Updated by"
msgstr "最終更新者"

#. module: data_merge
#: model:ir.model.fields,field_description:data_merge.field_data_merge_group__write_date
#: model:ir.model.fields,field_description:data_merge.field_data_merge_model__write_date
#: model:ir.model.fields,field_description:data_merge.field_data_merge_record__write_date
#: model:ir.model.fields,field_description:data_merge.field_data_merge_rule__write_date
msgid "Last Updated on"
msgstr "最終更新日"

#. module: data_merge
#: model:ir.model.fields,help:data_merge.field_data_merge_record__used_in
msgid "List of other models referencing this record"
msgstr "このレコードを参照する他のモデルのリスト"

#. module: data_merge
#: model:ir.model.fields,help:data_merge.field_data_merge_model__notify_user_ids
msgid "List of users to notify when there are new records to merge"
msgstr "マージする新しいレコードがあるときに通知するユーザのリスト"

#. module: data_merge
#: model:ir.model.fields.selection,name:data_merge.selection__data_merge_model__merge_mode__manual
msgid "Manual"
msgstr "手動"

#. module: data_merge
#. odoo-python
#: code:addons/data_merge/models/data_merge_record.py:0
#, python-format
msgid "Manual Selection - %s"
msgstr "手動選択 - %s"

#. module: data_merge
#. odoo-python
#. odoo-javascript
#: code:addons/data_merge/models/ir_model.py:0
#: code:addons/data_merge/static/src/views/data_merge_list_view.xml:0
#: model:ir.actions.server,name:data_merge.merge_action_crm_lost_reason
#: model:ir.actions.server,name:data_merge.merge_action_crm_tag
#: model:ir.actions.server,name:data_merge.merge_action_helpdesk_tag
#: model:ir.actions.server,name:data_merge.merge_action_helpdesk_ticket
#: model:ir.actions.server,name:data_merge.merge_action_project_tags
#: model:ir.actions.server,name:data_merge.merge_action_project_task
#: model:ir.actions.server,name:data_merge.merge_action_res_country
#: model:ir.actions.server,name:data_merge.merge_action_res_country_state
#: model:ir.actions.server,name:data_merge.merge_action_res_partner_category
#: model:ir.actions.server,name:data_merge.merge_action_res_partner_industry
#: model:ir.actions.server,name:data_merge.merge_action_res_partner_title
#: model:ir.actions.server,name:data_merge.merge_action_utm_campaign
#: model:ir.actions.server,name:data_merge.merge_action_utm_medium
#: model:ir.actions.server,name:data_merge.merge_action_utm_source
#: model_terms:ir.ui.view,arch_db:data_merge.view_data_merge_record_list
#, python-format
msgid "Merge"
msgstr "統合"

#. module: data_merge
#: model:ir.ui.menu,name:data_merge.ir_model_menu_merge_action_manager
msgid "Merge Action Manager"
msgstr "マージアクション管理"

#. module: data_merge
#: model:ir.model.fields,field_description:data_merge.field_data_merge_rule__match_mode
msgid "Merge If"
msgstr "マージ条件"

#. module: data_merge
#: model:ir.model.fields,field_description:data_merge.field_data_merge_model__merge_mode
msgid "Merge Mode"
msgstr "マージモード"

#. module: data_merge
#: model:ir.model.fields,field_description:data_merge.field_ir_model__ref_merge_ir_act_server_id
msgid "Merge Server Action"
msgstr "サーバアクションをマージ"

#. module: data_merge
#: model:ir.actions.act_window,name:data_merge.ir_model_action_merge
msgid "Merge action Manager"
msgstr "マージアクション管理者"

#. module: data_merge
#: model:ir.model.fields,field_description:data_merge.field_data_merge_model__is_contextual_merge_action
msgid "Merge action attached"
msgstr "添付済アクションをマージ"

#. module: data_merge
#. odoo-python
#: code:addons/data_merge/models/data_merge_model.py:0
#, python-format
msgid "Missing required PostgreSQL extension: unaccent"
msgstr "必要な PostgreSQL 拡張モジュールがありません: unaccent"

#. module: data_merge
#: model:ir.model.fields,field_description:data_merge.field_data_merge_group__res_model_id
#: model:ir.model.fields,field_description:data_merge.field_data_merge_model__res_model_id
#: model:ir.model.fields,field_description:data_merge.field_data_merge_record__res_model_id
#: model:ir.model.fields,field_description:data_merge.field_data_merge_rule__res_model_id
#: model_terms:ir.ui.view,arch_db:data_merge.data_merge_record_view_search_merge_action
#: model_terms:ir.ui.view,arch_db:data_merge.view_data_merge_record_search
msgid "Model"
msgstr "モデル"

#. module: data_merge
#: model:ir.model.fields,field_description:data_merge.field_data_merge_group__res_model_name
#: model:ir.model.fields,field_description:data_merge.field_data_merge_model__res_model_name
#: model:ir.model.fields,field_description:data_merge.field_data_merge_record__res_model_name
msgid "Model Name"
msgstr "モデル名"

#. module: data_merge
#: model:ir.model,name:data_merge.model_ir_model
msgid "Models"
msgstr "モデル"

#. module: data_merge
#: model:ir.model.fields.selection,name:data_merge.selection__data_merge_model__notify_frequency_period__months
msgid "Months"
msgstr "月"

#. module: data_merge
#: model:ir.model.fields,field_description:data_merge.field_data_merge_model__name
#: model:ir.model.fields,field_description:data_merge.field_data_merge_record__name
msgid "Name"
msgstr "名称"

#. module: data_merge
#. odoo-python
#: code:addons/data_merge/models/data_merge_record.py:0
#: model_terms:ir.actions.act_window,help:data_merge.action_data_merge_record
#: model_terms:ir.actions.act_window,help:data_merge.action_data_merge_record_notification
#, python-format
msgid "No duplicates found"
msgstr "重複は見つかりませんでした"

#. module: data_merge
#: model:ir.model.fields,field_description:data_merge.field_data_merge_model__notify_frequency
msgid "Notify"
msgstr "通知"

#. module: data_merge
#: model:ir.model.fields,field_description:data_merge.field_data_merge_model__notify_frequency_period
msgid "Notify Frequency Period"
msgstr "通知頻度期間"

#. module: data_merge
#: model:ir.model.fields,field_description:data_merge.field_data_merge_model__notify_user_ids
msgid "Notify Users"
msgstr "ユーザ通知"

#. module: data_merge
#. odoo-python
#: code:addons/data_merge/models/data_merge_record.py:0
#: code:addons/data_merge/models/data_merge_record.py:0
#, python-format
msgid "Query Failed."
msgstr "クエリーに失敗しました。"

#. module: data_merge
#: model:ir.model.fields,field_description:data_merge.field_data_merge_group__record_ids
msgid "Record"
msgstr "レコード"

#. module: data_merge
#: model:ir.model.fields,field_description:data_merge.field_data_merge_record__group_id
msgid "Record Group"
msgstr "レコードグループ"

#. module: data_merge
#: model:ir.model.fields,field_description:data_merge.field_data_merge_record__res_id
msgid "Record ID"
msgstr "レコードID"

#. module: data_merge
#: model_terms:ir.ui.view,arch_db:data_merge.data_merge_record_view_search_merge_action
#: model_terms:ir.ui.view,arch_db:data_merge.view_data_merge_record_search
msgid "Records"
msgstr "レコード"

#. module: data_merge
#: model:ir.model.fields,field_description:data_merge.field_data_merge_model__records_to_merge_count
msgid "Records To Merge Count"
msgstr "マージするレコード数"

#. module: data_merge
#: model:ir.model.fields,help:data_merge.field_data_merge_model__domain
msgid "Records eligible for the deduplication process"
msgstr "重複除外処理の対象となるレコード"

#. module: data_merge
#. odoo-python
#: code:addons/data_merge/models/data_merge_record.py:0
#, python-format
msgid "Records must be of the same model"
msgstr "レコードは同一モデルのものである必要があります。"

#. module: data_merge
#: model:ir.model.fields,help:data_merge.field_data_merge_model__merge_threshold
msgid ""
"Records with a similarity percentage above this threshold will be "
"automatically merged"
msgstr "このしきい値以上の類似度を持つレコードは自動的にマージされます。"

#. module: data_merge
#: model_terms:ir.ui.view,arch_db:data_merge.view_data_merge_record_search
msgid "Rule"
msgstr "ルール"

#. module: data_merge
#: model_terms:ir.ui.view,arch_db:data_merge.view_data_merge_model_form
msgid "Select a model to configure deduplication rules"
msgstr "モデルを選択して重複規則を設定します。"

#. module: data_merge
#: model:ir.model.fields,field_description:data_merge.field_data_merge_rule__sequence
msgid "Sequence"
msgstr "シーケンス"

#. module: data_merge
#: model:ir.model.fields,field_description:data_merge.field_data_merge_group__similarity
msgid "Similarity %"
msgstr "類似性 %"

#. module: data_merge
#: model:ir.model.fields,field_description:data_merge.field_data_merge_model__merge_threshold
msgid "Similarity Threshold"
msgstr "類似性しきい値"

#. module: data_merge
#: model:ir.model.fields,help:data_merge.field_data_merge_group__similarity
msgid ""
"Similarity coefficient based on the amount of text fields exactly in common."
msgstr "テキストフィールドが正確に共通している量に基づく類似度係数。"

#. module: data_merge
#: model:ir.model.fields,help:data_merge.field_data_merge_model__rule_ids
msgid "Suggest to merge records matching at least one of these rules"
msgstr "これらの規則の少なくとも1つに一致するレコードをマージするよう提案します。"

#. module: data_merge
#: model:ir.model.fields,field_description:data_merge.field_data_merge_model__create_threshold
msgid "Suggestion Threshold"
msgstr "提案のしきい値"

#. module: data_merge
#: model:ir.model.constraint,message:data_merge.constraint_data_merge_model_check_notif_freq
msgid "The notification frequency should be greater than 0"
msgstr "通知頻度は0より大きくする必要があります。"

#. module: data_merge
#. odoo-python
#: code:addons/data_merge/models/data_merge_record.py:0
#, python-format
msgid "The referenced record does not exist"
msgstr "参照されたレコードは存在しません。"

#. module: data_merge
#. odoo-javascript
#: code:addons/data_merge/static/src/views/data_merge_list_view.js:0
#, python-format
msgid "The selected records have been merged"
msgstr "選択されたレコードがマージされました。"

#. module: data_merge
#. odoo-python
#: code:addons/data_merge/models/data_merge_record.py:0
#, python-format
msgid "The target model does not exists."
msgstr "ターゲットモデルは存在しません。"

#. module: data_merge
#. odoo-python
#: code:addons/data_merge/models/data_merge_record.py:0
#, python-format
msgid "There is not referenced record"
msgstr "参照されたレコードがありません。"

#. module: data_merge
#: model:ir.model.constraint,message:data_merge.constraint_data_merge_model_uniq_name
msgid "This name is already taken"
msgstr "この名前は既に使用されています。"

#. module: data_merge
#: model:ir.model.fields,field_description:data_merge.field_data_merge_rule__field_id
msgid "Unique ID Field"
msgstr "ユニークID項目"

#. module: data_merge
#. odoo-javascript
#: code:addons/data_merge/static/src/views/data_merge_list_view.xml:0
#, python-format
msgid "Unselect"
msgstr "選択解除"

#. module: data_merge
#: model:ir.model.fields,field_description:data_merge.field_data_merge_record__record_write_uid
msgid "Updated By"
msgstr "更新者:"

#. module: data_merge
#: model:ir.model.fields,field_description:data_merge.field_data_merge_record__record_write_date
msgid "Updated On"
msgstr "更新日:"

#. module: data_merge
#: model:ir.model.fields,field_description:data_merge.field_data_merge_record__used_in
msgid "Used In"
msgstr "使用箇所"

#. module: data_merge
#: model:ir.model.fields.selection,name:data_merge.selection__data_merge_model__notify_frequency_period__weeks
msgid "Weeks"
msgstr "週"

#. module: data_merge
#: model:ir.model.fields,help:data_merge.field_data_merge_model__mix_by_company
msgid "When enabled, duplicates across different companies will be suggested"
msgstr "有効にすると、異なる会社間での重複が提案されます。"

#. module: data_merge
#. odoo-python
#: code:addons/data_merge/models/data_merge_record.py:0
#, python-format
msgid "You must select at least two %s in order to merge them."
msgstr "マージするには少なくとも2つの%sを選択する必要があります。"

#. module: data_merge
#: model_terms:ir.ui.view,arch_db:data_merge.data_merge_duplicate
msgid "duplicate records with the '"
msgstr "以下と重複したレコード:"

#. module: data_merge
#: model_terms:ir.ui.view,arch_db:data_merge.data_merge_duplicate
msgid "here"
msgstr "クリック"

#. module: data_merge
#: model_terms:ir.ui.view,arch_db:data_merge.data_merge_merged
msgid "merged into"
msgstr "以下にマージ済"

#. module: data_merge
#: model_terms:ir.ui.view,arch_db:data_merge.data_merge_main
msgid "merged into this one"
msgstr "これにマージ済:"
