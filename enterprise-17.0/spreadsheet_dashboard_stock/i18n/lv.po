# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* spreadsheet_dashboard_stock
# 
# Translators:
# <AUTHOR> <EMAIL>, 2023
# <PERSON>, 2023
# <PERSON><PERSON><PERSON> <arn<PERSON>@allegro.lv>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-10-26 22:21+0000\n"
"PO-Revision-Date: 2023-10-26 23:10+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON> <<EMAIL>>, 2023\n"
"Language-Team: Latvian (https://app.transifex.com/odoo/teams/41243/lv/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: lv\n"
"Plural-Forms: nplurals=3; plural=(n%10==1 && n%100!=11 ? 0 : n != 0 ? 1 : 2);\n"

#. module: spreadsheet_dashboard_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock/data/files/inventory_flow_dashboard.json:0
#: code:addons/spreadsheet_dashboard_stock/data/files/inventory_flow_dashboard.json:0
#, python-format
msgid " days"
msgstr " dienas"

#. module: spreadsheet_dashboard_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock/data/files/inventory_flow_dashboard.json:0
#, python-format
msgid "Average Cycle Time"
msgstr ""

#. module: spreadsheet_dashboard_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock/data/files/inventory_flow_dashboard.json:0
#, python-format
msgid "Average Delay"
msgstr ""

#. module: spreadsheet_dashboard_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock/data/files/inventory_flow_dashboard.json:0
#, python-format
msgid "Avg cycle time"
msgstr ""

#. module: spreadsheet_dashboard_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock/data/files/inventory_flow_dashboard.json:0
#, python-format
msgid "Avg delay"
msgstr ""

#. module: spreadsheet_dashboard_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock/data/files/inventory_flow_dashboard.json:0
#: code:addons/spreadsheet_dashboard_stock/data/files/inventory_flow_dashboard.json:0
#, python-format
msgid "Current"
msgstr "Tekošais"

#. module: spreadsheet_dashboard_stock
#: model:spreadsheet.dashboard,name:spreadsheet_dashboard_stock.spreadsheet_dashboard_inventory_flow
msgid "Inventory Flow"
msgstr ""

#. module: spreadsheet_dashboard_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock/data/files/inventory_flow_dashboard.json:0
#, python-format
msgid "KPI"
msgstr ""

#. module: spreadsheet_dashboard_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock/data/files/inventory_flow_dashboard.json:0
#: code:addons/spreadsheet_dashboard_stock/data/files/inventory_flow_dashboard.json:0
#, python-format
msgid "Location"
msgstr "Atrašanās vieta"

#. module: spreadsheet_dashboard_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock/data/files/inventory_flow_dashboard.json:0
#, python-format
msgid "Moved Quantity"
msgstr ""

#. module: spreadsheet_dashboard_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock/data/files/inventory_flow_dashboard.json:0
#: code:addons/spreadsheet_dashboard_stock/data/files/inventory_flow_dashboard.json:0
#: code:addons/spreadsheet_dashboard_stock/data/files/inventory_flow_dashboard.json:0
#: code:addons/spreadsheet_dashboard_stock/data/files/inventory_flow_dashboard.json:0
#, python-format
msgid "Moves"
msgstr "Krājumu pārvietošana"

#. module: spreadsheet_dashboard_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock/data/files/inventory_flow_dashboard.json:0
#: code:addons/spreadsheet_dashboard_stock/data/files/inventory_flow_dashboard.json:0
#, python-format
msgid "Partner"
msgstr "Kontaktpersona"

#. module: spreadsheet_dashboard_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock/data/files/inventory_flow_dashboard.json:0
#, python-format
msgid "Period"
msgstr "Periods"

#. module: spreadsheet_dashboard_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock/data/files/inventory_flow_dashboard.json:0
#: code:addons/spreadsheet_dashboard_stock/data/files/inventory_flow_dashboard.json:0
#, python-format
msgid "Previous"
msgstr "Iepriekšējais"

#. module: spreadsheet_dashboard_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock/data/files/inventory_flow_dashboard.json:0
#: code:addons/spreadsheet_dashboard_stock/data/files/inventory_flow_dashboard.json:0
#, python-format
msgid "Product"
msgstr "Produkts"

#. module: spreadsheet_dashboard_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock/data/files/inventory_flow_dashboard.json:0
#, python-format
msgid "Product quantity"
msgstr ""

#. module: spreadsheet_dashboard_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock/data/files/inventory_flow_dashboard.json:0
#: code:addons/spreadsheet_dashboard_stock/data/files/inventory_flow_dashboard.json:0
#: code:addons/spreadsheet_dashboard_stock/data/files/inventory_flow_dashboard.json:0
#: code:addons/spreadsheet_dashboard_stock/data/files/inventory_flow_dashboard.json:0
#, python-format
msgid "Quantity"
msgstr "Daudzums"

#. module: spreadsheet_dashboard_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock/data/files/inventory_flow_dashboard.json:0
#, python-format
msgid "Stock Moves Analysis by Destination Address "
msgstr ""

#. module: spreadsheet_dashboard_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock/data/files/inventory_flow_dashboard.json:0
#, python-format
msgid "Stock Moves Analysis by Destination Location"
msgstr ""

#. module: spreadsheet_dashboard_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock/data/files/inventory_flow_dashboard.json:0
#, python-format
msgid "Stock Moves Analysis by Product"
msgstr ""

#. module: spreadsheet_dashboard_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock/data/files/inventory_flow_dashboard.json:0
#, python-format
msgid "Stock Moves Analysis by Warehouse"
msgstr ""

#. module: spreadsheet_dashboard_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock/data/files/inventory_flow_dashboard.json:0
#, python-format
msgid "Top Locations"
msgstr ""

#. module: spreadsheet_dashboard_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock/data/files/inventory_flow_dashboard.json:0
#, python-format
msgid "Top Partners"
msgstr ""

#. module: spreadsheet_dashboard_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock/data/files/inventory_flow_dashboard.json:0
#, python-format
msgid "Top Products"
msgstr ""

#. module: spreadsheet_dashboard_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock/data/files/inventory_flow_dashboard.json:0
#, python-format
msgid "Top Warehouses"
msgstr ""

#. module: spreadsheet_dashboard_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock/data/files/inventory_flow_dashboard.json:0
#: code:addons/spreadsheet_dashboard_stock/data/files/inventory_flow_dashboard.json:0
#, python-format
msgid "Warehouse"
msgstr "Noliktava"

#. module: spreadsheet_dashboard_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock/data/files/inventory_flow_dashboard.json:0
#, python-format
msgid "Weekly Stock Moves"
msgstr ""

#. module: spreadsheet_dashboard_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock/data/files/inventory_flow_dashboard.json:0
#: code:addons/spreadsheet_dashboard_stock/data/files/inventory_flow_dashboard.json:0
#, python-format
msgid "last period"
msgstr ""

#. module: spreadsheet_dashboard_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock/data/files/inventory_flow_dashboard.json:0
#, python-format
msgid "since last period"
msgstr ""

#. module: spreadsheet_dashboard_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock/data/files/inventory_flow_dashboard.json:0
#, python-format
msgid "stats - current"
msgstr ""

#. module: spreadsheet_dashboard_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock/data/files/inventory_flow_dashboard.json:0
#, python-format
msgid "stats - previous"
msgstr ""
