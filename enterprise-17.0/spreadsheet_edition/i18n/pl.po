# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* spreadsheet_edition
# 
# Translators:
# Wil Odoo, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-01-05 13:47+0000\n"
"PO-Revision-Date: 2023-10-26 23:10+0000\n"
"Last-Translator: Wil Odoo, 2024\n"
"Language-Team: Polish (https://app.transifex.com/odoo/teams/41243/pl/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: pl\n"
"Plural-Forms: nplurals=4; plural=(n==1 ? 0 : (n%10>=2 && n%10<=4) && (n%100<12 || n%100>14) ? 1 : n!=1 && (n%10>=0 && n%10<=1) || (n%10>=5 && n%10<=9) || (n%100>=12 && n%100<=14) ? 2 : 3);\n"

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/assets/list_view/list_renderer.js:0
#, python-format
msgid "%(field name)s by %(order)s"
msgstr ""

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/bundle/actions/version_history/version_history_action.js:0
#, python-format
msgid "%(name)s (restored from %(timestamp)s)"
msgstr ""

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/assets/pivot_view/pivot_view.js:0
#, python-format
msgid "%(name)s by %(field)s"
msgstr ""

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/assets/pivot_view/pivot_view.js:0
#, python-format
msgid "%(name)s by %(field)s (%(period)s)"
msgstr ""

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/bundle/list/side_panels/listing_details_side_panel.js:0
#, python-format
msgid "%(sortName)s (ascending)"
msgstr ""

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/bundle/list/side_panels/listing_details_side_panel.js:0
#, python-format
msgid "%(sortName)s (descending)"
msgstr ""

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/bundle/actions/control_panel/spreadsheet_control_panel.js:0
#, python-format
msgid "- dates: %s"
msgstr ""

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/bundle/actions/control_panel/spreadsheet_control_panel.js:0
#, python-format
msgid "- numbers: %s"
msgstr ""

#. module: spreadsheet_edition
#: model:ir.model.fields,field_description:spreadsheet_edition.field_spreadsheet_revision__active
msgid "Active"
msgstr "Aktywne"

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/bundle/global_filters/global_filter_side_panel.xml:0
#, python-format
msgid "Add a new filter..."
msgstr "Dodaj nowy filtr..."

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/bundle/global_filters/components/filter_field_offset.js:0
#, python-format
msgid "After next"
msgstr "Po następnym"

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/bundle/list/side_panels/listing_all_side_panel.js:0
#, python-format
msgid "Are you sure you want to delete this list?"
msgstr ""

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/bundle/pivot/side_panels/pivot_list_side_panel.js:0
#, python-format
msgid "Are you sure you want to delete this pivot?"
msgstr ""

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/bundle/global_filters/components/filter_editor/date_filter_editor_side_panel.xml:0
#, python-format
msgid "Automatically filter on the current period"
msgstr "Automatyczne filtrowanie na podstawie bieżącego okresu"

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/bundle/global_filters/components/filter_editor/relation_filter_editor_side_panel.xml:0
#, python-format
msgid "Automatically filter on the current user"
msgstr ""

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/bundle/list/side_panels/listing_all_side_panel.xml:0
#: code:addons/spreadsheet_edition/static/src/bundle/pivot/side_panels/pivot_list_side_panel.xml:0
#: code:addons/spreadsheet_edition/static/src/bundle/version_history/side_panel/version_history_side_panel.xml:0
#, python-format
msgid "Back"
msgstr "Powrót"

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/bundle/global_filters/components/filter_field_offset.js:0
#, python-format
msgid "Before previous"
msgstr "Przed poprzednim"

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/assets/components/spreadsheet_selector_dialog/spreadsheet_selector_panel.xml:0
#, python-format
msgid "Blank spreadsheet"
msgstr "Pusty arkusz"

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/assets/components/spreadsheet_selector_dialog/spreadsheet_selector_dialog.xml:0
#: code:addons/spreadsheet_edition/static/src/bundle/actions/input_dialog/input_dialog.xml:0
#: code:addons/spreadsheet_edition/static/src/bundle/global_filters/components/filter_editor/filter_editor_side_panel.xml:0
#: code:addons/spreadsheet_edition/static/src/bundle/ir_menu_selector/ir_menu_selector.xml:0
#, python-format
msgid "Cancel"
msgstr "Anuluj"

#. module: spreadsheet_edition
#: model:ir.model,name:spreadsheet_edition.model_spreadsheet_revision
msgid "Collaborative spreadsheet revision"
msgstr "Wspólna weryfikacja arkusza kalkulacyjnego"

#. module: spreadsheet_edition
#: model:ir.model.fields,field_description:spreadsheet_edition.field_spreadsheet_revision__commands
msgid "Commands"
msgstr "Polecenia"

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/assets/components/spreadsheet_selector_dialog/spreadsheet_selector_dialog.xml:0
#: code:addons/spreadsheet_edition/static/src/bundle/actions/input_dialog/input_dialog.xml:0
#: code:addons/spreadsheet_edition/static/src/bundle/actions/spreadsheet_component.js:0
#: code:addons/spreadsheet_edition/static/src/bundle/ir_menu_selector/ir_menu_selector.xml:0
#, python-format
msgid "Confirm"
msgstr "Potwierdź"

#. module: spreadsheet_edition
#: model:ir.model.fields,field_description:spreadsheet_edition.field_spreadsheet_revision__create_uid
msgid "Created by"
msgstr "Utworzył(a)"

#. module: spreadsheet_edition
#: model:ir.model.fields,field_description:spreadsheet_edition.field_spreadsheet_revision__create_date
msgid "Created on"
msgstr "Data utworzenia"

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/bundle/chart/side_panels/odoo_line/odoo_line_config_panel.xml:0
#, python-format
msgid "Cumulative data"
msgstr ""

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/bundle/hooks.js:0
#, python-format
msgid "Currency"
msgstr "Waluta"

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/bundle/version_history/side_panel/version_history_item.js:0
#, python-format
msgid "Current Version"
msgstr ""

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/assets/components/spreadsheet_selector_dialog/spreadsheet_selector_dialog.js:0
#, python-format
msgid "Dashboards"
msgstr "Konsole"

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/bundle/global_filters/global_filter_side_panel.xml:0
#, python-format
msgid "Date"
msgstr "Data"

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/bundle/global_filters/components/filter_editor/date_filter_editor_side_panel.xml:0
#, python-format
msgid "Date field"
msgstr "Pole daty"

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/assets/helpers.js:0
#, python-format
msgid "Day"
msgstr "Dzień"

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/bundle/global_filters/components/filter_editor/date_filter_editor_side_panel.xml:0
#: code:addons/spreadsheet_edition/static/src/bundle/global_filters/components/filter_editor/relation_filter_editor_side_panel.xml:0
#: code:addons/spreadsheet_edition/static/src/bundle/global_filters/components/filter_editor/text_filter_editor_side_panel.xml:0
#, python-format
msgid "Default value"
msgstr "Domyślna wartość"

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/bundle/list/side_panels/listing_all_side_panel.xml:0
#: code:addons/spreadsheet_edition/static/src/bundle/pivot/side_panels/pivot_list_side_panel.xml:0
#, python-format
msgid "Delete"
msgstr "Usuń"

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/bundle/actions/control_panel/spreadsheet_control_panel.js:0
#, python-format
msgid ""
"Difference between user locale (%(user_locale)s) and spreadsheet locale "
"(%(spreadsheet_locale)s). This spreadsheet is using the formats below:"
msgstr ""

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/bundle/pivot/side_panels/pivot_details_side_panel.xml:0
#, python-format
msgid "Dimensions"
msgstr "Wymiary"

#. module: spreadsheet_edition
#: model:ir.model.fields,field_description:spreadsheet_edition.field_spreadsheet_revision__display_name
msgid "Display Name"
msgstr "Nazwa wyświetlana"

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/bundle/pivot/pivot_dialog.xml:0
#, python-format
msgid "Display missing cells only"
msgstr "Wyświetl tylko brakujące komórki"

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/bundle/chart/side_panels/common/config_panel.xml:0
#: code:addons/spreadsheet_edition/static/src/bundle/list/side_panels/listing_details_side_panel.xml:0
#: code:addons/spreadsheet_edition/static/src/bundle/pivot/side_panels/pivot_details_side_panel.xml:0
#, python-format
msgid "Domain"
msgstr "Domena"

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/bundle/o_spreadsheet/menu_item_registry.js:0
#, python-format
msgid "Download"
msgstr "Pobierz"

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/bundle/o_spreadsheet/menu_item_registry.js:0
#, python-format
msgid "Download as JSON"
msgstr "Pobierz jako JSON"

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/bundle/global_filters/components/filter_editor/filter_editor_side_panel.js:0
#, python-format
msgid "Duplicated Label"
msgstr "Zduplikowana etykieta"

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/bundle/global_filters/global_filter_side_panel.xml:0
#, python-format
msgid "Edit"
msgstr "Edytuj"

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/bundle/chart/side_panels/common/config_panel.xml:0
#: code:addons/spreadsheet_edition/static/src/bundle/list/side_panels/listing_details_side_panel.xml:0
#: code:addons/spreadsheet_edition/static/src/bundle/pivot/side_panels/pivot_details_side_panel.xml:0
#, python-format
msgid "Edit domain"
msgstr "Edytuj domenę"

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/bundle/global_filters/components/filter_editor/date_filter_editor_side_panel.xml:0
#: code:addons/spreadsheet_edition/static/src/bundle/global_filters/components/filter_editor/relation_filter_editor_side_panel.xml:0
#: code:addons/spreadsheet_edition/static/src/bundle/global_filters/components/filter_editor/text_filter_editor_side_panel.xml:0
#, python-format
msgid "Field matching"
msgstr "Dopasowanie pola"

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/bundle/global_filters/index.js:0
#: code:addons/spreadsheet_edition/static/src/bundle/global_filters/index.js:0
#: code:addons/spreadsheet_edition/static/src/bundle/global_filters/index.js:0
#, python-format
msgid "Filter properties"
msgstr "Filtr właściwości"

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/bundle/global_filters/global_filter_side_panel.xml:0
#: code:addons/spreadsheet_edition/static/src/bundle/global_filters/index.js:0
#, python-format
msgid "Filters"
msgstr "Filtry"

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/bundle/global_filters/components/filter_editor/date_filter_editor_side_panel.js:0
#, python-format
msgid "From / To"
msgstr ""

#. module: spreadsheet_edition
#: model:ir.model.fields,field_description:spreadsheet_edition.field_spreadsheet_revision__id
msgid "ID"
msgstr "ID"

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/assets/graph_view/graph_view.xml:0
#: code:addons/spreadsheet_edition/static/src/assets/graph_view/graph_view.xml:0
#: code:addons/spreadsheet_edition/static/src/bundle/pivot/pivot.xml:0
#: code:addons/spreadsheet_edition/static/src/bundle/pivot/pivot.xml:0
#: code:addons/spreadsheet_edition/static/src/bundle/pivot/pivot.xml:0
#: code:addons/spreadsheet_edition/static/src/bundle/pivot/pivot.xml:0
#, python-format
msgid "Insert in Spreadsheet"
msgstr "Wstaw do arkusza"

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/assets/list_view/insert_list_spreadsheet_menu.xml:0
#, python-format
msgid "Insert list in spreadsheet"
msgstr "Wstaw listę do arkusza kalkulacyjnego"

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/bundle/o_spreadsheet/menu_item_registry.js:0
#, python-format
msgid "Insert pivot"
msgstr ""

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/bundle/o_spreadsheet/menu_item_registry.js:0
#: code:addons/spreadsheet_edition/static/src/bundle/pivot/pivot_actions.js:0
#, python-format
msgid "Insert pivot cell"
msgstr "Wstaw komórkę przestawną"

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/assets/components/spreadsheet_selector_dialog/spreadsheet_selector_dialog.xml:0
#, python-format
msgid "Insert the first"
msgstr "Wstaw pierwszy"

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/bundle/global_filters/components/filter_editor/filter_editor_side_panel.xml:0
#, python-format
msgid "Label"
msgstr "Etykieta"

#. module: spreadsheet_edition
#: model:ir.model.fields,field_description:spreadsheet_edition.field_spreadsheet_revision__write_uid
msgid "Last Updated by"
msgstr "Ostatnio aktualizowane przez"

#. module: spreadsheet_edition
#: model:ir.model.fields,field_description:spreadsheet_edition.field_spreadsheet_revision__write_date
msgid "Last Updated on"
msgstr "Data ostatniej aktualizacji"

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/bundle/chart/side_panels/common/config_panel.xml:0
#: code:addons/spreadsheet_edition/static/src/bundle/list/side_panels/listing_details_side_panel.xml:0
#: code:addons/spreadsheet_edition/static/src/bundle/pivot/side_panels/pivot_details_side_panel.xml:0
#, python-format
msgid "Last updated at"
msgstr "Ostatnia aktualizacja w"

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/bundle/ir_ui_menu/index.js:0
#, python-format
msgid "Link an Odoo menu"
msgstr "Łączenie menu Odoo"

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/assets/insert_action_link_menu/insert_action_link_menu.xml:0
#, python-format
msgid "Link menu in spreadsheet"
msgstr "Menu linków w arkuszu kalkulacyjnym"

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/bundle/chart/odoo_menu/chart_panel.xml:0
#, python-format
msgid "Link to Odoo menu"
msgstr "Link do menu Odoo"

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/bundle/list/side_panels/listing_details_side_panel.xml:0
#, python-format
msgid "List Name"
msgstr "Nazwa listy"

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/bundle/list/index.js:0
#, python-format
msgid "List properties"
msgstr "Lista właściwości"

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/bundle/version_history/side_panel/version_history_side_panel.xml:0
#, python-format
msgid "Load More"
msgstr "Załaduj więcej"

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/bundle/o_spreadsheet/menu_item_registry.js:0
#: code:addons/spreadsheet_edition/static/src/bundle/version_history/side_panel/version_history_item.js:0
#, python-format
msgid "Make a copy"
msgstr "Utwórz kopię"

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/bundle/global_filters/components/filter_editor/filter_editor_side_panel.xml:0
#, python-format
msgid "Match this filter to a field for each data source"
msgstr "Dopasuj ten filtr do pola dla każdego źródła danych"

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/bundle/pivot/side_panels/pivot_details_side_panel.xml:0
#, python-format
msgid "Measures"
msgstr "Miary"

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/bundle/ir_menu_selector/ir_menu_selector.js:0
#, python-format
msgid "Menu Items"
msgstr "Elementy menu"

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/bundle/list/plugins/list_autofill_plugin.js:0
#, python-format
msgid "Missing list #%s"
msgstr ""

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/bundle/pivot/plugins/pivot_autofill_plugin.js:0
#, python-format
msgid "Missing pivot"
msgstr ""

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/bundle/pivot/plugins/pivot_autofill_plugin.js:0
#, python-format
msgid "Missing pivot #%s"
msgstr ""

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/bundle/chart/side_panels/common/config_panel.xml:0
#: code:addons/spreadsheet_edition/static/src/bundle/list/side_panels/listing_details_side_panel.xml:0
#: code:addons/spreadsheet_edition/static/src/bundle/pivot/side_panels/pivot_details_side_panel.xml:0
#: model:ir.model.fields,field_description:spreadsheet_edition.field_spreadsheet_revision__res_model
#, python-format
msgid "Model"
msgstr "Model"

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/assets/helpers.js:0
#: code:addons/spreadsheet_edition/static/src/bundle/global_filters/components/filter_editor/date_filter_editor_side_panel.xml:0
#, python-format
msgid "Month"
msgstr "Miesiąc"

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/bundle/global_filters/components/filter_editor/date_filter_editor_side_panel.js:0
#, python-format
msgid "Month / Quarter"
msgstr ""

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/assets/components/spreadsheet_selector_dialog/spreadsheet_selector_dialog.js:0
#, python-format
msgid "Name of the %s:"
msgstr "Nazwa %s:"

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/bundle/version_history/side_panel/version_history_item.js:0
#, python-format
msgid "Name this version"
msgstr ""

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/bundle/o_spreadsheet/menu_item_registry.js:0
#, python-format
msgid "New"
msgstr "Nowe"

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/bundle/global_filters/components/filter_editor/filter_editor_side_panel.js:0
#, python-format
msgid "New %s filter"
msgstr "Nowy filtr %s"

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/assets/components/spreadsheet_selector_dialog/spreadsheet_selector_panel.js:0
#, python-format
msgid "New sheet inserted in '%s'"
msgstr "Nowy arkusz wstawiony w '%s'"

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/bundle/global_filters/components/filter_field_offset.js:0
#, python-format
msgid "Next"
msgstr "Następny"

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/assets/components/spreadsheet_selector_dialog/spreadsheet_selector_panel.xml:0
#, python-format
msgid "No preview"
msgstr "Brak podglądu"

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/bundle/actions/input_dialog/input_dialog.js:0
#: code:addons/spreadsheet_edition/static/src/bundle/actions/spreadsheet_component.js:0
#: code:addons/spreadsheet_edition/static/src/bundle/actions/spreadsheet_component.js:0
#: code:addons/spreadsheet_edition/static/src/bundle/actions/version_history/version_history_action.js:0
#: code:addons/spreadsheet_edition/static/src/bundle/actions/version_history/version_history_action.js:0
#, python-format
msgid "Odoo Spreadsheet"
msgstr ""

#. module: spreadsheet_edition
#: model:ir.model.fields,field_description:spreadsheet_edition.field_spreadsheet_revision__parent_revision_id
msgid "Parent Revision"
msgstr "Rewizja nadrzędna"

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/bundle/global_filters/components/filter_field_offset.xml:0
#, python-format
msgid "Period offset"
msgstr "Przesunięcie okresu"

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/bundle/global_filters/components/filter_field_offset.js:0
#, python-format
msgid "Period offset applied to this source"
msgstr "Przesunięcie okresu zastosowane do tego źródła"

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/assets/pivot_view/pivot_view.js:0
#, python-format
msgid "Pivot contains duplicate groupbys"
msgstr "Pivot zawiera zduplikowane grupy"

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/bundle/pivot/side_panels/pivot_details_side_panel.xml:0
#, python-format
msgid "Pivot name"
msgstr "Nazwa przestawna"

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/bundle/pivot/index.js:0
#, python-format
msgid "Pivot properties"
msgstr "Właściwości pivot"

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/bundle/global_filters/components/filter_field_offset.js:0
#, python-format
msgid "Previous"
msgstr "Poprzedni"

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/bundle/o_spreadsheet/menu_item_registry.js:0
#, python-format
msgid "Print"
msgstr "Drukuj"

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/assets/helpers.js:0
#: code:addons/spreadsheet_edition/static/src/bundle/global_filters/components/filter_editor/date_filter_editor_side_panel.xml:0
#, python-format
msgid "Quarter"
msgstr "Kwartał"

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/bundle/actions/spreadsheet_component.js:0
#: code:addons/spreadsheet_edition/static/src/bundle/o_spreadsheet/menu_item_registry.js:0
#, python-format
msgid "Re-insert list"
msgstr "Wstaw ponownie listę"

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/bundle/o_spreadsheet/menu_item_registry.js:0
#, python-format
msgid "Re-insert pivot"
msgstr "Ponownie ustaw pivot"

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/bundle/list/plugins/list_autofill_plugin.js:0
#, python-format
msgid "Record #"
msgstr "Rekord #"

#. module: spreadsheet_edition
#: model:ir.model.fields,field_description:spreadsheet_edition.field_spreadsheet_revision__res_id
msgid "Record id"
msgstr "ID rekordu"

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/bundle/o_spreadsheet/menu_item_registry.js:0
#, python-format
msgid "Refresh all data"
msgstr "Odśwież wszystkie dane"

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/bundle/chart/side_panels/common/config_panel.xml:0
#: code:addons/spreadsheet_edition/static/src/bundle/list/side_panels/listing_details_side_panel.xml:0
#: code:addons/spreadsheet_edition/static/src/bundle/pivot/side_panels/pivot_details_side_panel.xml:0
#, python-format
msgid "Refresh values"
msgstr "Odświeżanie wartości"

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/bundle/global_filters/components/filter_editor/relation_filter_editor_side_panel.xml:0
#, python-format
msgid "Related model"
msgstr "Powiązany model"

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/bundle/global_filters/global_filter_side_panel.xml:0
#, python-format
msgid "Relation"
msgstr "Relacja"

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/bundle/global_filters/components/filter_editor/date_filter_editor_side_panel.js:0
#, python-format
msgid "Relative Period"
msgstr "Okres względny"

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/bundle/global_filters/components/filter_editor/filter_editor_side_panel.xml:0
#, python-format
msgid "Remove"
msgstr "Usuń"

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/bundle/actions/control_panel/spreadsheet_name.xml:0
#: code:addons/spreadsheet_edition/static/src/bundle/o_spreadsheet/editable_name/editable_name.xml:0
#: code:addons/spreadsheet_edition/static/src/bundle/version_history/side_panel/version_history_item.js:0
#, python-format
msgid "Rename"
msgstr "Zmień nazwę"

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/bundle/global_filters/components/filter_field_offset.js:0
#, python-format
msgid "Requires a selected field"
msgstr "Wymaga wybranego pola"

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/bundle/global_filters/components/filter_editor/text_filter_editor_side_panel.xml:0
#, python-format
msgid "Restrict values to a range"
msgstr ""

#. module: spreadsheet_edition
#: model:ir.model.fields,field_description:spreadsheet_edition.field_spreadsheet_revision__revision_id
msgid "Revision"
msgstr "Rewizja"

#. module: spreadsheet_edition
#: model:ir.model.fields,field_description:spreadsheet_edition.field_spreadsheet_revision__name
msgid "Revision name"
msgstr ""

#. module: spreadsheet_edition
#: model:ir.actions.act_window,name:spreadsheet_edition.spreadsheet_revision_action
#: model:ir.ui.menu,name:spreadsheet_edition.menu_technical_spreadsheet_revision
msgid "Revisions"
msgstr "Rewizje"

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/bundle/global_filters/components/filter_editor/filter_editor_side_panel.xml:0
#: code:addons/spreadsheet_edition/static/src/bundle/o_spreadsheet/editable_name/editable_name.xml:0
#, python-format
msgid "Save"
msgstr "Zapisz"

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/bundle/o_spreadsheet/menu_item_registry.js:0
#, python-format
msgid "Save as template"
msgstr "Zapisz jako szablon"

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/bundle/actions/control_panel/spreadsheet_breadcrumbs.xml:0
#, python-format
msgid "Saved"
msgstr "Zapisane"

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/bundle/actions/control_panel/spreadsheet_breadcrumbs.xml:0
#, python-format
msgid "Saving"
msgstr "Zapisywanie"

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/assets/components/spreadsheet_selector_dialog/spreadsheet_selector_panel.xml:0
#: code:addons/spreadsheet_edition/static/src/assets/components/spreadsheet_selector_dialog/spreadsheet_selector_panel.xml:0
#: code:addons/spreadsheet_edition/static/src/assets/components/spreadsheet_selector_dialog/spreadsheet_selector_panel.xml:0
#, python-format
msgid "Search..."
msgstr "Szukaj..."

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/bundle/list/index.js:0
#, python-format
msgid "See list properties"
msgstr "Zobacz listę właściwości"

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/bundle/pivot/index.js:0
#, python-format
msgid "See pivot properties"
msgstr "Zobacz właściwości pivot"

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/bundle/version_history/index.js:0
#, python-format
msgid "See version history"
msgstr ""

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/bundle/ir_menu_selector/ir_menu_selector.js:0
#, python-format
msgid "Select a menu..."
msgstr "Wybierz menu..."

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/assets/components/spreadsheet_selector_dialog/spreadsheet_selector_dialog.js:0
#, python-format
msgid "Select a spreadsheet to insert your %s."
msgstr "Wybierz arkusz kalkulacyjny do wstawienia %s."

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/bundle/ir_menu_selector/ir_menu_selector.js:0
#, python-format
msgid "Select an Odoo menu to link in your spreadsheet"
msgstr ""
"Wybierz menu Odoo, które chcesz połączyć w swoim arkuszu kalkulacyjnym"

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/bundle/actions/spreadsheet_component.js:0
#, python-format
msgid "Select the number of records to insert"
msgstr "Wybierz liczbę rekordów do wstawienia"

#. module: spreadsheet_edition
#: model:ir.model.fields,field_description:spreadsheet_edition.field_spreadsheet_dashboard__server_revision_id
#: model:ir.model.fields,field_description:spreadsheet_edition.field_spreadsheet_dashboard_share__server_revision_id
#: model:ir.model.fields,field_description:spreadsheet_edition.field_spreadsheet_mixin__server_revision_id
msgid "Server Revision"
msgstr ""

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/bundle/global_filters/index.js:0
#, python-format
msgid "Set as filter"
msgstr "Ustaw jako filtr"

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/bundle/o_spreadsheet/menu_item_registry.js:0
#, python-format
msgid "Snapshot"
msgstr ""

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/bundle/global_filters/components/filter_editor/filter_editor_side_panel.js:0
#, python-format
msgid "Some required fields are not valid"
msgstr "Niektóre wymagane pola są nieprawidłowe"

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/bundle/list/side_panels/listing_details_side_panel.xml:0
#: code:addons/spreadsheet_edition/static/src/bundle/pivot/side_panels/pivot_details_side_panel.xml:0
#, python-format
msgid "Sorting"
msgstr "Sortowanie"

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/assets/spreadsheet_cog_menu/spreadsheet_cog_menu.xml:0
#: model:ir.ui.menu,name:spreadsheet_edition.menu_technical_spreadsheet
#, python-format
msgid "Spreadsheet"
msgstr "Arkusz kalkulacyjny"

#. module: spreadsheet_edition
#: model:ir.model.fields,field_description:spreadsheet_edition.field_spreadsheet_dashboard__spreadsheet_revision_ids
#: model:ir.model.fields,field_description:spreadsheet_edition.field_spreadsheet_dashboard_share__spreadsheet_revision_ids
#: model:ir.model.fields,field_description:spreadsheet_edition.field_spreadsheet_mixin__spreadsheet_revision_ids
msgid "Spreadsheet Revision"
msgstr "Rewizja arkusza"

#. module: spreadsheet_edition
#: model:ir.model.fields,field_description:spreadsheet_edition.field_spreadsheet_dashboard__spreadsheet_snapshot
#: model:ir.model.fields,field_description:spreadsheet_edition.field_spreadsheet_dashboard_share__spreadsheet_snapshot
#: model:ir.model.fields,field_description:spreadsheet_edition.field_spreadsheet_mixin__spreadsheet_snapshot
msgid "Spreadsheet Snapshot"
msgstr "Migawka arkusza"

#. module: spreadsheet_edition
#: model:ir.model,name:spreadsheet_edition.model_spreadsheet_mixin
msgid "Spreadsheet mixin"
msgstr ""

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/assets/components/spreadsheet_selector_dialog/spreadsheet_selector_dialog.js:0
#, python-format
msgid "Spreadsheets"
msgstr "Arkusze kalkulacyjne"

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/bundle/chart/side_panels/odoo_bar/odoo_bar_config_panel.xml:0
#, python-format
msgid "Stacked barchart"
msgstr "Wykres słupkowy skumulowany"

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/bundle/chart/side_panels/odoo_line/odoo_line_config_panel.xml:0
#, python-format
msgid "Stacked linechart"
msgstr "Wykres liniowy skumulowany"

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/bundle/global_filters/global_filter_side_panel.xml:0
#, python-format
msgid "Text"
msgstr "Tekst"

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/bundle/actions/version_history/version_history_action.js:0
#, python-format
msgid ""
"The history of your spreadsheet is corrupted and you are likely missing "
"recent revisions. This feature cannot be used."
msgstr ""

#. module: spreadsheet_edition
#. odoo-python
#: code:addons/spreadsheet_edition/models/spreadsheet_mixin.py:0
#, python-format
msgid ""
"The operation could not be applied because of a concurrent update. Please "
"try again."
msgstr ""

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/bundle/actions/version_history/version_history_action.js:0
#, python-format
msgid ""
"There are missing revisions that prevent to restore the whole edition history.\n"
"\n"
"Would you like to load the more recent modifications?"
msgstr ""

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/bundle/version_history/side_panel/version_history_side_panel.xml:0
#, python-format
msgid "There are no prior revisions for this spreadsheet."
msgstr ""

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/bundle/global_filters/components/filter_editor/date_filter_editor_side_panel.xml:0
#, python-format
msgid "Time range"
msgstr "Zakres czasu"

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/bundle/pivot/plugins/pivot_autofill_plugin.js:0
#: code:addons/spreadsheet_edition/static/src/bundle/pivot/plugins/pivot_autofill_plugin.js:0
#: code:addons/spreadsheet_edition/static/src/bundle/pivot/spreadsheet_pivot_dialog.js:0
#, python-format
msgid "Total"
msgstr "Suma"

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/bundle/version_history/index.js:0
#, python-format
msgid "Version History"
msgstr ""

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/assets/helpers.js:0
#, python-format
msgid "Week"
msgstr "Tydzień"

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/assets/helpers.js:0
#: code:addons/spreadsheet_edition/static/src/bundle/global_filters/components/filter_editor/date_filter_editor_side_panel.xml:0
#, python-format
msgid "Year"
msgstr "Rok"

#. module: spreadsheet_edition
#. odoo-python
#: code:addons/spreadsheet_edition/models/spreadsheet_mixin.py:0
#, python-format
msgid "You are not allowed to access this spreadsheet."
msgstr ""

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/bundle/pivot/side_panels/pivot_details_side_panel.js:0
#, python-format
msgid "ascending"
msgstr "rosnąco"

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/bundle/pivot/side_panels/pivot_details_side_panel.js:0
#, python-format
msgid "descending"
msgstr "malejąco"

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/assets/components/spreadsheet_selector_dialog/spreadsheet_selector_dialog.js:0
#, python-format
msgid "graph"
msgstr "wykres"

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/bundle/pivot/pivot_dialog.xml:0
#, python-format
msgid "has no cell missing from this sheet"
msgstr "nie ma żadnej brakującej komórki w tym arkuszu"

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/assets/components/spreadsheet_selector_dialog/spreadsheet_selector_dialog.js:0
#, python-format
msgid "link"
msgstr "link"

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/assets/components/spreadsheet_selector_dialog/spreadsheet_selector_dialog.js:0
#, python-format
msgid "list"
msgstr "lista"

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/bundle/chart/side_panels/common/config_panel.js:0
#: code:addons/spreadsheet_edition/static/src/bundle/list/side_panels/listing_details_side_panel.js:0
#: code:addons/spreadsheet_edition/static/src/bundle/pivot/side_panels/pivot_details_side_panel.js:0
#, python-format
msgid "never"
msgstr "nigdy"

#. module: spreadsheet_edition
#: model:ir.model.constraint,message:spreadsheet_edition.constraint_spreadsheet_revision_parent_revision_unique
msgid "o-spreadsheet revision refused due to concurrency"
msgstr "o-wersja arkusza kalkulacyjnego odrzucona z powodu współbieżności"

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/assets/components/spreadsheet_selector_dialog/spreadsheet_selector_dialog.js:0
#, python-format
msgid "pivot"
msgstr "pivot"

#. module: spreadsheet_edition
#. odoo-javascript
#: code:addons/spreadsheet_edition/static/src/assets/components/spreadsheet_selector_dialog/spreadsheet_selector_dialog.xml:0
#, python-format
msgid "records of the list."
msgstr "rekordy listy."

#. module: spreadsheet_edition
#: model:ir.model,name:spreadsheet_edition.model_ir_websocket
msgid "websocket message handling"
msgstr "obsługa komunikatów websocket"
