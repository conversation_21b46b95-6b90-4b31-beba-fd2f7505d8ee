from typing import List, Dict, Any
from odoo import models, api, _
from odoo.exceptions import ValidationError


class AccountMove(models.Model):
    _inherit = 'account.move'

    @api.model_create_multi
    def create(self, vals_list):
        """Create account moves and handle consumable products."""
        moves = super().create(vals_list)
        self._process_accruals(moves)
        return moves

    def _process_accruals(self, moves):
        """
        Process accrual entries for consumable and service products.
        Args:
            moves: Newly created account moves
        """
        if not self._context.get('close_to_accrual'):
            return

        for move in moves:
            consumable_debit_lines = move._get_consumable_debit_lines()
            service_debit_lines = move._get_service_debit_lines()
            move._update_accrual_accounts(consumable_debit_lines)
            move._update_accrual_accounts(service_debit_lines)

    def _get_consumable_debit_lines(self):
        """Get move lines for consumable products with debit amounts."""
        return self.line_ids.filtered(
            lambda line: line.product_id.type == 'consu' and line.debit
        )

    def _get_service_debit_lines(self):
        """Get move lines for consumable products with debit amounts."""
        return self.line_ids.filtered(
            lambda line: line.product_id.type == 'service' and line.debit
        )

    def _update_accrual_accounts(self, debit_lines):
        """
        Update account IDs for consumable product lines.
        Args:
            debit_lines: Move lines to update
        """
        for line in debit_lines:
            accrual_account = line.product_id.categ_id.property_account_purchase_accrual_id
            if not accrual_account:
                continue
            line.account_id = accrual_account.id
