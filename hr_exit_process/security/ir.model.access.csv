id,name,model_id:id,group_id:id,perm_read,perm_write,perm_create,perm_unlink
hr_exit_employee,hr.exit,model_hr_exit,base.group_user,1,1,1,1
hr_exit_employee_line,hr.exit.line,model_hr_exit_line,base.group_user,1,1,1,0
hr_exit_checklist,hr.exit.checklist,model_hr_exit_checklist,base.group_user,1,0,0,0
hr_exit_checklist_line,hr.exit.checklist.line,model_hr_exit_checklist_line,base.group_user,1,0,0,0
hr_exit_checklist_officer,hr.exit.checklist.officer,model_hr_exit_checklist,hr.group_hr_user,1,1,1,1
hr_exit_checklist_line_officer,hr.exit.checklist.line.officer,model_hr_exit_checklist_line,hr.group_hr_user,1,1,1,1
hr_contract_employee,hr.contract,hr_contract.model_hr_contract,base.group_user,1,0,0,0
hr_exit_department,hr.exit.department,model_hr_exit,hr_exit_process.group_department_manager_for_exit,1,1,1,1
hr_exit_line_department,hr.exit.line.department,model_hr_exit_line,hr_exit_process.group_department_manager_for_exit,1,1,1,1
hr_exit_checklist_department,hr.exit.checklist.department,model_hr_exit_checklist,hr_exit_process.group_department_manager_for_exit,1,1,1,1
hr_exit_checklist_line_department,hr.exit.checklist.line.department,model_hr_exit_checklist_line,hr_exit_process.group_department_manager_for_exit,1,1,1,1
hr_exit_genaral,hr.exit.genaral,model_hr_exit,hr_exit_process.group_genaral_manager_for_exit,1,1,1,1
hr_exit_line_genaral,hr.exit.line.genaral,model_hr_exit_line,hr_exit_process.group_genaral_manager_for_exit,1,1,1,1
hr_exit_checklist_genaral,hr.exit.checklist.genaral,model_hr_exit_checklist,hr_exit_process.group_genaral_manager_for_exit,1,1,1,1
hr_exit_checklist_line_genaral,hr.exit.checklist.line.genaral,model_hr_exit_checklist_line,hr_exit_process.group_genaral_manager_for_exit,1,1,1,1


