/* Regular */
@font-face {
    font-family: 'Open Sans';
    
    src: url('../fonts/OpenSans-Regular-webfont.eot');
    src: url('../fonts/OpenSans-Regular-webfont.eot%3F') format('embedded-opentype'),
         url('../fonts/OpenSans-Regular-webfont.woff') format('woff'),
         url('../fonts/OpenSans-Regular-webfont.ttf') format('truetype'),
         url('../fonts/OpenSans-Regular-webfont.svg') format('svg');
    font-weight: normal;
    font-weight: 400;
    font-style: normal;

}

/* Italic */
@font-face {
    font-family: 'Open Sans';
    src: url('../fonts/OpenSans-Italic-webfont.eot');
    src: url('../fonts/OpenSans-Italic-webfont.eot%3F') format('embedded-opentype'),
         url('../fonts/OpenSans-Italic-webfont.woff') format('woff'),
         url('../fonts/OpenSans-Italic-webfont.ttf') format('truetype'),
         url('../fonts/OpenSans-Italic-webfont.svg') format('svg');
    font-weight: normal;
    font-weight: 400;
    font-style: italic;

}

/* Light */
@font-face {
    font-family: 'Open Sans';
    src: url('../fonts/OpenSans-Light-webfont.eot');
    src: url('../fonts/OpenSans-Light-webfont.eot%3F') format('embedded-opentype'),
         url('../fonts/OpenSans-Light-webfont.woff') format('woff'),
         url('../fonts/OpenSans-Light-webfont.ttf') format('truetype'),
         url('../fonts/OpenSans-Light-webfont.svg') format('svg');
    font-weight: 200;
    font-style: normal;

}

/* Light Italic */
@font-face {
    font-family: 'Open Sans';
    src: url('../fonts/OpenSans-LightItalic-webfont.eot');
    src: url('../fonts/OpenSans-LightItalic-webfont.eot%3F') format('embedded-opentype'),
         url('../fonts/OpenSans-LightItalic-webfont.woff') format('woff'),
         url('../fonts/OpenSans-LightItalic-webfont.ttf') format('truetype'),
         url('../fonts/OpenSans-LightItalic-webfont.svg') format('svg');
    font-weight: 200;
    font-style: italic;

}

/* Semibold */
@font-face {
    font-family: 'Open Sans';
    src: url('../fonts/OpenSans-Semibold-webfont.eot');
    src: url('../fonts/OpenSans-Semibold-webfont.eot%3F') format('embedded-opentype'),
         url('../fonts/OpenSans-Semibold-webfont.woff') format('woff'),
         url('../fonts/OpenSans-Semibold-webfont.ttf') format('truetype'),
         url('../fonts/OpenSans-Semibold-webfont.svg') format('svg');
    font-weight: 500;
    font-style: normal;

}

/* Semibold Italic */
@font-face {
    font-family: 'Open Sans';
    src: url('../fonts/OpenSans-SemiboldItalic-webfont.eot');
    src: url('../fonts/OpenSans-SemiboldItalic-webfont.eot%3F') format('embedded-opentype'),
         url('../fonts/OpenSans-SemiboldItalic-webfont.woff') format('woff'),
         url('../fonts/OpenSans-SemiboldItalic-webfont.ttf') format('truetype'),
         url('../fonts/OpenSans-SemiboldItalic-webfont.svg') format('svg');	 
    font-weight: 500;
    font-style: italic;
}

/* Semibold */
@font-face {
    font-family: 'Open Sans';
    src: url('../fonts/OpenSans-Semibold-webfont.eot');
    src: url('../fonts/OpenSans-Semibold-webfont.eot%3F') format('embedded-opentype'),
         url('../fonts/OpenSans-Semibold-webfont.woff') format('woff'),
         url('../fonts/OpenSans-Semibold-webfont.ttf') format('truetype'),
         url('../fonts/OpenSans-Semibold-webfont.svg') format('svg');
    font-weight: bold;
    font-style: normal;

}


/* Bold */
/*
@font-face {
    font-family: 'Open Sans';
    src: url('../fonts/OpenSans-Bold-webfont.eot');
    src: url('../fonts/OpenSans-Bold-webfont.eot?#iefix') format('embedded-opentype'),
         url('../fonts/OpenSans-Bold-webfont.woff') format('woff'),
         url('../fonts/OpenSans-Bold-webfont.ttf') format('truetype'),
         url('../fonts/OpenSans-Bold-webfont.svg#OpenSansBold') format('svg');
    font-weight: bolder;
    font-weight: 700;
    font-style: normal;

}
*/

/* Bold Italic */
/*
@font-face {
    font-family: 'Open Sans';
    src: url('../fonts/OpenSans-BoldItalic-webfont.eot');
    src: url('../fonts/OpenSans-BoldItalic-webfont.eot?#iefix') format('embedded-opentype'),
         url('../fonts/OpenSans-BoldItalic-webfont.woff') format('woff'),
         url('../fonts/OpenSans-BoldItalic-webfont.ttf') format('truetype'),
         url('../fonts/OpenSans-BoldItalic-webfont.svg#OpenSansBoldItalic') format('svg');
    font-weight: bolder;
    font-weight: 700;
    font-style: italic;

}
*/

/* Extra Bold */
/*
@font-face {
    font-family: 'Open Sans';
    src: url('../fonts/OpenSans-ExtraBold-webfont.eot');
    src: url('../fonts/OpenSans-ExtraBold-webfont.eot?#iefix') format('embedded-opentype'),
         url('../fonts/OpenSans-ExtraBold-webfont.woff') format('woff'),
         url('../fonts/OpenSans-ExtraBold-webfont.ttf') format('truetype'),
         url('../fonts/OpenSans-ExtraBold-webfont.svg#OpenSansExtrabold') format('svg');
    font-weight: 900;
    font-style: normal;

}
*/

/* Extra Bold Italic */
/*
@font-face {
    font-family: 'Open Sans';
    src: url('../fonts/OpenSans-ExtraBoldItalic-webfont.eot');
    src: url('../fonts/OpenSans-ExtraBoldItalic-webfont.eot?#iefix') format('embedded-opentype'),
         url('../fonts/OpenSans-ExtraBoldItalic-webfont.woff') format('woff'),
         url('../fonts/OpenSans-ExtraBoldItalic-webfont.ttf') format('truetype'),
         url('../fonts/OpenSans-ExtraBoldItalic-webfont.svg#OpenSansExtraboldItalic') format('svg');
    font-weight: 900;
    font-style: italic;

}
*/



@font-face{
	font-family:'Glyphicons Halflings';
	src:url('../fonts/glyphicons-halflings-regular.eot');
	src:url('../fonts/glyphicons-halflings-regular.eot%3F') format('embedded-opentype'),
		url('../fonts/glyphicons-halflings-regular.woff') format('woff'),
		url('../fonts/glyphicons-halflings-regular.ttf') format('truetype'),
		url('../fonts/glyphicons-halflings-regular.svg') format('svg');
}
@font-face {
  font-family: 'FontAwesome';
  src: url('../fonts/fontawesome-webfont.eot%3Fv=4.0.3');
  src: url('../fonts/fontawesome-webfont.eot%3F') format('embedded-opentype'), url('../fonts/fontawesome-webfont.woff%3Fv=4.0.3') format('woff'), url('../fonts/fontawesome-webfont.ttf%3Fv=4.0.3') format('truetype'), url('../fonts/fontawesome-webfont.svg%3Fv=4.0.3') format('svg');
  font-weight: normal;
  font-style: normal;
}
@font-face {
	font-family: 'entypo';
	src:url('../fonts/entypo.eot');
	src:url('../fonts/entypo.eot%3F') format('embedded-opentype'),
		url('../fonts/entypo.ttf') format('truetype'),
		url('../fonts/entypo.woff') format('woff'),
		url('../fonts/entypo.svg') format('svg');
	font-weight: normal;
	font-style: normal;
}