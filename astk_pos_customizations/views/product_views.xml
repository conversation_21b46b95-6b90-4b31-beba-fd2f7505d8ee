<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="product_product_ext_form_view2" model="ir.ui.view">
        <field name="name">product_extended.product.form.view</field>
        <field name="model">product.template</field>
        <field name="priority">3</field>
        <field name="inherit_id" ref="product.product_template_form_view"/>
        <field name="arch" type="xml">
            <xpath expr="//page[@name='general_information']//group[@name='group_standard_price']//field[@name='product_tag_ids']"
                   position="after">
                <field name="gender"/>
                <field name="season_id"/>
                <field name="seasonality_id"/>
                <field name="fabric_type_id"/>
                <field name="fabric_composition_id"/>
                <field name="fabric_dye_finish_id"/>
                <field name="product_fit_id"/>
                <field name="collection_classification_id"/>
                <field name="collection_type_id"/>
            </xpath>

        </field>
    </record>
    <record id="product_tree_inherit" model="ir.ui.view">
        <field name="name">product product tree inherit</field>
        <field name="model">product.product</field>
        <field name="inherit_id" ref="product.product_product_tree_view"/>
        <field name="arch" type="xml">

            <xpath expr="//field[@name='lst_price']" position="before">
                <!-- Add new fields here -->
                <field name="season_id" optional="show"/>
                <field name="gender" optional="show"/>
            </xpath>

            <xpath expr="//field[@name='standard_price']" position="attributes">
                <!-- Add new fields here -->
                <attribute name="groups">astk_pos_customizations.group_pos_show_margin_cost</attribute>
            </xpath>

        </field>
    </record>
    <record id="product_template_tree_inherit" model="ir.ui.view">
        <field name="name">product template tree inherit</field>
        <field name="model">product.template</field>
        <field name="inherit_id" ref="product.product_template_tree_view"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='standard_price']" position="attributes">
                <!-- Add new fields here -->
                <attribute name="groups">astk_pos_customizations.group_pos_show_margin_cost</attribute>
            </xpath>

        </field>
    </record>

    <record id="product_form_view_sale_order_inherit" model="ir.ui.view">
        <field name="name">product.product.sale.order</field>
        <field name="model">product.product</field>
        <field name="inherit_id" ref="product.product_normal_form_view"/>
        <field name="arch" type="xml">
            <xpath expr="//page[@name='general_information']//group[@name='group_standard_price']//field[@name='additional_product_tag_ids']"
                   position="before">
                <field name="egs"/>
            </xpath>
            <xpath expr="//field[@name='standard_price']" position="attributes">
                <attribute name="groups">astk_pos_customizations.group_pos_show_margin_cost</attribute>
            </xpath>
        </field>
    </record>

    <record id="product_template_form_inherit" model="ir.ui.view">
        <field name="name">product.template.form.inherit</field>
        <field name="model">product.template</field>
        <field name="inherit_id" ref="product.product_template_only_form_view"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='standard_price']" position="attributes">
                <attribute name="groups">astk_pos_customizations.group_pos_show_margin_cost</attribute>
            </xpath>
        </field>
    </record>
     <record id="point_of_sale.menu_pos_products" model="ir.ui.menu">
        <field name="groups_id" eval="[(5,0),(4, ref('astk_pos_customizations.group_pos_show_margin_cost'))]"/>
    </record>
    <record id="point_of_sale.pos_config_menu_action_product_product" model="ir.ui.menu">
        <field name="groups_id" eval="[(5,0),(4, ref('astk_pos_customizations.group_pos_show_margin_cost'))]"/>
    </record>
    <record id="stock.menu_product_variant_config_stock" model="ir.ui.menu">
        <field name="groups_id" eval="[(5,0),(4, ref('astk_pos_customizations.group_pos_show_margin_cost'))]"/>
    </record>
    <record id="stock.product_product_menu" model="ir.ui.menu">
        <field name="groups_id" eval="[(5,0),(4, ref('astk_pos_customizations.group_pos_show_margin_cost'))]"/>
    </record>
</odoo>