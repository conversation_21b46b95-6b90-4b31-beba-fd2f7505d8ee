from odoo import models, fields, api


class MaintenanceSpareParts(models.Model):
    _name = 'maintenance.spare.parts'
    _description = 'Maintenance Spare Parts'

    maintenance_id = fields.Many2one('maintenance.request', string='Maintenance Request', ondelete='cascade')
    product_id = fields.Many2one('product.product', string='Product', required=True)
    quantity = fields.Float(string='Quantity', default=1.0, digits='Product Unit of Measure', required=True)
    uom_id = fields.Many2one('uom.uom', string='Unit of Measure', required=True, 
                             domain="[('category_id', '=', product_uom_category_id)]")
    product_uom_category_id = fields.Many2one(related='product_id.uom_id.category_id')

    # Related fields for analysis
    maintenance_name = fields.Char(related='maintenance_id.name', string='Maintenance Request', store=True)
    maintenance_date = fields.Date(related='maintenance_id.request_date', string='Request Date', store=True)
    maintenance_type = fields.Selection(related='maintenance_id.maintenance_type', string='Maintenance Type', store=True)
    equipment_id = fields.Many2one(related='maintenance_id.equipment_id', string='Equipment', store=True)
    user_id = fields.Many2one(related='maintenance_id.user_id', string='Technician', store=True)
    stage_id = fields.Many2one(related='maintenance_id.stage_id', string='Stage', store=True)
    maintenance_team_id = fields.Many2one(related='maintenance_id.maintenance_team_id', string='Team', store=True)
    product_name = fields.Char(related='product_id.name', string='Product Name', store=True)
    product_default_code = fields.Char(related='product_id.default_code', string='Product Code', store=True)
    product_categ_id = fields.Many2one(related='product_id.categ_id', string='Product Category', store=True)

    @api.onchange('product_id')
    def _onchange_product_id(self):
        if self.product_id:
            self.uom_id = self.product_id.uom_id.id

